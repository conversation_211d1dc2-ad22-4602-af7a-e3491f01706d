// This file configures the initialization of Bugsnag for edge features (middleware, edge routes, and so on).
// The config you add here will be used whenever one of the edge features is loaded.
// Note that this config is unrelated to the Vercel Edge Runtime and is also required when running locally.
// https://docs.bugsnag.com/platforms/javascript/

import Bugsnag from '@bugsnag/js';

// Only initialize Bugsnag in production environment
// if (process.env.NODE_ENV === 'production' && process.env.BUGSNAG_API_KEY) {
  Bugsnag.start({
    apiKey: process.env.BUGSNAG_API_KEY,

    // Configure which release stages to report errors for
    enabledReleaseStages: ['production'],
    releaseStage: process.env.NODE_ENV,

    // App version for tracking releases
    appVersion: process.env.APP_VERSION || '1.0.0',

    // Configure which errors to capture
    autoDetectErrors: true,
    autoTrackSessions: true,

    // Configure breadcrumbs (limited for edge runtime)
    enabledBreadcrumbTypes: [
      'request',
      'process',
      'log',
      'error',
      'manual'
    ],

    // Configure metadata
    metadata: {
      app: {
        name: '<PERSON><PERSON><PERSON>',
        type: 'edge'
      },
      runtime: {
        type: 'edge',
        environment: process.env.NODE_ENV
      }
    },

    // Configure error filtering and enhancement
    onError: (event) => {
      // Add edge runtime context
      event.addMetadata('edge', {
        timestamp: new Date().toISOString(),
        nodeEnv: process.env.NODE_ENV
      });

      // Add request context if available
      if (event.request) {
        event.addMetadata('request', {
          method: event.request.method,
          url: event.request.url,
          timestamp: new Date().toISOString()
        });
      }

      return true;
    }
  });
// }

export default Bugsnag;
