import { create } from 'zustand';
import type * as monaco from 'monaco-editor/esm/vs/editor/editor.api'; // For view state type

// Define File structure (can be nested for the tree)
export interface File {
  path: string; // Unique identifier, e.g., 'src/components/Button.tsx'
  name: string; // Display name, e.g., 'Button.tsx'
  content: string;
  type: 'file' | 'folder';
  children?: File[]; // Populated by buildTree for folders
}

// Type for Monaco's editor view state
type MonacoEditorViewState = monaco.editor.ICodeEditorViewState;

interface BuilderState {
  isBuilderOpen: boolean;
  builderMode: 'code' | 'preview';
  filesByPath: Record<string, File>; // Flat map for O(1) access by path
  tree: File[]; // Nested structure for FileTree component
  selectedFilePath: string | null;
  editorViewState: Record<string, MonacoEditorViewState | null>; // Store view state per file path
  unsavedChanges: Record<string, boolean>; // Track unsaved changes per file path

  // Actions
  toggleBuilder: () => void;
  openBuilder: () => void;
  closeBuilder: () => void;
  setBuilderMode: (mode: 'code' | 'preview') => void;
  selectFile: (filePath: string | null) => void;
  loadInitialFiles: (files: File[]) => void; // Action to process flat list into map and tree
  setEditorViewState: (
    filePath: string,
    viewState: MonacoEditorViewState | null
  ) => void;
  setUnsavedChanges: (filePath: string, hasChanges: boolean) => void;
}

// Helper to build the nested tree structure
const buildTree = (filesMap: Record<string, File>): File[] => {
  const mapWithPathAsKey = { ...filesMap }; // Use the map directly
  const rootLevelFiles = new Set<File>();

  // Create nodes and map them
  Object.values(mapWithPathAsKey).forEach((file) => {
    file.children = file.type === 'folder' ? [] : undefined; // Initialize children for folders
  });

  // Build the tree structure
  Object.values(mapWithPathAsKey).forEach((file) => {
    const segments = file.path.split('/');
    if (segments.length === 1) {
      rootLevelFiles.add(file);
    } else {
      const parentPath = segments.slice(0, -1).join('/');
      const parent = mapWithPathAsKey[parentPath];
      if (parent && parent.type === 'folder' && parent.children) {
        // Ensure no duplicates if buildTree is called multiple times accidentally
        if (!parent.children.some((child) => child.path === file.path)) {
          parent.children.push(file);
        }
      } else {
        // If parent doesn't exist or isn't a folder, add to root (or handle error)
        console.warn(`Parent folder ${parentPath} not found for ${file.path}`);
        rootLevelFiles.add(file);
      }
    }
  });

  // Sort children alphabetically (folders first, then files)
  Object.values(mapWithPathAsKey).forEach((file) => {
    if (file.children) {
      file.children.sort((a, b) => {
        if (a.type !== b.type) {
          return a.type === 'folder' ? -1 : 1; // Folders first
        }
        return a.name.localeCompare(b.name); // Then alphabetically
      });
    }
  });

  // Convert Set to Array and sort root level
  const sortedRoot = Array.from(rootLevelFiles).sort((a, b) => {
    if (a.type !== b.type) {
      return a.type === 'folder' ? -1 : 1; // Folders first
    }
    return a.name.localeCompare(b.name); // Then alphabetically
  });

  return sortedRoot;
};

// Initial fake data (flat list) - Should be loaded via loadInitialFiles
const initialFilesData: File[] = [
  {
    path: 'App.tsx',
    name: 'App.tsx',
    type: 'file',
    content: 'function App() {\n  return <div>Hello</div>;\n}',
  },
  {
    path: 'styles.css',
    name: 'styles.css',
    type: 'file',
    content: 'body { margin: 0; }',
  },
  { path: 'public', name: 'public', type: 'folder', content: '' },
  {
    path: 'public/index.html',
    name: 'index.html',
    type: 'file',
    content:
      '<!DOCTYPE html><html><head><title>Test</title></head><body><h1>Preview</h1></body></html>',
  },
  { path: 'src', name: 'src', type: 'folder', content: '' },
  { path: 'src/components', name: 'components', type: 'folder', content: '' },
  {
    path: 'src/components/Button.tsx',
    name: 'Button.tsx',
    type: 'file',
    content: 'export function Button() { return <button>Click Me</button>; }',
  },
];

export const useBuilderStore = create<BuilderState>((set, get) => ({
  isBuilderOpen: false,
  builderMode: 'code',
  filesByPath: {}, // Initialized by loadInitialFiles
  tree: [], // Initialized by loadInitialFiles
  selectedFilePath: null,
  editorViewState: {},
  unsavedChanges: {},

  toggleBuilder: () =>
    set((state) => ({ isBuilderOpen: !state.isBuilderOpen })),
  openBuilder: () => set({ isBuilderOpen: true }),
  closeBuilder: () => set({ isBuilderOpen: false, selectedFilePath: null }), // Reset selection on close
  setBuilderMode: (mode) => set({ builderMode: mode }),

  selectFile: (filePath) => {
    // Use get() to check if the file exists in filesByPath
    if (filePath && !get().filesByPath[filePath]) {
      console.warn(`File ${filePath} not found in filesByPath`);
    }
    // Save view state of the *previous* file before switching
    // (Implementation detail: This relies on MonacoEditorWrapper calling setEditorViewState on unmount/change)
    set({
      selectedFilePath: filePath,
      builderMode: 'code',
      isBuilderOpen: true,
    }); // Ensure code view is active and builder is open
  },

  loadInitialFiles: (files) => {
    const filesMap: Record<string, File> = {};
    files.forEach((file) => {
      filesMap[file.path] = { ...file }; // Create map entries
    });
    const tree = buildTree(filesMap); // Build nested tree
    set({ filesByPath: filesMap, tree });
  },

  setEditorViewState: (filePath, viewState) =>
    set((state) => ({
      editorViewState: { ...state.editorViewState, [filePath]: viewState },
    })),

  setUnsavedChanges: (filePath, hasChanges) =>
    set((state) => {
      const newUnsavedChanges = { ...state.unsavedChanges };
      if (hasChanges) {
        newUnsavedChanges[filePath] = true;
      } else {
        delete newUnsavedChanges[filePath]; // Remove entry if saved
      }
      return { unsavedChanges: newUnsavedChanges };
    }),
}));

// Initialize the store with default data
export const initializeBuilderStore = () => {
  useBuilderStore.getState().loadInitialFiles(initialFilesData);
};
