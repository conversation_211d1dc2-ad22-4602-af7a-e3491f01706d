import { APP_DEFAULT_MODEL } from '@/constants/chat';

export interface ModelSelectionContext {
  accessibleModelIds: string[];
  userDefaultModelId?: string;
  workspaceDefaultModel?: string;
  promptDefaultModel?: string;
}

export interface ModelSelectionResult {
  modelId: string;
  reason: ModelSelectionReason;
  fallbackModelId?: string; // What would have been selected without prompt/workspace override
}

export type ModelSelectionReason =
  | 'prompt_default'
  | 'workspace_default'
  | 'user_preference'
  | 'last_used'
  | 'app_default'
  | 'first_accessible'
  | 'explicit_selection';

export class ModelSelectionService {
  private context: ModelSelectionContext;

  constructor(context: ModelSelectionContext) {
    this.context = context;
  }

  /**
   * Selects the best model based on the priority hierarchy
   */
  selectModel(options?: {
    promptDefaultModel?: string;
    workspaceDefaultModel?: string;
    explicitSelection?: string;
  }): ModelSelectionResult {
    const { promptDefaultModel, workspaceDefaultModel, explicitSelection } =
      options || {};

    // Get the fallback model (what would be selected without prompt/workspace)
    const fallbackModelId = this.getFallbackModel();

    // 1. Explicit selection (user manually chose)
    if (explicitSelection && this.isModelAccessible(explicitSelection)) {
      return {
        modelId: explicitSelection,
        reason: 'explicit_selection',
        fallbackModelId,
      };
    }

    // 2. Prompt default model
    const effectivePromptDefault =
      promptDefaultModel || this.context.promptDefaultModel;
    if (
      effectivePromptDefault &&
      this.isModelAccessible(effectivePromptDefault)
    ) {
      return {
        modelId: effectivePromptDefault,
        reason: 'prompt_default',
        fallbackModelId,
      };
    }

    // 3. Workspace default model
    const effectiveWorkspaceDefault =
      workspaceDefaultModel || this.context.workspaceDefaultModel;
    if (
      effectiveWorkspaceDefault &&
      this.isModelAccessible(effectiveWorkspaceDefault)
    ) {
      return {
        modelId: effectiveWorkspaceDefault,
        reason: 'workspace_default',
        fallbackModelId,
      };
    }

    // 4. Return fallback model
    return {
      modelId: fallbackModelId,
      reason: this.getFallbackReason(),
      fallbackModelId,
    };
  }

  /**
   * Gets the fallback model (user preference -> last used -> app default -> first accessible)
   */
  private getFallbackModel(): string {
    // 1. User's preferred default model
    if (
      this.context.userDefaultModelId &&
      this.isModelAccessible(this.context.userDefaultModelId)
    ) {
      return this.context.userDefaultModelId;
    }

    // 2. Last used model (from localStorage)
    const lastUsedModel = ModelSelectionService.getLastUsedModel();
    if (lastUsedModel && this.isModelAccessible(lastUsedModel)) {
      return lastUsedModel;
    }

    // 3. App default model
    if (this.isModelAccessible(APP_DEFAULT_MODEL)) {
      return APP_DEFAULT_MODEL;
    }

    // 4. First accessible model
    const firstAccessible = this.getFirstAccessibleModel();
    if (firstAccessible) {
      return firstAccessible;
    }

    throw new Error('No accessible models found');
  }

  /**
   * Gets the reason for the fallback model
   */
  private getFallbackReason(): ModelSelectionReason {
    if (
      this.context.userDefaultModelId &&
      this.isModelAccessible(this.context.userDefaultModelId)
    ) {
      return 'user_preference';
    }

    const lastUsedModel = ModelSelectionService.getLastUsedModel();
    if (lastUsedModel && this.isModelAccessible(lastUsedModel)) {
      return 'last_used';
    }

    if (this.isModelAccessible(APP_DEFAULT_MODEL)) {
      return 'app_default';
    }

    return 'first_accessible';
  }

  /**
   * Checks if a model is accessible to the user
   */
  private isModelAccessible(modelId: string): boolean {
    return this.context.accessibleModelIds.includes(modelId);
  }

  /**
   * Gets the first accessible model
   */
  private getFirstAccessibleModel(): string | null {
    return this.context.accessibleModelIds.length > 0
      ? this.context.accessibleModelIds[0]
      : null;
  }

  /**
   * Checks if the selected model is different from what would be the fallback
   * Used to determine if indicator should be shown
   */
  shouldShowIndicator(result: ModelSelectionResult): boolean {
    return (
      (result.reason === 'prompt_default' ||
        result.reason === 'workspace_default') &&
      result.modelId !== result.fallbackModelId
    );
  }

  /**
   * Formats the reason for display to users
   */
  static formatSelectionReason(result: ModelSelectionResult): string {
    switch (result.reason) {
      case 'prompt_default':
        return 'Switched to prompt\'s default model';
      case 'workspace_default':
        return 'Switched to workspace default model';
      case 'user_preference':
        return 'Using your preferred default model';
      case 'last_used':
        return 'Using your last used model';
      case 'app_default':
        return 'Using system default model';
      case 'first_accessible':
        return 'Using first available model';
      case 'explicit_selection':
        return 'Model selected manually';
      default:
        return 'Model selected';
    }
  }

  /**
   * Updates the last used model (for localStorage persistence)
   */
  static updateLastUsedModel(modelId: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('last_used_model_id', modelId);
    }
  }

  /**
   * Gets the last used model from localStorage
   */
  static getLastUsedModel(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('last_used_model_id');
    }
    return null;
  }
}

/**
 * Utility function to create a ModelSelectionService instance
 */
export function createModelSelectionService(
  context: ModelSelectionContext
): ModelSelectionService {
  return new ModelSelectionService(context);
}

/**
 * Hook-friendly function for React components
 */
export function selectModel(
  context: ModelSelectionContext,
  options?: Parameters<ModelSelectionService['selectModel']>[0]
): ModelSelectionResult {
  const service = new ModelSelectionService(context);
  return service.selectModel(options);
}
