import { <PERSON><PERSON><PERSON>ider } from './llm';
import { ProviderConfig } from './types';
import { OpenAIProvider } from './providers/openai';
import { AnthropicProvider } from './providers/anthropic';
import { GeminiProvider } from './providers/gemini';
import { DeepSeekProvider } from './providers/deepseek';

type ProviderConstructor = new (config: ProviderConfig) => LLMProvider;

export class LLMProviderFactory {
  private static providers: Record<string, ProviderConstructor> = {};

  static registerProvider(
    name: string,
    providerClass: ProviderConstructor
  ): void {
    LLMProviderFactory.providers[name] = providerClass;
  }

  static createProvider(name: string, config: ProviderConfig): LLMProvider {
    const ProviderClass = LLMProviderFactory.providers[name];
    if (!ProviderClass) {
      throw new Error(`Provider ${name} not registered`);
    }
    return new ProviderClass(config);
  }
}

LLMProviderFactory.registerProvider('openai', OpenAIProvider);
LLMProviderFactory.registerProvider('anthropic', AnthropicProvider);
LLMProviderFactory.registerProvider('gemini', GeminiProvider);
LLMProviderFactory.registerProvider('deepseek', DeepSeekProvider);
