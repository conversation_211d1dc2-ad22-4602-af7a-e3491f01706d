import { <PERSON><PERSON><PERSON><PERSON> } from '../llm';
import { CompletionOptions, ProviderConfig, Message } from '../types';
import OpenAI from 'openai';
import { AiError } from '@/lib/error';
import { logger } from '@/lib/logger';
import { fetchAndEncodeFile } from '../utils';

// Helper function to check if a provider supports tools
function supportsTools(model: {
  provider?: unknown;
  allows_tool_usage?: boolean | null;
}): boolean {
  // First check the model's own allows_tool_usage field
  if (model.allows_tool_usage !== undefined) {
    return model.allows_tool_usage === true;
  }

  // Fallback to provider field if it exists and has supports_tool_usage
  return !!(model?.provider as { supports_tool_usage?: boolean })?.supports_tool_usage;
}

const log = logger.child({ provider: 'openai' });

// Helper types match structure needed for API call
type ExpectedInputPart = {
  type: 'text' | 'image_url' | 'file';
  text?: string;
  image_url?: string | { url: string; detail?: 'auto' | 'low' | 'high' };
  filename?: string;
  base64_data?: string;
  mime_type?: string;
  url?: string;
};

type APIInputTextPart = { type: 'input_text'; text: string };
type APIInputImagePart = {
  type: 'input_image';
  image_url: string;
  detail: 'auto' | 'low' | 'high';
};
type APIInputFilePart = {
  type: 'input_file';
  filename: string;
  file_data: string;
};
type APIContentPart = APIInputTextPart | APIInputImagePart | APIInputFilePart;

type UserMessageInput = { role: 'user'; content: APIContentPart[] };
type AssistantMessageInput = { role: 'assistant'; content: string };

export class OpenAIProvider extends LLMProvider {
  protected client: OpenAI;

  constructor(config: ProviderConfig, baseURL?: string) {
    super(config);
    this.validateApiKey();
    this.client = new OpenAI({
      apiKey: this.config.apiKey,
      ...(baseURL && { baseURL }),
    });
  }

  async generateCompletion(
    messages: Message[],
    options: CompletionOptions
  ): Promise<string | null> {
    const formattedMessages = messages.map((msg) => ({
      role: msg.role,
      content: msg.content,
    }));

    const tools: OpenAI.Chat.ChatCompletionTool[] = [];

    // Add image generation tool only if provider supports tools and image generation is enabled
    if (supportsTools(options.model) && options.useImageGeneration) {
      tools.push({
        type: 'function',
        function: {
          name: 'generate_image',
          description: 'Create an image with Stability AI',
          parameters: {
            type: 'object',
            properties: {
              prompt: {
                type: 'string',
                description: 'The text prompt describing the image to generate',
              },
              size: {
                type: 'string',
                enum: ['512x512', '768x768', '1024x1024'],
                default: '768x768',
                description: 'The size of the image to generate',
              },
            },
            required: ['prompt'],
          },
        },
      });
    }

    const completion = await this.client.chat.completions.create({
      model: options.model.name,
      messages: formattedMessages as OpenAI.Chat.ChatCompletionMessageParam[],
      temperature: options.temperature,
      max_tokens: options.maxTokens as number,
      tools: tools.length > 0 ? tools : undefined,
    });
    return completion.choices[0].message.content;
  }

  async generateStreamingCompletion(
    messages: Message[],
    options: CompletionOptions
  ): Promise<ReadableStream> {
    try {
      const formattedMessages = messages.map((msg) => ({
        role: msg.role,
        content: msg.content,
      }));

      const tools: OpenAI.Chat.ChatCompletionTool[] = [];

      // Add image generation tool only if provider supports tools and image generation is enabled
      if (supportsTools(options.model) && options.useImageGeneration) {
        tools.push({
          type: 'function',
          function: {
            name: 'generate_image',
            description: 'Create an image with Stability AI',
            parameters: {
              type: 'object',
              properties: {
                prompt: {
                  type: 'string',
                  description: 'The text prompt describing the image to generate',
                },
                size: {
                  type: 'string',
                  enum: ['512x512', '768x768', '1024x1024'],
                  default: '768x768',
                  description: 'The size of the image to generate',
                },
              },
              required: ['prompt'],
            },
          },
        });
      }

      const modelParam: OpenAI.Chat.ChatCompletionCreateParams = {
        model: options.model.name,
        messages: formattedMessages as OpenAI.Chat.ChatCompletionMessageParam[],
        temperature: options.temperature,
        stream: true,
        tools: tools.length > 0 ? tools : undefined,
      };

      // o1* models don't support max_tokens, convert to max_completion_tokens
      if (
        options.model.name.includes('o1') ||
        options.model.name.includes('o3') ||
        options.model.name.includes('o4')
      ) {
        modelParam.max_completion_tokens = options.maxTokens as number;
        delete modelParam.temperature;
      } else {
        modelParam.max_tokens = options.maxTokens as number;
      }

      const response = await this.client.chat.completions.create(modelParam);

      // Transform the streaming response into a ReadableStream of text chunks
      const encoder = new TextEncoder();

      return new ReadableStream({
        async start(controller) {
          for await (const chunk of response) {
            const choice = chunk.choices[0];
            const content = choice?.delta?.content || '';

            // Handle tool calls
            if (choice?.delta?.tool_calls) {
              for (const toolCall of choice.delta.tool_calls) {
                if (toolCall.function?.name === 'generate_image') {
                  const toolChunk = {
                    type: 'tool',
                    name: 'generate_image',
                    args: JSON.parse(toolCall.function.arguments || '{}'),
                  };
                  controller.enqueue(
                    encoder.encode(JSON.stringify(toolChunk) + '\n')
                  );
                }
              }
            }

            // Handle regular content
            if (content) {
              controller.enqueue(encoder.encode(content));
            }
          }
          controller.close();
        },
      });
    } catch (error: unknown) {
      const err = error as { error?: { message?: string } };
      const errorMessage = err?.error?.message || 'Unknown error';
      throw AiError.streamingError(errorMessage, error as Error);
    }
  }

  async generateStreamingCompletionWithResponsesAPI(
    messages: Message[],
    options: CompletionOptions
  ): Promise<ReadableStream> {
    try {
      const formattedInput = await this._formatMessagesForResponsesAPI(
        messages
      );

      const apiParams: OpenAI.Responses.ResponseCreateParams = {
        model: options.model.name,
        input: formattedInput,
        temperature: options.temperature,
        max_output_tokens: options.maxTokens as number,
        stream: true,
      };

      // o3 models dont support temperature
      if (options.model.name.includes('o3')) {
        delete apiParams.temperature;
      }

      // Add tools configuration for Responses API
      type ResponsesAPITool =
        | { type: 'web_search_preview' }
        | {
            type: 'function';
            function: {
              name: string;
              description: string;
              parameters: {
                type: string;
                properties: Record<string, unknown>;
                required: string[];
              };
            };
          };

      const tools: ResponsesAPITool[] = [];

      if (options.useWebSearch) {
        tools.push({ type: 'web_search_preview' });
      }

      // Add image generation tool only if provider supports tools and image generation is enabled
      if (supportsTools(options.model) && options.useImageGeneration) {
        tools.push({
          type: 'function',
          function: {
            name: 'generate_image',
            description: 'Create an image with Stability AI',
            parameters: {
              type: 'object',
              properties: {
                prompt: {
                  type: 'string',
                  description: 'The text prompt describing the image to generate',
                },
                size: {
                  type: 'string',
                  enum: ['512x512', '768x768', '1024x1024'],
                  default: '768x768',
                  description: 'The size of the image to generate',
                },
              },
              required: ['prompt'],
            },
          },
        });
      }

      if (tools.length > 0) {
        // Type assertion needed due to OpenAI SDK's strict typing for Responses API
        apiParams.tools = tools as OpenAI.Responses.ResponseCreateParams['tools'];
      }

      const responseStream = await this.client.responses.create(apiParams);

      const encoder = new TextEncoder();

      return new ReadableStream({
        async start(controller) {
          try {
            // Ensure responseStream is treated as an async iterable
            const stream =
              responseStream as AsyncIterable<OpenAI.Responses.ResponseStreamEvent>;
            for await (const event of stream) {
              if (event.type === 'response.output_text.delta') {
                const delta = event.delta;
                if (delta) {
                  const chunk = { type: 'delta', content: delta };
                  controller.enqueue(
                    encoder.encode(JSON.stringify(chunk) + '\n')
                  );
                }
              } else if (
                event.type === 'response.output_text.annotation.added'
              ) {
                if (event.annotation.type === 'url_citation') {
                  const chunk = {
                    type: 'annotation',
                    annotation: event.annotation,
                  };
                  controller.enqueue(
                    encoder.encode(JSON.stringify(chunk) + '\n')
                  );
                }
                // TODO: Add function call support for Responses API when types are available
                // } else if (event.type === 'response.function_call.created') {
                //   // Handle function calls (tool calls)
                //   const functionCall = event.function_call;
                //   if (functionCall.name === 'generate_image') {
                //     const chunk = {
                //       type: 'tool',
                //       name: 'generate_image',
                //       args: functionCall.arguments,
                //     };
                //     controller.enqueue(
                //       encoder.encode(JSON.stringify(chunk) + '\n')
                //     );
                //   }
              } else if (
                event.type === 'error' ||
                event.type === 'response.failed'
              ) {
                const errorDetails =
                  event.type === 'error' ? event : event.response.error;
                const errorMessage =
                  errorDetails?.message || 'Unknown streaming error';
                log.error('Streaming Error Event:', errorDetails);
                const errorChunk = {
                  type: 'error',
                  error: { message: errorMessage },
                };
                controller.enqueue(
                  encoder.encode(JSON.stringify(errorChunk) + '\n')
                );
                controller.close();
                return;
              }
            }
            controller.close();
          } catch (streamError) {
            log.error('Error processing stream:', streamError);
            const errorChunk = {
              type: 'error',
              error: { message: 'Error processing stream response' },
            };
            controller.enqueue(
              encoder.encode(JSON.stringify(errorChunk) + '\n')
            );
            controller.close();
          }
        },
        cancel(reason) {
          log.info('Stream cancelled:', reason);
        },
      });
    } catch (error: unknown) {
      const err = error as { error?: { message?: string }; message?: string };
      const errorMessage =
        err?.error?.message ||
        err?.message ||
        'Unknown error initiating stream';
      throw AiError.streamingError(
        errorMessage,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  private async _formatMessagesForResponsesAPI(
    messages: Message[]
  ): Promise<OpenAI.Responses.ResponseInput> {
    const formattedInputPromises = messages.map(
      async (msg): Promise<UserMessageInput | AssistantMessageInput> => {
        if (msg.role === 'assistant') {
          // Assistant messages in history are represented directly
          if (typeof msg.content !== 'string') {
            log.warn(
              'Assistant message content is not a string, simplifying:',
              msg.content
            );
            const assistantText = Array.isArray(msg.content)
              ? msg.content
                  .filter((p) => p.type === 'text')
                  .map((p) => p.text)
                  .join('')
              : '';
            return { role: 'assistant', content: assistantText };
          }
          return { role: 'assistant', content: msg.content };
        }

        // User messages processing (needs input_* types)
        let apiContentParts: APIContentPart[] = [];
        if (Array.isArray(msg.content)) {
          const partPromises = msg.content.map(
            async (partUntyped): Promise<APIContentPart | null> => {
              const part = partUntyped as ExpectedInputPart;

              if (part.type === 'text' && typeof part.text === 'string') {
                return { type: 'input_text', text: part.text };
              } else if (part.type === 'image_url') {
                const imageUrlSource = part.image_url;
                let url: string | undefined;
                let detail: 'auto' | 'low' | 'high' = 'auto';
                if (typeof imageUrlSource === 'string') {
                  url = imageUrlSource;
                } else if (
                  typeof imageUrlSource === 'object' &&
                  imageUrlSource !== null &&
                  typeof imageUrlSource.url === 'string'
                ) {
                  url = imageUrlSource.url;
                  detail = imageUrlSource.detail || 'auto';
                } else {
                  log.warn('Invalid image_url format:', part);
                  return null;
                }
                if (!url) {
                  log.warn('Could not extract URL from image_url:', part);
                  return null;
                }
                return {
                  type: 'input_image',
                  image_url: url,
                  detail: detail ?? 'auto',
                };
              } else if (part.type === 'file' && part.filename) {
                let fileDataUri: string | null = null;
                if (part.base64_data && part.mime_type) {
                  log.debug(
                    `Using provided base64 data for file: ${part.filename}`
                  );
                  fileDataUri = `data:${part.mime_type};base64,${part.base64_data}`;
                } else if (part.url) {
                  log.debug(
                    `Fetching data from URL for file: ${part.filename} (${part.url})`
                  );
                  try {
                    const encodedFile = await fetchAndEncodeFile(
                      part.url,
                      part.mime_type
                    );
                    if (!encodedFile) {
                      throw new Error('File encoding failed');
                    }
                    const { base64String, mimeType } = encodedFile;
                    const dataUriPrefix = `data:${mimeType};base64,`;
                    fileDataUri = `${dataUriPrefix}${base64String}`;
                  } catch (fetchError) {
                    log.error(
                      `Failed to fetch/encode file ${part.filename} from ${part.url}:`,
                      fetchError
                    );
                    return null;
                  }
                } else {
                  log.warn(
                    'File part is missing both base64_data and url:',
                    part
                  );
                  return null;
                }

                return {
                  type: 'input_file',
                  filename: part.filename,
                  file_data: fileDataUri,
                };
              }
              log.warn('Unknown or invalid content part type/structure:', part);
              return null;
            }
          );
          apiContentParts = (await Promise.all(partPromises)).filter(
            (part): part is APIContentPart => part !== null
          );

          if (apiContentParts.length === 0) {
            // If all parts filtered out, use empty text input for user
            apiContentParts = [{ type: 'input_text', text: '' }];
          }
        } else if (typeof msg.content === 'string') {
          // User message with simple string content
          apiContentParts = [{ type: 'input_text', text: msg.content }];
        } else {
          // Default empty user message
          apiContentParts = [{ type: 'input_text', text: '' }];
        }

        return {
          role: 'user', // Explicitly user here
          content: apiContentParts,
        };
      }
    );

    // Cast to the expected OpenAI SDK type
    return (await Promise.all(
      formattedInputPromises
    )) as OpenAI.Responses.ResponseInput;
  }

  async countTokens(text: string): Promise<number> {
    // For accurate token counting, you'd want to use a tokenizer like tiktoken
    // This is a simple approximation
    return Math.ceil(text.length / 4);
  }

  async getModels(): Promise<{ id: string; name: string }[]> {
    const response = await this.client.models.list();
    return response.data.map((model: OpenAI.Models.Model) => ({
      id: model.id,
      name: model.id,
    }));
  }
}
