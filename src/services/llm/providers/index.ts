import { OpenAIProvider } from './openai';
import { AnthropicProvider } from './anthropic';
import { GeminiProvider } from './gemini';
import { DeepSeekProvider } from './deepseek';

export const providers = {
  openai: new OpenAIProvider({
    apiKey: process.env.OPENAI_API_KEY || '',
  }),
  anthropic: new AnthropicProvider({
    apiKey: process.env.ANTHROPIC_API_KEY || '',
  }),
  gemini: new GeminiProvider({
    apiKey: process.env.GEMINI_API_KEY || '',
  }),
  deepseek: new DeepSeekProvider({
    apiKey: process.env.DEEPSEEK_API_KEY || '',
  }),
};

export type ProviderType = keyof typeof providers;
