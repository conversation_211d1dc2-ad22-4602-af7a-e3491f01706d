import {
  GoogleGenerativeAI,
  HarmCategory,
  HarmBlockThreshold,
  Content,
  Part,
  GenerateContentRequest,
  GenerationConfig,
} from '@google/generative-ai';
import { LLMProvider } from '../llm'; // Assuming '../llm' contains the abstract class
import {
  CompletionOptions,
  ProviderConfig,
  Message,
  MessageContentPart,
} from '../types'; // Assuming '../types' contains these interfaces
import { AiError, ErrorCode } from '@/lib/error'; // Assuming error handling exists
import { logger } from '@/lib/logger'; // Assuming logger exists
import { fetchAndEncodeFile } from '../utils'; // Import the new utility

const log = logger.child({ provider: 'gemini' });

export class GeminiProvider extends LLMProvider {
  private client: GoogleGenerativeAI;

  constructor(config: ProviderConfig) {
    super(config);
    this.validateApiKey();
    this.client = new GoogleGenerativeAI(this.config.apiKey!); // api<PERSON><PERSON> is validated
  }

  // --- Core Methods Implementation ---

  async generateCompletion(
    messages: Message[],
    options: CompletionOptions
  ): Promise<string | null> {
    try {
      const { contents, systemInstruction, generationConfig } =
        await this._prepareApiRequest(messages, options);

      const model = this.client.getGenerativeModel({
        model: options.model.name,
        generationConfig, // Pass generation config
        // Default safety settings (adjust as needed)
        safetySettings: [
          {
            category: HarmCategory.HARM_CATEGORY_HARASSMENT,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
          {
            category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
          {
            category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
          {
            category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
        ],
      });

      log.debug('Generating non-streaming completion with Gemini:', {
        model: options.model.name,
        messageCount: contents.length,
        hasSystemInstruction: !!systemInstruction,
        generationConfig,
      });

      const result = await model.generateContent({ contents });
      const response = result.response;

      if (
        !response ||
        !response.candidates ||
        response.candidates.length === 0
      ) {
        log.warn('Gemini response missing candidates:', response);
        // Check for blocked content
        if (response?.promptFeedback?.blockReason) {
          log.error(
            `Gemini request blocked due to: ${response.promptFeedback.blockReason}`,
            response.promptFeedback
          );
          throw AiError.contentBlocked(
            `Request blocked by Gemini safety filters: ${response.promptFeedback.blockReason}`
          );
        }
        return null; // Or throw an error
      }

      // Check finish reason of the first candidate
      const finishReason = response.candidates[0].finishReason;
      if (
        finishReason &&
        finishReason !== 'STOP' &&
        finishReason !== 'MAX_TOKENS'
      ) {
        log.warn(
          `Gemini completion finished due to reason: ${finishReason}`,
          response.candidates[0]
        );
        // Handle specific reasons like SAFETY if necessary
        if (finishReason === 'SAFETY') {
          throw AiError.contentBlocked(
            'Response blocked by Gemini safety filters.'
          );
        }
        // Potentially throw for other reasons like RECITATION, OTHER
      }

      // Extract text, handling potential lack of text content
      const text = response.text?.() ?? null; // Use text() helper, fallback to null

      if (text === null) {
        log.warn(
          'Gemini response candidate has no text content.',
          response.candidates[0]
        );
      }

      return text;
    } catch (error: unknown) {
      log.error('Error generating completion with Gemini:', error);
      if (error instanceof AiError) throw error; // Re-throw known AI errors
      // Attempt to parse Google specific errors
      const message =
        error instanceof Error // Check if error is an Error object
          ? error.message
          : 'Failed to generate completion with Gemini';
      // You might want to check error.details or specific error types from the SDK if available
      throw new AiError(message, ErrorCode.AI_COMPLETION, undefined);
    }
  }

  async generateStreamingCompletion(
    messages: Message[],
    options: CompletionOptions
  ): Promise<ReadableStream> {
    try {
      const { contents, systemInstruction, generationConfig } =
        await this._prepareApiRequest(messages, options);

      const model = this.client.getGenerativeModel({
        model: options.model.name,
        generationConfig, // Pass generation config
        // Default safety settings (adjust as needed)
        safetySettings: [
          {
            category: HarmCategory.HARM_CATEGORY_HARASSMENT,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
          {
            category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
          {
            category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
          {
            category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
        ],
      });

      log.debug('Generating streaming completion with Gemini:', {
        model: options.model.name,
        messageCount: contents.length,
        hasSystemInstruction: !!systemInstruction,
        generationConfig,
      });

      const streamResult = await model.generateContentStream({ contents });

      // Transform the AsyncIterable<GenerateContentResponse> into a ReadableStream
      const encoder = new TextEncoder();
      const readableStream = new ReadableStream({
        async start(controller) {
          try {
            for await (const chunk of streamResult.stream) {
              // Check for blocked content in the stream response
              if (chunk?.promptFeedback?.blockReason) {
                log.error(
                  `Gemini stream blocked due to: ${chunk.promptFeedback.blockReason}`,
                  chunk.promptFeedback
                );
                const errorMsg = `Stream blocked by Gemini safety filters: ${chunk.promptFeedback.blockReason}`;
                controller.error(AiError.contentBlocked(errorMsg));
                return; // Stop processing the stream
              }

              // Check finish reason if available in the chunk (might appear at the end)
              const finishReason = chunk.candidates?.[0]?.finishReason;
              if (
                finishReason &&
                finishReason !== 'STOP' &&
                finishReason !== 'MAX_TOKENS'
              ) {
                log.warn(
                  `Gemini stream finished due to reason: ${finishReason}`,
                  chunk.candidates?.[0]
                );
                if (finishReason === 'SAFETY') {
                  controller.error(
                    AiError.contentBlocked(
                      'Stream response blocked by Gemini safety filters.'
                    )
                  );
                  return;
                }
                // Handle other reasons if needed, maybe just log or close gracefully
              }

              const text = chunk.text?.(); // Use text() helper
              if (text) {
                controller.enqueue(encoder.encode(text));
              }
            }
            controller.close();
          } catch (streamError: unknown) {
            log.error('Error processing Gemini stream:', streamError);
            // Ensure the error passed to controller.error is an actual Error object
            const errorToSignal =
              streamError instanceof Error
                ? streamError
                : new Error(String(streamError));
            controller.error(
              AiError.streamingError(
                'Failed during Gemini stream processing',
                errorToSignal
              )
            );
          }
        },
        cancel(reason) {
          log.info('Gemini stream cancelled:', reason);
          // Clean up resources if necessary, though the SDK might handle this
        },
      });

      return readableStream;
    } catch (error: unknown) {
      log.error('Error initiating streaming completion with Gemini:', error);
      if (error instanceof AiError) throw error; // Re-throw known AI errors
      throw AiError.streamingError(
        'Failed to initiate streaming completion with Gemini',
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  async countTokens(textOrMessages: string | Message[]): Promise<number> {
    try {
      // Use the latest Pro model for token counting as a general baseline
      // Or allow specifying a model if needed
      const model = this.client.getGenerativeModel({
        model: 'gemini-1.5-pro-latest',
      });

      let request: GenerateContentRequest;

      if (typeof textOrMessages === 'string') {
        // Simple text input
        request = {
          contents: [{ role: 'user', parts: [{ text: textOrMessages }] }],
        };
      } else {
        // Format messages array
        const { contents } = await this._prepareApiRequest(textOrMessages, {
          model: { name: 'gemini-1.5-pro-latest' },
          // Cast to bypass strict CompletionOptions type check for token counting
        } as CompletionOptions);
        request = { contents };
      }

      log.debug('Counting tokens with Gemini:', {
        model: model.model,
        hasSystemInstruction: !!request.systemInstruction,
      });
      const result = await model.countTokens(request);
      return result.totalTokens;
    } catch (error: unknown) {
      log.error('Error counting tokens with Gemini:', error);
      // Provide a fallback or throw a specific error
      // Fallback approximation (very rough)
      // const approxText = typeof textOrMessages === 'string' ? textOrMessages : JSON.stringify(textOrMessages);
      // return Math.ceil(approxText.length / 4);
      throw new Error(
        `Failed to count tokens with Gemini: ${
          error instanceof Error ? error.message : String(error) // Get message safely
        }`
      );
    }
  }

  async getModels(): Promise<{ id: string; name: string }[]> {
    // Gemini doesn't have a standard public API endpoint to list *all* fine-tuned models.
    // We list the known base models. Users might need to configure specific fine-tuned IDs.
    // Naming convention: Use the model ID for both 'id' and 'name' for simplicity,
    // or provide more user-friendly names.
    log.debug('Retrieving hardcoded Gemini models list.');
    return Promise.resolve([
      // --- Gemini 1.5 Models ---
      { id: 'gemini-1.5-pro-latest', name: 'Gemini 1.5 Pro (latest)' },
      { id: 'gemini-1.5-flash-latest', name: 'Gemini 1.5 Flash (latest)' },
      // Specific versions (optional, uncomment if needed)
      // { id: 'gemini-1.5-pro-001', name: 'Gemini 1.5 Pro (001)' },
      // { id: 'gemini-1.5-flash-001', name: 'Gemini 1.5 Flash (001)' },

      // --- Older Models (Optional) ---
      { id: 'gemini-1.0-pro', name: 'Gemini 1.0 Pro' }, // Alias for latest 1.0 pro
      // { id: 'gemini-pro', name: 'Gemini Pro (Alias)' }, // Common alias
      // { id: 'gemini-pro-vision', name: 'Gemini Pro Vision' }, // If needed specifically

      // --- Ultra Model (if available/applicable) ---
      // { id: 'gemini-ultra', name: 'Gemini Ultra' }, // Check availability and naming
    ]);
  }

  // --- Helper Methods ---

  protected validateApiKey(): void {
    if (!this.config.apiKey) {
      log.error('Gemini API key is not set.');
      throw new Error('GeminiProvider API key is not set');
    }
  }

  /**
   * Prepares the request object for the Google Generative AI API.
   * Extracts system instructions, formats messages, and builds generation config.
   */
  private async _prepareApiRequest(
    messages: Message[],
    options: CompletionOptions
  ): Promise<{
    contents: Content[];
    systemInstruction?: string | Part | Array<string | Part>;
    generationConfig: GenerationConfig;
  }> {
    const systemMessages = messages.filter((msg) => msg.role === 'system');
    const conversationMessages = messages.filter(
      (msg) => msg.role !== 'system'
    );

    // Handle System Instruction (Use the first system message found)
    let systemInstruction: string | Part | Array<string | Part> | undefined =
      undefined;
    if (systemMessages.length > 0) {
      const sysContent = systemMessages[0].content;
      if (typeof sysContent === 'string') {
        systemInstruction = sysContent;
      } else {
        // If system prompt has parts (e.g., text and image), format them
        // Note: Check Gemini docs if systemInstruction supports complex parts.
        // As of now, it's often simpler text. Let's assume string for now.
        const textPart = sysContent.find((part) => part.type === 'text');
        if (textPart) {
          systemInstruction = textPart.text;
          log.warn(
            'Using only the text part of the first system message as system instruction.'
          );
        } else {
          log.warn(
            'Could not extract string content from system message, ignoring system instruction.'
          );
        }
      }
      if (systemMessages.length > 1) {
        log.warn(
          'Multiple system messages found. Only the first one will be used as system instruction.'
        );
      }
    }

    // Format conversation messages into Gemini's Content[] format
    const contents: Content[] = await this._formatMessagesToContent(
      conversationMessages
    );

    // Build GenerationConfig
    const generationConfig: GenerationConfig = {
      // candidateCount: options.candidateCount, // If needed
      // stopSequences: options.stopSequences, // Pass through stop sequences
      maxOutputTokens: options.maxTokens, // Map maxTokens
      temperature: options.temperature, // Pass through temperature
      // topP: options.topP, // If needed
      // topK: options.topK, // If needed
    };

    // Remove undefined keys from generationConfig
    Object.keys(generationConfig).forEach((key) => {
      const typedKey = key as keyof GenerationConfig;
      if (generationConfig[typedKey] === undefined) {
        delete generationConfig[typedKey];
      }
    });

    return { contents, systemInstruction, generationConfig };
  }

  /**
   * Formats the internal Message[] structure (excluding system messages)
   * into the Content[] structure required by the Gemini API.
   * Handles multimodal content (text, image, file).
   */
  private async _formatMessagesToContent(
    messages: Message[]
  ): Promise<Content[]> {
    const formattedContent: Content[] = [];

    for (const msg of messages) {
      // Map roles: 'assistant' -> 'model', 'user' -> 'user'
      const role = msg.role === 'assistant' ? 'model' : 'user';
      if (role !== 'user' && role !== 'model') {
        log.warn(`Skipping message with unmappable role: ${msg.role}`);
        continue; // Skip messages with roles Gemini doesn't understand
      }

      const parts: Part[] = [];
      const content = msg.content;

      if (typeof content === 'string') {
        // Simple text message
        if (content.trim()) {
          // Avoid adding empty text parts
          parts.push({ text: content });
        }
      } else if (Array.isArray(content)) {
        // Multimodal message with parts
        for (const partUntyped of content) {
          // Cast to our extended type to check for url, filename etc.
          // We assume these extra props might be added dynamically to the part
          const part = partUntyped as MessageContentPart & {
            url?: string;
            filename?: string;
            mime_type?: string;
            base64_data?: string;
          };

          if (part.type === 'text') {
            if (part.text.trim()) {
              // Avoid empty text parts
              parts.push({ text: part.text });
            }
          } else if (part.type === 'image_url') {
            // Handle image_url type
            let imageData: { base64String: string; mimeType: string } | null =
              null;

            // Prefer pre-encoded base64 data if available and mime_type is known
            if (part.base64_data && part.mime_type) {
              log.debug(
                `Using provided base64 data for image: ${
                  part.filename ?? 'image'
                }`
              );
              imageData = {
                base64String: part.base64_data,
                mimeType: part.mime_type,
              };
            }
            // Otherwise, fetch from URL
            else {
              // image_url can be string or { url: string }
              const url =
                typeof part.image_url === 'string'
                  ? part.image_url
                  : part.image_url?.url; // Access url safely

              if (url) {
                // Pass mime_type hint if available
                imageData = await fetchAndEncodeFile(url, part.mime_type);
              } else {
                log.warn('Image part missing URL and base64 data:', part);
              }
            }

            if (imageData) {
              parts.push({
                inlineData: {
                  data: imageData.base64String,
                  mimeType: imageData.mimeType,
                },
              });
            } else {
              log.warn(
                'Skipping image part due to missing data/URL or fetch error.'
              );
            }
          } else if (part.type === 'file') {
            // Handle file type (could be image or other)
            let fileData: { base64String: string; mimeType: string } | null =
              null;

            // Prefer pre-encoded base64 data
            if (part.base64_data && part.mime_type) {
              log.debug(
                `Using provided base64 data for file: ${
                  part.filename ?? 'file'
                }`
              );
              fileData = {
                base64String: part.base64_data,
                mimeType: part.mime_type,
              };
            }
            // Otherwise, fetch from URL (requires mime_type hint)
            else if (part.url && part.mime_type) {
              fileData = await fetchAndEncodeFile(part.url, part.mime_type);
            } else {
              log.warn('File part missing URL/mime_type or base64 data:', part);
            }

            if (fileData) {
              // Gemini uses inlineData for files too
              parts.push({
                inlineData: {
                  data: fileData.base64String,
                  mimeType: fileData.mimeType,
                },
              });
            } else {
              log.warn(
                'Skipping file part due to missing data/URL or fetch error.'
              );
            }
          } else {
            // Handle potential future part types or log unexpected ones
            // This case should ideally not be reached if MessageContentPart covers all types
            // Use type assertion to access 'type' if TypeScript thinks it's 'never'
            const partType = (part as { type?: unknown }).type; // Avoid 'any'
            log.warn('Skipping unsupported message part type:', partType);
          }
        }
      } else {
        log.warn('Skipping message with unexpected content type:', content);
      }

      // Only add the message if it has valid parts
      if (parts.length > 0) {
        formattedContent.push({ role, parts });
      } else {
        log.warn('Skipping message as it resulted in zero valid parts:', msg);
      }
    }

    // Gemini requires conversation to start with a 'user' role.
    // If the first message isn't 'user', we might need to prepend an empty user message
    // or adjust logic based on API constraints. Let's check the SDK's behavior,
    // but for now, we assume the input `messages` adhere to `user`, `assistant`, `user`... sequence
    // after filtering system prompts.
    if (formattedContent.length > 0 && formattedContent[0].role !== 'user') {
      log.warn(
        'Conversation does not start with a "user" message. Prepending an empty user message.'
      );
      // This might not be ideal, review Gemini best practices.
      // Consider throwing an error if the sequence is invalid.
      formattedContent.unshift({
        role: 'user',
        parts: [{ text: '(Previous context)' }],
      });
    }

    // Gemini also requires alternating user/model roles.
    // Let's add a check and potentially filter out consecutive messages of the same role.
    const cleanedContent: Content[] = [];
    let lastRole: string | null = null;
    for (const content of formattedContent) {
      if (content.role === lastRole) {
        log.warn(
          `Skipping consecutive message with role: ${content.role}. Merging or handling might be needed.`
        );
        // Simple approach: skip the duplicate role message.
        // More complex: merge content if possible (especially text).
        continue;
      }
      cleanedContent.push(content);
      lastRole = content.role;
    }

    return cleanedContent; // Return the potentially cleaned list
  }
}
