import { <PERSON><PERSON><PERSON><PERSON> } from '../llm';
import { CompletionOptions, ProviderConfig, Message, MessageContentPart } from '../types';
import Anthropic from '@anthropic-ai/sdk';
import { AiError } from '@/lib/error';
import { logger } from '@/lib/logger';

const log = logger.child({ provider: 'anthropic' });

export class AnthropicProvider extends LLMProvider {
  private client: Anthropic;

  constructor(config: ProviderConfig) {
    super(config);
    this.validateApiKey();
    this.client = new Anthropic({
      apiKey: this.config.apiKey,
    });
  }

  async generateCompletion(
    messages: Message[],
    options: CompletionOptions
  ): Promise<string | null> {
    // Format messages for Anthropic API
    const formattedMessages = this.formatMessages(messages);

    const response = await this.client.messages.create({
      model: options.model.name,
      messages: formattedMessages,
      temperature: options.temperature,
      max_tokens: options.maxTokens as number,
      stop_sequences: options.stopSequences,
      system: this.extractSystemPrompt(messages),
      stream: false,
    });

    return response.content[0].type === 'text'
      ? response.content[0].text
      : null;
  }

  async generateStreamingCompletion(
    messages: Message[],
    options: CompletionOptions
  ): Promise<ReadableStream> {
    // Format messages for Anthropic API
    try {
      const formattedMessages = this.formatMessages(messages);

      const stream = await this.client.messages.create({
        model: options.model.name,
        messages: formattedMessages,
        temperature: options.temperature,
        max_tokens: options.maxTokens as number,
        stop_sequences: options.stopSequences,
        system: this.extractSystemPrompt(messages),
        stream: true,
      });

      // Transform the streaming response into a ReadableStream of text chunks
      const encoder = new TextEncoder();

      return new ReadableStream({
        async start(controller) {
          for await (const chunk of stream) {
            if (chunk.type === 'content_block_delta' && 'text' in chunk.delta) {
              controller.enqueue(encoder.encode(chunk.delta.text));
            }
          }
          controller.close();
        },
      });
    } catch (error) {
      throw AiError.streamingError(
        'Failed to generate streaming completion',
        error as Error
      );
    }
  }

  // Helper to extract system prompt
  private extractSystemPrompt(messages: Message[]): string | undefined {
    const systemMessages = messages.filter((msg) => msg.role === 'system');
    if (systemMessages.length > 0) {
      const content = systemMessages[0].content;
      // Anthropic system prompt must be a string
      if (typeof content === 'string') {
        return content;
      }
    }
    return undefined;
  }

  private formatMessages(messages: Message[]): Anthropic.MessageParam[] {
    // Filter out system messages as they're handled separately
    return messages
      .filter((msg) => msg.role !== 'system')
      .map((msg): Anthropic.MessageParam => {
        let content: string | Anthropic.ContentBlockParam[];

        if (typeof msg.content === 'string') {
          content = msg.content;
        } else {
          // Map MessageContentPart[] to Anthropic.ContentBlockParam[]
          content = msg.content
            .map((part): Anthropic.ContentBlockParam | null => {
              // Cast includes url from updated FileContentPart
              const extendedPart = part as MessageContentPart & {
                url?: string;
                mime_type?: string;
                filename?: string;
              };

              if (extendedPart.type === 'text') {
                return { type: 'text', text: extendedPart.text };
              } else if (extendedPart.type === 'image_url') {
                // Use URL source for images
                return {
                  type: 'image',
                  source: {
                    type: 'url',
                    url: extendedPart.image_url.url,
                    // Anthropic image type doesn't use media_type directly here?
                    // Check Anthropic docs if specific media_type needed for URL source.
                  },
                };
              } else if (extendedPart.type === 'file' && extendedPart.url && extendedPart.mime_type) {
                 // Use URL source for files that Anthropic supports via URL (e.g., PDF)
                 // Anthropic uses 'document' type for files like PDF.
                 // We might need to map mime_types if Anthropic supports other file types via URL.
                 if (extendedPart.mime_type === 'application/pdf') {
                    log.info(`Using URL source for PDF file: ${extendedPart.filename}`);
                    return {
                        type: 'document',
                        source: {
                            type: 'url',
                            url: extendedPart.url,
                        },
                    };
                 } else {
                    // If it's a file type Anthropic doesn't support via URL,
                    // or we don't know how to map it, skip it.
                    log.warn(`Skipping file type ${extendedPart.mime_type} as Anthropic URL source is not implemented/supported for it.`);
                    return null;
                 }
              }
              // Log and return null for other unsupported part types or structures
              log.warn('Skipping unsupported message part structure:', part);
              return null;
            })
            .filter(
              (part): part is Anthropic.ContentBlockParam => part !== null
            );

          // Handle cases where content becomes empty after filtering
          if (content.length === 0) {
            // If the original content was an empty string or empty array, result should be empty string.
            // If original content had parts but all were unsupported, result should be empty string.
            content = '';
            if (typeof msg.content !== 'string' && msg.content.length > 0) {
              log.warn(
                'Message content resulted in empty after filtering unsupported parts. Sending empty content.',
                msg
              );
            }
          }
        }

        // Anthropic API requires content to be a string or a non-empty array.
        // If we ended up with an empty array, make it an empty string.
        if (Array.isArray(content) && content.length === 0) {
          content = '';
        }

        // Ensure the role is correctly typed for Anthropic API
        const role = msg.role as 'user' | 'assistant';
        if (role !== 'user' && role !== 'assistant') {
          log.error(
            `Invalid role ${msg.role} found after filtering system messages.`
          );
          // This should ideally not happen due to the filter above, but as a safeguard:
          throw new Error(
            `Invalid message role ${msg.role} for Anthropic API.`
          );
        }

        return {
          role: role,
          content: content,
        };
      });
  }

  async countTokens(text: string): Promise<number> {
    // For accurate token counting, consider using Anthropic's tokenizer
    // This is a simple approximation
    return Math.ceil(text.length / 4);
  }

  async getModels(): Promise<{ id: string; name: string }[]> {
    // Anthropic doesn't have a models endpoint, so we hard-code the available models
    return [
      { id: 'claude-3-5-sonnet-20241022', name: 'Claude 3.5 Sonnet' },
      { id: 'claude-3-haiku-20240307', name: 'Claude 3 Haiku' },
      { id: 'claude-3-opus-20240229', name: 'Claude 3 Opus' },
    ];
  }
}
