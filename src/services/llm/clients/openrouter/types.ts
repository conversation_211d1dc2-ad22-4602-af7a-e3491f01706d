export interface OpenRouterUsage {
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
  prompt_tokens_details?: {
    cached_tokens: number;
  };
  completion_tokens_details?: {
    reasoning_tokens: number;
  };
}

export interface OpenRouterMessage {
  role: string;
  content: string | OpenRouterMessageContentPart[];
  annotations?: unknown[] | null;
}

export interface OpenRouterImageUrlOptions {
  url: string;
  detail?: 'auto' | 'low' | 'high';
}

export interface OpenRouterFileOptions {
  filename: string;
  file_data: string;
}

export interface OpenRouterMessageContentPart {
  type: 'text' | 'image_url' | 'file';
  text?: string;
  image_url?: string | OpenRouterImageUrlOptions;
  file?: OpenRouterFileOptions;
}

export interface OpenRouterCompletionOptions {
  model: string;
  messages: OpenRouterMessage[];
  stream?: boolean;
  temperature?: number;
  max_tokens?: number;
  stop?: string[];
  top_p?: number;
  top_k?: number;
  tools?: Array<{
    type: string;
    function: {
      name: string;
      description: string;
      parameters: {
        type: string;
        properties: Record<string, unknown>;
        required: string[];
      };
    };
  }>;
  plugins?: Array<{
    id: string;
    max_results?: number;
    pdf?: { engine: string };
  }>;
}

export interface OpenRouterResponse {
  id: string;
  model: string;
  choices: {
    message: {
      role: string;
      content: string;
      annotations?: Array<{
        type: string;
        url_citation?: {
          url: string;
          title: string;
          content?: string;
          start_index: number;
          end_index: number;
        };
      }>;
    };
    finish_reason: string;
    index: number;
  }[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}