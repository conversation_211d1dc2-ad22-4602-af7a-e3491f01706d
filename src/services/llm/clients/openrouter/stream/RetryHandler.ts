import { logger } from '@/lib/logger';
import { AppError, ErrorCode } from '@/lib/error';

export class RetryHandler {
  private log = logger.child({ service: 'RetryHandler' });

  async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries = 3,
    initialBackoffMs = 1000
  ): Promise<T> {
    let lastError: Error | undefined;
    let backoffMs = initialBackoffMs;

    /**
     * We treat `maxRetries` as the number of *additional* attempts we can make *after* the first failure.
     * Therefore, the total number of attempts equals `maxRetries + 1` and the loop starts with `attempt = 0`.
     * This guarantees that the operation is executed at least once even when `maxRetries` is 0.
     */
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;

        // If the error is not retryable OR we've exhausted all retries, re-throw.
        const exhaustedRetries = attempt === maxRetries;
        if (!this.isRetryableError(error) || exhaustedRetries) {
          const errToThrow =
            lastError instanceof AppError
              ? lastError
              : new AppError(
                  lastError.message,
                  ErrorCode.AI_COMPLETION,
                  500,
                  lastError
                );

          if (exhaustedRetries) {
            this.log.error('Max retries reached', {
              maxRetries,
              lastError: lastError.message,
            });
          }

          throw errToThrow;
        }

        // Log and perform backoff before next attempt.
        this.log.warn('Operation failed, retrying', {
          attempt: attempt + 1, // 1-based for readability
          maxRetries,
          backoffMs,
          error: lastError.message,
        });

        const jitter = backoffMs * (Math.random() * 0.5); // 0-50% jitter
        await new Promise((resolve) => setTimeout(resolve, backoffMs + jitter));
        backoffMs *= 2; // Exponential backoff
      }
    }

    // The code should never reach this point, but TypeScript requires a return/throw.
    throw new AppError(
      'Unexpected failure in RetryHandler.withRetry',
      ErrorCode.AI_COMPLETION,
      500,
      lastError
    );
  }

  private isRetryableError(error: unknown): boolean {
    if (error instanceof AppError) {
      // Base retry decision primarily on HTTP status code
      if (error.statusCode) {
        // Always retry server errors (5xx)
        if (error.statusCode >= 500 && error.statusCode < 600) {
          return true;
        }
        // Always retry rate limiting (429)
        if (error.statusCode === 429) {
          return true;
        }
        // Don't retry other client errors (4xx except 429)
        if (error.statusCode >= 400 && error.statusCode < 500) {
          return false;
        }
      }
    }

    // For errors without status codes, be conservative and retry
    // (network errors, timeouts, etc.)
    return true;
  }
}
