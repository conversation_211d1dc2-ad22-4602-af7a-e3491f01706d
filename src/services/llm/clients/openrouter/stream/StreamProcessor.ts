import { logger } from '@/lib/logger';
import { ToolCallManager } from './ToolCallManager';
import { StreamMetadataClientData } from '@/lib/supabase/types';
import { AppError, ErrorCode } from '@/lib/error';
import { TextDecoder } from 'util';

export type StreamEvent =
  | { type: 'metadata'; data: StreamMetadataClientData }
  | { type: 'delta'; content: string }
  | { type: 'tool'; name: string; args: Record<string, unknown> }
  | { type: 'annotation'; annotation: unknown }
  | { type: 'error'; error: string; recoverable: boolean }
  | { type: 'complete'; finalMetadata?: StreamMetadataClientData };

export class StreamProcessor {
  private buffer = '';
  private readonly maxBufferSize = 1024 * 1024; // 1MB limit
  private toolCallManager = new ToolCallManager();
  private hasSentMetadata = false;
  private log = logger.child({ service: 'StreamProcessor' });

  private safeBufferTruncate(buffer: string, maxSize: number): string {
    if (buffer.length <= maxSize) return buffer;

    // Find the last complete JSON object boundary
    const truncatePoint = Math.floor(maxSize * 0.75);
    const lastNewline = buffer.lastIndexOf('\n', truncatePoint);
    const lastBrace = buffer.lastIndexOf('}', truncatePoint);
    const lastBracket = buffer.lastIndexOf(']', truncatePoint);

    // Find the furthest complete boundary
    const boundary = Math.max(lastNewline, lastBrace, lastBracket);

    if (boundary > 0) {
      this.log.warn('Buffer truncated at complete boundary', {
        originalSize: buffer.length,
        newSize: boundary + 1,
        truncationPoint: boundary,
      });
      const tail = buffer.substring(boundary + 1);

      // If the tail now fits, return it whole
      if (tail.length <= maxSize) {
        return tail;
      }

      // Otherwise, search inside the last maxSize portion for a fresh boundary
      const segment = tail.slice(-maxSize);
      const segNewline = segment.lastIndexOf('\n');
      const segBrace = segment.lastIndexOf('}');
      const segBracket = segment.lastIndexOf(']');
      const segBoundary = Math.max(segNewline, segBrace, segBracket);

      if (segBoundary > 0) {
        return segment.substring(segBoundary + 1);
      }

      // No boundary in that slice? Return the raw last maxSize chars
      return segment;
    }

    // No clean boundary found—truncate at max size from end
    this.log.warn('Buffer truncated at max size (no clean boundary found)', {
      originalSize: buffer.length,
      newSize: maxSize,
    });
    return buffer.substring(buffer.length - maxSize);
  }

  process(chunk: Uint8Array): StreamEvent[] {
    const decoder = new TextDecoder();
    const newData = decoder.decode(chunk, { stream: true });
    const combinedData = this.buffer + newData;

    const events: StreamEvent[] = [];

    // If the chunk is extremely large, try to process it directly
    if (combinedData.length > this.maxBufferSize) {
      // Process complete lines first to handle large chunks
      const lines = combinedData.split('\n');
      const incompleteLastLine = lines.pop() || '';

      // Process complete lines immediately
      for (const line of lines) {
        if (!line.trim()) continue;

        if (line.startsWith('data: ')) {
          const data = line.substring(6);
          if (data === '[DONE]') {
            this.log.info('Received [DONE] marker');
            continue;
          }

          try {
            const parsed = JSON.parse(data);
            const newEvents = this.processChunk(parsed);
            events.push(...newEvents);
          } catch (error) {
            this.log.error('Failed to parse JSON data', {
              line: line.substring(0, 100) + '...', // Truncate long lines in logs
              error: error instanceof Error ? error.message : 'Unknown error',
            });
            events.push({
              type: 'error',
              error: 'Failed to parse stream data',
              recoverable: true,
            });
          }
        }
      }

      // Keep only the incomplete line in buffer with size limit
      this.buffer =
        incompleteLastLine.length > this.maxBufferSize
          ? this.safeBufferTruncate(incompleteLastLine, this.maxBufferSize)
          : incompleteLastLine;
    } else {
      // Normal processing for smaller chunks
      this.buffer = combinedData;

      // Process complete lines
      const lines = this.buffer.split('\n');
      this.buffer = lines.pop() || ''; // Keep incomplete line in buffer

      for (const line of lines) {
        if (!line.trim()) continue;

        if (line.startsWith('data: ')) {
          const data = line.substring(6);
          if (data === '[DONE]') {
            this.log.info('Received [DONE] marker');
            continue;
          }

          try {
            const parsed = JSON.parse(data);
            const newEvents = this.processChunk(parsed);
            events.push(...newEvents);
          } catch (error) {
            this.log.error('Failed to parse JSON data', {
              line: line.substring(0, 100) + '...', // Truncate long lines in logs
              error: error instanceof Error ? error.message : 'Unknown error',
            });
            events.push({
              type: 'error',
              error: 'Failed to parse stream data',
              recoverable: true,
            });
          }
        }
      }
    }

    return events;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private processChunk(chunk: any): StreamEvent[] {
    const events: StreamEvent[] = [];

    // Handle metadata - check for metadata even without choices
    if (!this.hasSentMetadata) {
      const metadata: StreamMetadataClientData = {};
      if (chunk.provider) metadata.provider = chunk.provider;
      if (chunk.model) metadata.model = chunk.model;
      if (chunk.id) metadata.id = chunk.id;
      if (chunk.object) metadata.object_type = chunk.object;
      if (chunk.created) metadata.created_timestamp = chunk.created;

      if (Object.keys(metadata).length > 0) {
        this.hasSentMetadata = true;
        events.push({ type: 'metadata', data: metadata });
      }
    }

    // Handle usage data
    if (chunk.usage) {
      events.push({
        type: 'metadata',
        data: { usage: chunk.usage },
      });
    }

    const choice = chunk.choices?.[0];
    if (!choice) {
      return events;
    }

    // Handle content delta
    if (choice.delta?.content) {
      events.push({
        type: 'delta',
        content: choice.delta.content,
      });
    }

    // Handle tool calls
    if (choice.delta?.tool_calls) {
      for (const toolCall of choice.delta.tool_calls) {
        const fn = toolCall.function;

        this.toolCallManager.addChunk(
          toolCall.id,
          fn?.name,
          fn?.arguments,
          toolCall.index
        );
      }
    }

    // Handle completed tool calls
    if (choice.finish_reason === 'tool_calls') {
      const pendingCalls = this.toolCallManager.getPendingCalls();
      for (const call of pendingCalls) {
        try {
          const parsedArgs = call.arguments.trim()
            ? JSON.parse(call.arguments)
            : {};

          events.push({
            type: 'tool',
            name: call.name,
            args: parsedArgs,
          });

          this.toolCallManager.completeCall(call.id);
        } catch (error) {
          this.log.error('Failed to parse tool arguments', {
            callId: call.id,
            error,
          });
          this.toolCallManager.errorCall(call.id, error as Error);
        }
      }
    }

    // Handle annotations
    if (choice.delta?.annotations) {
      for (const annotation of choice.delta.annotations) {
        events.push({
          type: 'annotation',
          annotation,
        });
      }
    }

    return events;
  }

  flush(): StreamEvent[] {
    const events: StreamEvent[] = [];

    // Handle any remaining data in buffer
    if (this.buffer.trim()) {
      // Extract JSON string from SSE format if present
      const jsonString = this.buffer.startsWith('data: ')
        ? this.buffer.substring(6)
        : this.buffer;

      if (jsonString.trim()) {
        try {
          // Attempt to parse the final buffer as-is
          const parsed = JSON.parse(jsonString);
          const newEvents = this.processChunk(parsed);
          events.push(...newEvents);
        } catch (error) {
          // Log the corrupt buffer content for debugging
          this.log.error('Failed to parse remaining buffer data', {
            error: error instanceof Error ? error.message : 'Unknown error',
            bufferContent:
              jsonString.substring(0, 200) +
              (jsonString.length > 200 ? '...' : ''),
            bufferLength: jsonString.length,
          });

          // Emit a non-recoverable error event
          events.push({
            type: 'error',
            error: 'Stream ended with incomplete data that could not be parsed',
            recoverable: false,
          });
        }
      }
    }

    // Handle incomplete tool calls
    const pendingCalls = this.toolCallManager.getPendingCalls();
    if (pendingCalls.length > 0) {
      this.log.warn('Stream ended with incomplete tool calls', {
        count: pendingCalls.length,
      });
      for (const call of pendingCalls) {
        this.toolCallManager.errorCall(
          call.id,
          new AppError('Tool call incomplete', ErrorCode.AI_STREAMING)
        );
      }
    }

    this.clear();
    return events;
  }

  clear(): void {
    this.buffer = '';
    this.hasSentMetadata = false;
    this.toolCallManager.clear();
  }
}
