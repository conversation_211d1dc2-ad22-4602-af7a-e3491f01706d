import { StreamProcessor, StreamEvent } from '../StreamProcessor';
import { TextEncoder } from 'util';

describe('StreamProcessor', () => {
  let processor: StreamProcessor;
  const encoder = new TextEncoder();

  beforeEach(() => {
    processor = new StreamProcessor();
  });

  it('should process metadata events', () => {
    const chunk = encoder.encode(
      'data: {"id":"test-id","model":"test-model","provider":"test-provider"}\n'
    );
    const events = processor.process(chunk);
    expect(events).toHaveLength(1);
    expect(events[0]).toEqual({
      type: 'metadata',
      data: {
        id: 'test-id',
        model: 'test-model',
        provider: 'test-provider',
      },
    });
  });

  it('should process content delta events', () => {
    const chunk = encoder.encode(
      'data: {"choices":[{"delta":{"content":"Hello"}}]}\n'
    );
    const events = processor.process(chunk);
    expect(events).toHaveLength(1);
    expect(events[0]).toEqual({
      type: 'delta',
      content: 'Hello',
    });
  });

  it('should process tool call events', () => {
    const chunks = [
      encoder.encode(
        'data: {"choices":[{"delta":{"tool_calls":[{"id":"1","function":{"name":"test_function"}}]}}]}\n'
      ),
      encoder.encode(
        'data: {"choices":[{"delta":{"tool_calls":[{"id":"1","function":{"arguments":"{\\"arg\\":\\"value\\"}"}}]}}]}\n'
      ),
      encoder.encode(
        'data: {"choices":[{"finish_reason":"tool_calls"}]}\n'
      ),
    ];

    let events: StreamEvent[] = [];
    for (const chunk of chunks) {
      events = events.concat(processor.process(chunk));
    }

    expect(events).toContainEqual({
      type: 'tool',
      name: 'test_function',
      args: { arg: 'value' },
    });
  });

  it('should handle malformed JSON gracefully', () => {
    const chunk = encoder.encode('data: {"malformed":}\n');
    const events = processor.process(chunk);
    expect(events).toHaveLength(1);
    expect(events[0]).toEqual({
      type: 'error',
      error: 'Failed to parse stream data',
      recoverable: true,
    });
  });

  it('should handle incomplete chunks', () => {
    const chunk = encoder.encode('data: {"choices":[{"delta":{"content":"Hello"}}');
    const events = processor.process(chunk);
    expect(events).toHaveLength(0);
  });

  it('should handle multiple events in one chunk', () => {
    const chunk = encoder.encode(
      'data: {"choices":[{"delta":{"content":"Hello"}}]}\n' +
      'data: {"choices":[{"delta":{"content":"World"}}]}\n'
    );
    const events = processor.process(chunk);
    expect(events).toHaveLength(2);
    expect(events[0]).toEqual({
      type: 'delta',
      content: 'Hello',
    });
    expect(events[1]).toEqual({
      type: 'delta',
      content: 'World',
    });
  });

  it('should handle [DONE] marker', () => {
    const chunk = encoder.encode('data: [DONE]\n');
    const events = processor.process(chunk);
    expect(events).toHaveLength(0);
  });

  it('should handle usage data', () => {
    const chunk = encoder.encode(
      'data: {"usage":{"prompt_tokens":10,"completion_tokens":20,"total_tokens":30}}\n'
    );
    const events = processor.process(chunk);
    expect(events).toHaveLength(1);
    expect(events[0]).toEqual({
      type: 'metadata',
      data: {
        usage: {
          prompt_tokens: 10,
          completion_tokens: 20,
          total_tokens: 30,
        },
      },
    });
  });

  it('should handle annotations', () => {
    const chunk = encoder.encode(
      'data: {"choices":[{"delta":{"annotations":[{"type":"citation","url_citation":{"url":"test.com","title":"Test"}}]}}]}\n'
    );
    const events = processor.process(chunk);
    expect(events).toHaveLength(1);
    expect(events[0]).toEqual({
      type: 'annotation',
      annotation: {
        type: 'citation',
        url_citation: {
          url: 'test.com',
          title: 'Test',
        },
      },
    });
  });

  it('should flush remaining data', () => {
    // Add some data to the buffer
    processor.process(encoder.encode('data: {"choices":[{"delta":{"content":"Hello"}}]}'));

    // Flush should process the remaining data
    const events = processor.flush();
    expect(events).toHaveLength(1);
    expect(events[0]).toEqual({
      type: 'delta',
      content: 'Hello',
    });
  });

  it('should handle buffer size limits', () => {
    // Create a large chunk that exceeds the buffer size limit
    const largeChunk = 'x'.repeat(2 * 1024 * 1024); // 2MB
    const chunk = encoder.encode(`data: {"choices":[{"delta":{"content":"${largeChunk}"}}]}\n`);

    // Should not throw and should process the chunk
    const events = processor.process(chunk);
    expect(events).toHaveLength(1);
    expect(events[0].type).toBe('delta');
  });
});