import { ToolCallManager } from '../ToolCallManager';
import { AppError, ErrorCode } from '@/lib/error';

describe('ToolCallManager', () => {
  let manager: ToolCallManager;

  beforeEach(() => {
    manager = new ToolCallManager();
  });

  it('should add chunks to a tool call', () => {
    manager.addChunk('1', 'test_function', '{"arg":"value"}');
    const call = manager.getCall('1');
    expect(call).toBeDefined();
    expect(call?.name).toBe('test_function');
    expect(call?.arguments).toBe('{"arg":"value"}');
    expect(call?.status).toBe('pending');
  });

  it('should handle chunks without ID', () => {
    manager.addChunk('', 'test_function', '{"arg":"value"}');
    const calls = manager.getPendingCalls();
    expect(calls).toHaveLength(0);
  });

  it('should accumulate arguments across chunks', () => {
    manager.addChunk('1', 'test_function', '{"arg1":"value1"}');
    manager.addChunk('1', undefined, '{"arg2":"value2"}');

    const call = manager.getCall('1');
    expect(call?.arguments).toBe('{"arg1":"value1"}{"arg2":"value2"}');
  });

  it('should update name if provided in later chunks', () => {
    manager.addChunk('1', undefined, '{"arg":"value"}');
    manager.addChunk('1', 'test_function', undefined);

    const call = manager.getCall('1');
    expect(call?.name).toBe('test_function');
  });

  it('should complete a tool call', () => {
    manager.addChunk('1', 'test_function', '{"arg":"value"}');
    const completedCall = manager.completeCall('1');

    expect(completedCall?.status).toBe('complete');
    expect(manager.getPendingCalls()).toHaveLength(0);
  });

  it('should handle errors in tool calls', () => {
    manager.addChunk('1', 'test_function', '{"arg":"value"}');
    const error = new AppError('Test error', ErrorCode.AI_COMPLETION);
    manager.errorCall('1', error);

    const call = manager.getCall('1');
    expect(call?.status).toBe('error');
  });

  it('should clear all tool calls', () => {
    manager.addChunk('1', 'test_function1', '{"arg1":"value1"}');
    manager.addChunk('2', 'test_function2', '{"arg2":"value2"}');

    manager.clear();
    expect(manager.getPendingCalls()).toHaveLength(0);
    expect(manager.getCall('1')).toBeUndefined();
    expect(manager.getCall('2')).toBeUndefined();
  });

  it('should track multiple tool calls independently', () => {
    manager.addChunk('1', 'test_function1', '{"arg1":"value1"}');
    manager.addChunk('2', 'test_function2', '{"arg2":"value2"}');

    const calls = manager.getPendingCalls();
    expect(calls).toHaveLength(2);
    expect(calls[0].name).toBe('test_function1');
    expect(calls[1].name).toBe('test_function2');
  });

  it('should handle tool calls with only arguments', () => {
    manager.addChunk('1', undefined, '{"arg":"value"}');
    const call = manager.getCall('1');
    expect(call?.name).toBe('');
    expect(call?.arguments).toBe('{"arg":"value"}');
  });

  it('should handle tool calls with only name', () => {
    manager.addChunk('1', 'test_function', undefined);
    const call = manager.getCall('1');
    expect(call?.name).toBe('test_function');
    expect(call?.arguments).toBe('');
  });
});