import { ResourceManager } from '../ResourceManager';

describe('ResourceManager', () => {
  let manager: ResourceManager;
  let mockCallback: jest.Mock;

  beforeEach(() => {
    jest.useFakeTimers();
    manager = new ResourceManager(1000); // 1 second timeout
    mockCallback = jest.fn();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('should provide an AbortSignal', () => {
    const signal = manager.getSignal();
    expect(signal).toBeDefined();
    expect(signal.aborted).toBe(false);
  });

  it('should set a timeout', () => {
    manager.setTimeout(mockCallback);
    expect(mockCallback).not.toHaveBeenCalled();

    jest.advanceTimersByTime(1000);
    expect(mockCallback).toHaveBeenCalledTimes(1);
  });

  it('should set a custom timeout duration', () => {
    manager.setTimeout(mockCallback, 2000);
    expect(mockCallback).not.toHaveBeenCalled();

    jest.advanceTimersByTime(1000);
    expect(mockCallback).not.toHaveBeenCalled();

    jest.advanceTimersByTime(1000);
    expect(mockCallback).toHaveBeenCalledTimes(1);
  });

  it('should cleanup resources', () => {
    manager.setTimeout(mockCallback);
    manager.cleanup();

    jest.advanceTimersByTime(1000);
    expect(mockCallback).not.toHaveBeenCalled();

    const signal = manager.getSignal();
    expect(signal.aborted).toBe(true);
  });

  it('should track multiple timeouts', () => {
    const mockCallback2 = jest.fn();
    manager.setTimeout(mockCallback);
    manager.setTimeout(mockCallback2, 2000);

    jest.advanceTimersByTime(1000);
    expect(mockCallback).toHaveBeenCalledTimes(1);
    expect(mockCallback2).not.toHaveBeenCalled();

    jest.advanceTimersByTime(1000);
    expect(mockCallback2).toHaveBeenCalledTimes(1);
  });

  it('should clear all timeouts on cleanup', () => {
    const mockCallback2 = jest.fn();
    manager.setTimeout(mockCallback);
    manager.setTimeout(mockCallback2);

    manager.cleanup();

    jest.advanceTimersByTime(1000);
    expect(mockCallback).not.toHaveBeenCalled();
    expect(mockCallback2).not.toHaveBeenCalled();
  });

  it('should check if aborted', () => {
    expect(manager.isAborted()).toBe(false);
    manager.cleanup();
    expect(manager.isAborted()).toBe(true);
  });

  it('should handle cleanup multiple times', () => {
    manager.cleanup();
    manager.cleanup(); // Should not throw
    expect(manager.isAborted()).toBe(true);
  });

  it('should not allow new timeouts after cleanup', () => {
    manager.cleanup();
    manager.setTimeout(mockCallback);

    jest.advanceTimersByTime(1000);
    expect(mockCallback).not.toHaveBeenCalled();
  });
});