import { logger } from '@/lib/logger';

export interface StreamMetrics {
  bytesProcessed: number;
  chunksReceived: number;
  toolCallsExecuted: number;
  errors: number;
  duration: number;
  startTime: number;
}

export class MetricsCollector {
  private metrics: StreamMetrics = {
    bytesProcessed: 0,
    chunksReceived: 0,
    toolCallsExecuted: 0,
    errors: 0,
    duration: 0,
    startTime: Date.now(),
  };

  private log = logger.child({ service: 'MetricsCollector' });

  incrementBytes(bytes: number): void {
    this.metrics.bytesProcessed += bytes;
  }

  incrementChunks(): void {
    this.metrics.chunksReceived++;
  }

  incrementToolCalls(): void {
    this.metrics.toolCallsExecuted++;
  }

  incrementErrors(): void {
    this.metrics.errors++;
  }

  getMetrics(): StreamMetrics {
    this.metrics.duration = Date.now() - this.metrics.startTime;
    return { ...this.metrics };
  }

  logMetrics(): void {
    const metrics = this.getMetrics();
    // Use a minimum duration of 1ms to prevent division by zero
    const safeDuration = Math.max(metrics.duration, 1);
    this.log.info('Stream metrics', {
      ...metrics,
      bytesPerSecond: (
        metrics.bytesProcessed /
        (safeDuration / 1000)
      ).toFixed(2),
      chunksPerSecond: (
        metrics.chunksReceived /
        (safeDuration / 1000)
      ).toFixed(2),
    });
  }

  reset(): void {
    this.metrics = {
      bytesProcessed: 0,
      chunksReceived: 0,
      toolCallsExecuted: 0,
      errors: 0,
      duration: 0,
      startTime: Date.now(),
    };
  }
}
