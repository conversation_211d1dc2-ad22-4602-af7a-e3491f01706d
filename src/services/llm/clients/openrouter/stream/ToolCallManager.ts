import { logger } from '@/lib/logger';
import { v4 as uuidv4 } from 'uuid';

interface ToolCallState {
  id: string;
  index: number;
  name: string;
  arguments: string;
  status: 'pending' | 'complete' | 'error';
  chunks: string[];
  createdAt: number;
}

export class ToolCallManager {
  private calls = new Map<string, ToolCallState>();
  private callsByIndex = new Map<number, string>(); // index -> id mapping
  private log = logger.child({ service: 'ToolCallManager' });
  private readonly maxToolCallAge = 5 * 60 * 1000; // 5 minutes

  private generateUniqueId(): string {
    return uuidv4();
  }

  private cleanupOldCalls(): void {
    const now = Date.now();
    for (const [id, state] of this.calls.entries()) {
      if (now - state.createdAt > this.maxToolCallAge) {
        this.log.warn('Cleaning up old tool call', {
          id,
          index: state.index,
          age: now - state.createdAt,
          status: state.status,
        });
        this.calls.delete(id);
        this.callsByIndex.delete(state.index);
      }
    }
  }

  addChunk(
    id: string | undefined,
    name: string | undefined,
    args: string | undefined,
    index?: number
  ): void {
    // Clean up old calls before processing new ones
    this.cleanupOldCalls();

    // If ID is explicitly empty string, ignore the chunk
    if (id === '') {
      this.log.warn('Received tool call chunk with empty ID');
      return;
    }

    let toolCallId: string;
    let toolCallIndex: number;

    // Determine the tool call ID and index
    if (id) {
      // If we have an ID, use it
      toolCallId = id;
      toolCallIndex = index ?? 0;
      // Update the index mapping
      this.callsByIndex.set(toolCallIndex, toolCallId);
    } else if (index !== undefined && this.callsByIndex.has(index)) {
      // If we have an index and we've seen this index before, use the existing ID
      toolCallId = this.callsByIndex.get(index)!;
      toolCallIndex = index;
    } else {
      // No ID and no known index, generate a new unique ID
      toolCallId = this.generateUniqueId();
      toolCallIndex = index ?? 0;
      this.callsByIndex.set(toolCallIndex, toolCallId);
    }

    let state = this.calls.get(toolCallId);
    if (!state) {
      state = {
        id: toolCallId,
        index: toolCallIndex,
        name: name || '',
        arguments: '',
        status: 'pending',
        chunks: [],
        createdAt: Date.now(),
      };
      this.calls.set(toolCallId, state);
    }

    if (name) {
      state.name = name;
    }

    if (args) {
      state.arguments += args;
      state.chunks.push(args);
    }
  }

  completeCall(id: string): ToolCallState | undefined {
    const state = this.calls.get(id);
    if (state) {
      state.status = 'complete';
      this.log.info(`Completed tool call: ${state.name}`, {
        id,
        index: state.index,
        argsLength: state.arguments.length,
        duration: Date.now() - state.createdAt,
      });
      this.calls.delete(id);
      this.callsByIndex.delete(state.index);
    }
    return state;
  }

  errorCall(id: string, error: Error): void {
    const state = this.calls.get(id);
    if (state) {
      state.status = 'error';
      this.log.error(`Tool call failed: ${state.name}`, {
        id,
        error: error.message,
        duration: Date.now() - state.createdAt,
      });
      // Don't delete the call immediately, keep it for inspection
    }
  }

  getPendingCalls(): ToolCallState[] {
    return Array.from(this.calls.values()).filter(
      (call) => call.status === 'pending'
    );
  }

  clear(): void {
    this.calls.clear();
    this.callsByIndex.clear();
  }

  getCall(id: string): ToolCallState | undefined {
    return this.calls.get(id);
  }
}
