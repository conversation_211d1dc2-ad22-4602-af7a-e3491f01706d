import { logger } from '@/lib/logger';

export class ResourceManager {
  private abortController = new AbortController();
  private timers: NodeJS.Timeout[] = [];
  private log = logger.child({ service: 'ResourceManager' });

  constructor(private readonly timeoutMs: number = 30000) {}

  getSignal(): AbortSignal {
    return this.abortController.signal;
  }

  setTimeout(callback: () => void, ms: number = this.timeoutMs): void {
    // Don't set new timeouts if already cleaned up
    if (this.abortController.signal.aborted) {
      this.log.warn('Attempted to set timeout after cleanup');
      return;
    }

    const timer = setTimeout(() => {
      this.log.warn('Operation timed out', { timeoutMs: ms });
      callback();
    }, ms);
    this.timers.push(timer);
  }

  cleanup(): void {
    this.log.info('Cleaning up resources');
    this.abortController.abort();
    this.timers.forEach(clearTimeout);
    this.timers = [];
  }

  isAborted(): boolean {
    return this.abortController.signal.aborted;
  }
}
