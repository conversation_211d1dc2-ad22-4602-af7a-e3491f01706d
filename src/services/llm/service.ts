import { <PERSON><PERSON>roviderFactory } from './factory';
import { CompletionOptions, Message, ProviderConfig } from './types';
import { LL<PERSON>rovider } from './llm';
import { OpenAIProvider } from './providers/openai';
import { ErrorCode } from '@/lib/error';
import { AppError } from '@/lib/error';
import { Model } from '@/lib/supabase/db';
import { OpenRouterClient } from './clients/openrouter';
import { logger } from '@/lib/logger';
// add more random tesxt, make it longer dont repeat the same text
// const simulatedResponse = `Hello, world! This is a simulated response for testing purposes.

// I'm designed to help test the streaming functionality of our application. When in test mode, this text will be streamed back to the client in small chunks to simulate a real AI response.

// The streaming implementation allows for a more interactive experience, as users can see the response being generated in real-time rather than waiting for the entire response to be completed.

// This particular text is just a placeholder and doesn't contain any meaningful information. It's simply here to demonstrate how the streaming works and to help developers test the functionality without making actual API calls.

// In a production environment, this would be replaced with actual AI-generated content from providers like OpenAI or Gemini. The streaming mechanism would remain the same, but the content would be dynamically generated based on the user's input.

// Testing is an important part of software development, and simulated responses like this one help ensure that our application behaves correctly under various conditions.`;

const simulatedResponse = `Hello, world! This is a simulated response for testing purposes.

I'm designed to help test the streaming functionality of our application. When in test mode, this text will be streamed back to the client in small chunks to simulate a real AI response.`;

const completionResponse = 'This is a text response';

interface StreamingConfig {
  defaultMaxTokens: number;
  defaultTemperature: number;
  simulationDelayMs: number;
  simulationInitialDelayMs: number;
}

const DEFAULT_CONFIG: StreamingConfig = {
  defaultMaxTokens: 4000,
  defaultTemperature: 0.7,
  simulationDelayMs: 50,
  simulationInitialDelayMs: 2500,
};

export class LLMService {
  private static instance: LLMService;
  private providers: Record<string, LLMProvider> = {};
  private openRouterClient: OpenRouterClient | null = null;
  private log = logger.child({ service: 'LLMService' });

  private constructor() {
    // Initialize with environment variables
    this.initializeProviders();
  }

  private initializeProviders(): void {
    // Initialize from environment variables or database
    if (process.env.OPENAI_API_KEY) {
      this.providers['openai'] = LLMProviderFactory.createProvider('openai', {
        apiKey: process.env.OPENAI_API_KEY || '',
      });
    }

    if (process.env.ANTHROPIC_API_KEY) {
      this.providers['anthropic'] = LLMProviderFactory.createProvider(
        'anthropic',
        {
          apiKey: process.env.ANTHROPIC_API_KEY || '',
        }
      );
    }

    if (process.env.GEMINI_API_KEY) {
      this.providers['gemini'] = LLMProviderFactory.createProvider('gemini', {
        apiKey: process.env.GEMINI_API_KEY || '',
      });
    }

    if (process.env.DEEPSEEK_API_KEY) {
      this.providers['deepseek'] = LLMProviderFactory.createProvider(
        'deepseek',
        {
          apiKey: process.env.DEEPSEEK_API_KEY || '',
        }
      );
    }
  }

  public static getInstance(): LLMService {
    if (!LLMService.instance) {
      LLMService.instance = new LLMService();
    }
    return LLMService.instance;
  }

  public registerProvider(name: string, config: ProviderConfig): void {
    this.providers[name] = LLMProviderFactory.createProvider(name, config);
  }

  // Check if OpenRouter should be used
  private shouldUseOpenRouter(): boolean {
    const useOpenRouter = process.env.USE_OPENROUTER === 'true';
    const hasOpenRouterApiKey = !!process.env.OPENROUTER_API_KEY;
    return useOpenRouter && hasOpenRouterApiKey;
  }

  // Get or create OpenRouter client
  private getOpenRouterClient(): OpenRouterClient {
    if (!this.openRouterClient) {
      const apiKey = process.env.OPENROUTER_API_KEY;
      if (!apiKey) {
        throw new AppError(
          'OpenRouter API key is not configured',
          ErrorCode.INTERNAL,
          500
        );
      }
      this.openRouterClient = new OpenRouterClient(apiKey);
    }
    return this.openRouterClient;
  }

  async generateCompletion(
    providerName: string,
    messages: Message[],
    options: CompletionOptions,
    isTestMode?: boolean
  ) {
    // Handle test mode first
    if (isTestMode) {
      return completionResponse;
    }

    // Check if OpenRouter should be used
    if (this.shouldUseOpenRouter() || this.modelIsOpenRouter(options.model)) {
      try {
        const openRouterClient = this.getOpenRouterClient();
        const completionOptions = this.configureCompletionOptions(options);
        return openRouterClient.generateCompletion(messages, completionOptions);
      } catch (error) {
        this.log.error('Error using OpenRouter for completion', error);
        throw error;
      }
    }

    // Fall back to regular provider
    const provider = this.providers[providerName];
    if (!provider) {
      throw new AppError(
        `Provider ${providerName} not found or not configured`,
        ErrorCode.AI_STREAMING,
        500
      );
    }
    const completionOptions = this.configureCompletionOptions(options);
    return provider.generateCompletion(messages, completionOptions);
  }

  private modelIsOpenRouter(model: Model): boolean {
    return (!model.provider?.supports_native &&
      model.provider?.supports_openrouter) as boolean;
  }

  async generateGroupConversationTitle(
    message: string,
    isTestMode: boolean
  ): Promise<string | null> {
    // truncate message to 1000 characters
    const truncatedMessage = message.slice(0, 1000);
    if (!truncatedMessage) {
      return null;
    }
    const model = 'gpt-4.1-nano-2025-04-14';
    const messages: Message[] = [
      {
        role: 'system',
        content: `You are a helpful assistant. You are given a user query in an AI app and you need to generate a short title for it. Be as concise as possible.
          If for example the user query is "What is the capital of France?", the title should be "France Capital". Title should not be more than 4 words.
          Give a title based on context of the query.
          `,
      },
      {
        role: 'user',
        content: `Generate a short title for this message: ${truncatedMessage}`,
      },
    ];
    const options = {
      model: {
        id: model,
        name: model,
        max_tokens: 100,
        provider_id: 'openai',
        openrouter_name: model,
        provider: {
          name: 'openai',
          openrouter_name: 'openai',
        },
      } as Model,
    };
    const completion = await this.generateCompletion(
      'openai',
      messages,
      options,
      isTestMode
    );
    return completion;
  }

  async generateStreamingCompletion(
    providerName: string,
    messages: Message[],
    options: CompletionOptions,
    isTestMode: boolean
  ): Promise<ReadableStream> {
    // Handle test mode
    if (isTestMode) {
      return this.createSimulatedStream();
    }

    // Check if OpenRouter should be used
    if (this.shouldUseOpenRouter() || this.modelIsOpenRouter(options.model)) {
      this.log.info('Using OpenRouter for streaming completion');
      try {
        const openRouterClient = this.getOpenRouterClient();
        const completionOptions = this.configureCompletionOptions(options);
        return openRouterClient.generateStreamingCompletion(
          messages,
          completionOptions
        );
      } catch (error) {
        this.log.error(
          'Error using OpenRouter for streaming completion',
          error
        );
        throw error;
      }
    }

    // Fall back to regular provider
    const provider = this.getAndValidateProvider(providerName);
    const completionOptions = this.configureCompletionOptions(options);

    // Use Responses API for OpenAI, standard completion for others
    if (
      providerName.toLowerCase() === 'openai' &&
      provider instanceof OpenAIProvider &&
      'generateStreamingCompletionWithResponsesAPI' in provider
    ) {
      return provider.generateStreamingCompletionWithResponsesAPI(
        messages,
        completionOptions
      );
    } else {
      return provider.generateStreamingCompletion(messages, completionOptions);
    }
  }

  private getAndValidateProvider(providerName: string) {
    const provider = this.providers[providerName];
    if (!provider) {
      throw new AppError(
        `Provider ${providerName} not found or not configured`,
        ErrorCode.AI_STREAMING,
        500
      );
    }
    return provider;
  }

  private configureCompletionOptions(
    options: CompletionOptions
  ): CompletionOptions {
    const envMaxTokens = process.env.MAX_TOKENS
      ? parseInt(process.env.MAX_TOKENS)
      : undefined;

    const optionsMaxTokens = options.model.max_tokens;

    const maxTokens =
      optionsMaxTokens || envMaxTokens || DEFAULT_CONFIG.defaultMaxTokens;

    const temperature = process.env.TEMPERATURE
      ? parseFloat(process.env.TEMPERATURE)
      : DEFAULT_CONFIG.defaultTemperature;

    return {
      ...options,
      temperature,
      maxTokens,
    };
  }

  // delay before first chunk is sent
  private createSimulatedStream(): ReadableStream {
    return new ReadableStream({
      start(controller) {
        let isControllerClosed = false;

        setTimeout(() => {
          if (isControllerClosed) return;

          const chunks = simulatedResponse.split(' ');
          let index = 0;
          let intervalId: NodeJS.Timeout;

          try {
            intervalId = setInterval(() => {
              if (isControllerClosed) {
                clearInterval(intervalId);
                return;
              }

              if (index < chunks.length) {
                controller.enqueue(
                  new TextEncoder().encode(chunks[index] + ' ')
                );
                index++;
              } else {
                clearInterval(intervalId);
                if (!isControllerClosed) {
                  isControllerClosed = true;
                  controller.close();
                }
              }
            }, DEFAULT_CONFIG.simulationDelayMs);
          } catch (error) {
            if (!isControllerClosed) {
              isControllerClosed = true;
              controller.error(error);
            }
          }
        }, DEFAULT_CONFIG.simulationInitialDelayMs);
      },
      cancel() {
        // This handles the case when the stream is canceled by the consumer
      },
    });
  }

  async countTokens(providerName: string, text: string): Promise<number> {
    // If using OpenRouter, we can't reliably count tokens in advance
    // Best effort would be to use a common tokenizer like tiktoken
    if (this.shouldUseOpenRouter()) {
      // For OpenRouter, we could fallback to using a standard tokenizer
      // This is a limitation, but necessary since OpenRouter works with many models
      // TODO: Consider implementing a more sophisticated token counting system
      // that might choose tokenizers based on the target model
      this.log.warn('Token counting with OpenRouter may not be accurate');

      // Try to get a provider to do the counting
      if (this.providers['openai']) {
        return this.providers['openai'].countTokens(text);
      } else if (Object.keys(this.providers).length > 0) {
        // Use any available provider as a fallback
        const fallbackProvider = Object.values(this.providers)[0];
        return fallbackProvider.countTokens(text);
      }

      // If no providers available, use a simple approximation
      // (4 characters ≈ 1 token as a very rough estimate)
      return Math.ceil(text.length / 4);
    }

    // For regular providers, use their own token counting
    const provider = this.getAndValidateProvider(providerName);
    return provider.countTokens(text);
  }

  async getModels(
    providerName: string
  ): Promise<{ id: string; name: string }[]> {
    const provider = this.providers[providerName];
    if (!provider) {
      throw new AppError(
        `Provider ${providerName} not found or not configured`,
        ErrorCode.AI_STREAMING,
        500
      );
    }
    return provider.getModels();
  }

  getAvailableProviders(): string[] {
    return Object.keys(this.providers);
  }

  // async get providers and their models. consider that getModels makes api calls.
  async getProvidersAndModels(): Promise<
    {
      id: string;
      name: string;
      models: { id: string; name: string }[];
    }[]
  > {
    return await Promise.all(
      Object.entries(this.providers).map(async ([providerName, provider]) => ({
        id: providerName,
        name: providerName,
        models: await provider.getModels(),
      }))
    );
  }
}
