import { logger } from '@/lib/logger';
import { <PERSON><PERSON><PERSON> } from 'buffer'; // Needed for base64 encoding in Node.js

const log = logger.child({ module: 'llm-utils' });

/**
 * Fetches content from a URL, encodes it to base64, and determines the MIME type.
 *
 * @param url The URL to fetch content from.
 * @param mimeTypeHint An optional hint for the MIME type if the Content-Type header is missing or incorrect.
 * @returns A promise resolving to an object containing the base64 encoded string and the determined MIME type, or null if an error occurs.
 */
export async function fetchAndEncodeFile(
  url: string,
  mimeTypeHint?: string
): Promise<{ base64String: string; mimeType: string } | null> {
  try {
    log.info(`Fetching file from URL: ${url}`);
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status} for ${url}`);
    }

    // Determine MIME type: Use header, then hint, then default
    const fetchedMimeType =
      response.headers.get('content-type')?.split(';')[0] ?? // Get type before potential charset
      mimeTypeHint ??
      'application/octet-stream'; // Default if unknown

    const arrayBuffer = await response.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    const base64String = buffer.toString('base64');

    log.info(`Successfully fetched and encoded content from ${url} as ${fetchedMimeType}`);
    return { base64String, mimeType: fetchedMimeType };
  } catch (error) {
    log.error(`Error fetching/encoding content from ${url}:`, error);
    return null; // Indicate failure
  }
}