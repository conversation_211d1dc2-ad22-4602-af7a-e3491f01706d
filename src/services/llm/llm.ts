import { Message, ProviderConfig, CompletionOptions } from './types';

export abstract class LL<PERSON><PERSON>ider {
  protected config: ProviderConfig;

  constructor(config: ProviderConfig) {
    this.config = config;
  }

  abstract generateCompletion(
    messages: Message[],
    options: CompletionOptions
  ): Promise<string | null>;

  abstract generateStreamingCompletion(
    messages: Message[],
    options: CompletionOptions
  ): Promise<ReadableStream>;

  abstract countTokens(text: string): Promise<number>;

  abstract getModels(): Promise<{ id: string; name: string }[]>;

  protected validateApiKey(): void {
    if (!this.config.apiKey) {
      throw new Error(`${this.constructor.name} API key is not set`);
    }
  }
}
