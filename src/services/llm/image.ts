import { logger } from '@/lib/logger';
import { AppError, ErrorCode } from '@/lib/error';

export type StabilityService = 'core' | 'ultra';

export interface ImageGenerationOptions {
  prompt: string;
  aspectRatio?:
    | '16:9'
    | '1:1'
    | '21:9'
    | '2:3'
    | '3:2'
    | '4:5'
    | '5:4'
    | '9:16'
    | '9:21';
  negativePrompt?: string;
  seed?: number;
  stylePreset?:
    | '3d-model'
    | 'analog-film'
    | 'anime'
    | 'cinematic'
    | 'comic-book'
    | 'digital-art'
    | 'enhance'
    | 'fantasy-art'
    | 'isometric'
    | 'line-art'
    | 'low-poly'
    | 'modeling-compound'
    | 'neon-punk'
    | 'origami'
    | 'photographic'
    | 'pixel-art'
    | 'tile-texture';
  outputFormat?: 'jpeg' | 'png' | 'webp';
  service?: StabilityService;
  // Ultra-specific options
  image?: Buffer;
  strength?: number;
}

export interface ImageGenerationResult {
  base64: string;
  width: number;
  height: number;
  safetyScore?: number;
  providerJobId?: string;
  service: StabilityService;
}

export interface StabilityImageResponse {
  image: string; // base64 encoded image
  finish_reason: string;
  seed: number;
}

export interface StabilityErrorResponse {
  errors: Array<{
    id: string;
    name: string;
    message: string;
  }>;
}

export class ImageGenerationService {
  private static instance: ImageGenerationService;
  private log = logger.child({ service: 'ImageGenerationService' });

  private constructor() {}

  public static getInstance(): ImageGenerationService {
    if (!ImageGenerationService.instance) {
      ImageGenerationService.instance = new ImageGenerationService();
    }
    return ImageGenerationService.instance;
  }

  private validateEnvironment(): void {
    if (!process.env.STABILITY_API_KEY) {
      throw new AppError(
        'Stability API key is not configured',
        ErrorCode.INTERNAL,
        500
      );
    }
  }

  private getServiceEndpoint(service: StabilityService): string {
    const baseUrl = process.env.STABILITY_BASE || 'https://api.stability.ai';
    switch (service) {
      case 'core':
        return `${baseUrl}/v2beta/stable-image/generate/core`;
      case 'ultra':
        return `${baseUrl}/v2beta/stable-image/generate/ultra`;
      default:
        throw new AppError(
          `Invalid service: ${service}. Must be 'core' or 'ultra'`,
          ErrorCode.INVALID_REQUEST,
          400
        );
    }
  }

  private createFormData(options: ImageGenerationOptions): FormData {
    const formData = new FormData();

    // Required field
    formData.append('prompt', options.prompt);

    // Optional fields
    if (options.aspectRatio) {
      formData.append('aspect_ratio', options.aspectRatio);
    }

    if (options.negativePrompt) {
      formData.append('negative_prompt', options.negativePrompt);
    }

    if (options.seed !== undefined) {
      formData.append('seed', options.seed.toString());
    }

    if (options.stylePreset) {
      formData.append('style_preset', options.stylePreset);
    }

    if (options.outputFormat) {
      formData.append('output_format', options.outputFormat);
    }

    // Ultra-specific fields
    if (options.service === 'ultra') {
      if (options.image) {
        formData.append('image', new Blob([options.image]), 'image.png');

        // Strength is required when image is provided
        if (options.strength !== undefined) {
          formData.append('strength', options.strength.toString());
        } else {
          throw new AppError(
            'Strength parameter is required when image is provided for Ultra service',
            ErrorCode.INVALID_REQUEST,
            400
          );
        }
      }
    }

    return formData;
  }

  private getImageDimensions(
    aspectRatio: string = '1:1',
    service: StabilityService
  ): { width: number; height: number } {
    // Core: 1.5 megapixels, Ultra: 1 megapixel
    const totalPixels = service === 'core' ? 1500000 : 1000000;

    const ratioMap: Record<string, { width: number; height: number }> = {
      '1:1': { width: Math.sqrt(totalPixels), height: Math.sqrt(totalPixels) },
      '16:9': {
        width: Math.sqrt((totalPixels * 16) / 9),
        height: Math.sqrt((totalPixels * 9) / 16),
      },
      '9:16': {
        width: Math.sqrt((totalPixels * 9) / 16),
        height: Math.sqrt((totalPixels * 16) / 9),
      },
      '21:9': {
        width: Math.sqrt((totalPixels * 21) / 9),
        height: Math.sqrt((totalPixels * 9) / 21),
      },
      '9:21': {
        width: Math.sqrt((totalPixels * 9) / 21),
        height: Math.sqrt((totalPixels * 21) / 9),
      },
      '2:3': {
        width: Math.sqrt((totalPixels * 2) / 3),
        height: Math.sqrt((totalPixels * 3) / 2),
      },
      '3:2': {
        width: Math.sqrt((totalPixels * 3) / 2),
        height: Math.sqrt((totalPixels * 2) / 3),
      },
      '4:5': {
        width: Math.sqrt((totalPixels * 4) / 5),
        height: Math.sqrt((totalPixels * 5) / 4),
      },
      '5:4': {
        width: Math.sqrt((totalPixels * 5) / 4),
        height: Math.sqrt((totalPixels * 4) / 5),
      },
    };

    const dimensions = ratioMap[aspectRatio];
    if (!dimensions) {
      // Default to 1:1 if aspect ratio is not found
      return ratioMap['1:1'];
    }

    return {
      width: Math.round(dimensions.width),
      height: Math.round(dimensions.height),
    };
  }

  async generateImage(
    options: ImageGenerationOptions
  ): Promise<ImageGenerationResult> {
    this.validateEnvironment();

    const service = options.service || 'core';
    const endpoint = this.getServiceEndpoint(service);
    const { width, height } = this.getImageDimensions(
      options.aspectRatio,
      service
    );

    this.log.info('Generating image with Stability AI', {
      prompt: options.prompt.substring(0, 100),
      service,
      aspectRatio: options.aspectRatio || '1:1',
      outputFormat: options.outputFormat || 'png',
    });

    const formData = this.createFormData(options);

    let attempts = 0;
    const maxRetries = 2;

    while (attempts <= maxRetries) {
      try {
        const response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${process.env.STABILITY_API_KEY}`,
            Accept: 'application/json',
          },
          body: formData,
        });

        if (!response.ok) {
          const errorText = await response.text();
          this.log.error('Stability AI API error', {
            status: response.status,
            error: errorText,
            service,
          });

          let errorMessage = `Stability AI API error (${response.status})`;

          try {
            const errorData = JSON.parse(errorText) as StabilityErrorResponse;
            if (errorData.errors && errorData.errors.length > 0) {
              errorMessage = errorData.errors[0].message || errorMessage;
            }
          } catch {
            // Keep the default error message if JSON parsing fails
          }

          // Handle specific error codes
          if (response.status === 403) {
            throw new AppError(
              'Content flagged by moderation system',
              ErrorCode.INVALID_REQUEST,
              403
            );
          }

          if (response.status === 413) {
            throw new AppError(
              'Request too large (>10MB)',
              ErrorCode.INVALID_REQUEST,
              413
            );
          }

          if (response.status === 429) {
            throw new AppError(
              'Rate limit exceeded (>150 requests in 10 seconds)',
              ErrorCode.INVALID_REQUEST,
              429
            );
          }

          if (response.status >= 500 && attempts < maxRetries) {
            attempts++;
            const delay = attempts * 1000; // 1s, 2s delays
            this.log.warn(`Retrying image generation after ${delay}ms`, {
              attempt: attempts,
            });
            await new Promise((resolve) => setTimeout(resolve, delay));
            continue;
          }

          throw new AppError(
            errorMessage,
            ErrorCode.AI_STREAMING,
            response.status
          );
        }

        const data = (await response.json()) as StabilityImageResponse;

        if (!data.image) {
          throw new AppError(
            'No image returned from Stability AI',
            ErrorCode.AI_STREAMING,
            500
          );
        }

        this.log.info('Image generated successfully', {
          finishReason: data.finish_reason,
          seed: data.seed,
          service,
          aspectRatio: options.aspectRatio || '1:1',
        });

        return {
          base64: data.image,
          width,
          height,
          providerJobId: data.seed?.toString(),
          service,
        };
      } catch (error) {
        if (
          attempts === maxRetries ||
          !(error instanceof AppError && error.statusCode >= 500)
        ) {
          throw error;
        }
        attempts++;
        const delay = attempts * 1000;
        this.log.warn(`Retrying image generation after ${delay}ms`, {
          attempt: attempts,
          error,
        });
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }

    // This shouldn't be reached, but TypeScript needs it
    throw new AppError(
      'Failed to generate image after retries',
      ErrorCode.AI_STREAMING,
      500
    );
  }
}
