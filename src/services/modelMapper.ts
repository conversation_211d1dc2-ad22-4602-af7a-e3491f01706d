import { logger } from '@/lib/logger';
import {
  Model,
  ModelArchitecture,
  ModelCapabilities,
  ModelPricing,
  ProviderSpecificData,
} from '@/lib/supabase/types';

const log = logger.child({ module: 'ModelMapper' });

// Interface for OpenRouter model data
export interface OpenRouterModel {
  id: string;
  name: string;
  created: number;
  description: string;
  context_length: number;
  architecture: {
    modality?: string;
    input_modalities: string[];
    output_modalities: string[];
    tokenizer: string;
    instruct_type?: string | null;
  };
  pricing: {
    prompt: string;
    completion: string;
    image?: string;
    request?: string;
    web_search?: string;
    internal_reasoning?: string;
    input_cache_read?: string;
    input_cache_write?: string;
  };
  top_provider: {
    context_length: number;
    max_completion_tokens?: number;
    is_moderated: boolean;
  };
  per_request_limits: Record<string, unknown> | null;
  supported_parameters: string[];
  hugging_face_id?: string;
}

export class ModelMapper {
  /**
   * Maps an OpenRouter model to our internal model structure
   */
  static mapOpenRouterModelToInternal(
    openRouterModel: OpenRouterModel,
    existingModel?: Partial<Model>
  ): Partial<Model> {
    try {
      // Extract model ID from the full path (e.g., "google/gemini-2.5-pro-preview" -> "gemini-2.5-pro-preview")
      const modelNameParts = openRouterModel.id.split('/');
      const modelName = modelNameParts[modelNameParts.length - 1];

      // Map capabilities based on supported parameters and modalities
      const capabilities: ModelCapabilities = {
        file_upload:
          openRouterModel.architecture.input_modalities.includes('file'),
        web_search: openRouterModel.supported_parameters.includes('web_search'),
        visible_by_default: false,
        image_generation:
          openRouterModel.supported_parameters.includes('tools'),
        code_generation: true, // Assume all models support code generation
        function_calling:
          openRouterModel.supported_parameters.includes('tools') ||
          openRouterModel.supported_parameters.includes('tool_choice'),
        reasoning: openRouterModel.supported_parameters.includes('reasoning'),
        structured_output:
          openRouterModel.supported_parameters.includes('structured_outputs') ||
          openRouterModel.supported_parameters.includes('response_format'),
      };

      return {
        ...existingModel,
        name: modelName,
        display_name: openRouterModel.name,
        description: openRouterModel.description,
        openrouter_name: modelName,
        context_length: openRouterModel.context_length,
        capabilities: capabilities as ModelCapabilities,
        architecture: openRouterModel.architecture as ModelArchitecture,
        pricing: openRouterModel.pricing as ModelPricing,
        supported_parameters: openRouterModel.supported_parameters,
        provider_specific_data: {
          max_completion_tokens:
            openRouterModel.top_provider.max_completion_tokens,
          is_moderated: openRouterModel.top_provider.is_moderated,
          per_request_limits: openRouterModel.per_request_limits,
          hugging_face_id: openRouterModel.hugging_face_id,
        } as ProviderSpecificData,
        last_synced: new Date().toISOString(),
      };
    } catch (error) {
      log.error('Error mapping OpenRouter model', {
        model: openRouterModel.id,
        error,
      });
      throw error;
    }
  }

  /**
   * Maps our internal model structure to a format suitable for API responses
   */
  static mapInternalModelToApiResponse(model: Model) {
    return {
      id: model.id,
      name: model.name,
      display_name: model.display_name,
      description: model.description || '',
      provider: {
        id: model.provider?.id,
        name: model.provider?.name,
        display_name: model.provider?.display_name,
      },
      tier: model.tier,
      capabilities: model.capabilities || {
        file_upload: model.allows_file_upload,
        web_search: model.allows_search,
        visible_by_default: model.is_visible_by_default,
      },
      context_length: model.context_length,
      architecture: model.architecture,
      pricing: model.pricing,
      supported_parameters: model.supported_parameters || [],
    };
  }
}
