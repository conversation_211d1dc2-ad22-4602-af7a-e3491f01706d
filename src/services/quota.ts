import { createClient } from '@/utils/supabase/server';
import { logger } from '@/lib/logger';
import { AppError, ErrorCode } from '@/lib/error';
import {
  SubscriptionPlan,
  SUBSCRIPTION_PLANS,
  SubscriptionData,
} from '@/lib/supabase/types';
import { User } from '@supabase/supabase-js';

export interface QuotaLimits {
  imagesPerDay: number;
  maxImageSize: '512x512' | '768x768' | '1024x1024';
}

export interface QuotaUsage {
  imagesUsed: number;
  imagesRemaining: number;
  maxImages: number;
  resetDate: string;
}

export interface ComparisonQuotaUsage {
  comparisonsUsed: number;
  comparisonsRemaining: number;
  maxComparisons: number;
  resetDate: string;
}

export interface TokenQuotaStatus {
  canUse: boolean;
  tokensUsed: number;
  tokensRemaining: number;
  tokenQuota: number | null;
  resetDate: string;
}

export interface ModelRecommendation {
  modelId: string | null;
  reason: string;
  options?: string[];
}

export class QuotaService {
  private static instance: QuotaService;
  private log = logger.child({ service: 'QuotaService' });

  private constructor() {}

  public static getInstance(): QuotaService {
    if (!QuotaService.instance) {
      QuotaService.instance = new QuotaService();
    }
    return QuotaService.instance;
  }

  private getQuotaLimits(plan: SubscriptionPlan): QuotaLimits {
    const quotaMap: Record<SubscriptionPlan, QuotaLimits> = {
      free: {
        imagesPerDay: 5,
        maxImageSize: '512x512',
      },
      starter: {
        imagesPerDay: 15,
        maxImageSize: '1024x1024',
      },
      premium: {
        imagesPerDay: 50,
        maxImageSize: '1024x1024',
      },
    };

    return quotaMap[plan];
  }

  private getTodayDateString(): string {
    return new Date().toISOString().split('T')[0];
  }

  async getUserSubscriptionPlan(userId: string): Promise<SubscriptionPlan> {
    try {
      const supabase = await createClient();

      const { data: subscription } = await supabase
        .from('subscriptions')
        .select('plan, status')
        .eq('user_id', userId)
        .single();

      if (!subscription || subscription.status !== 'active') {
        return 'free';
      }

      return subscription.plan;
    } catch (error) {
      this.log.warn('Failed to get user subscription, defaulting to free', {
        userId,
        error,
      });
      return 'free';
    }
  }

  async checkImageQuota(
    user: User,
    imageSize: '512x512' | '768x768' | '1024x1024'
  ): Promise<{ allowed: boolean; usage: QuotaUsage; reason?: string }> {
    try {
      const supabase = await createClient();
      const today = this.getTodayDateString();

      // Get user's subscription plan
      const userPlan = await this.getUserSubscriptionPlan(user.id);
      const quotaLimits = this.getQuotaLimits(userPlan);

      // Check if requested image size is allowed for user's plan
      const requestedSizeValue = parseInt(imageSize.split('x')[0]);
      const maxSizeValue = parseInt(quotaLimits.maxImageSize.split('x')[0]);

      if (requestedSizeValue > maxSizeValue) {
        return {
          allowed: false,
          usage: {
            imagesUsed: 0,
            imagesRemaining: 0,
            maxImages: quotaLimits.imagesPerDay,
            resetDate: today,
          },
          reason: `Image size ${imageSize} not available for ${userPlan} plan. Maximum allowed: ${quotaLimits.maxImageSize}`,
        };
      }

      // Get current usage for today
      const { data: dailyUsage } = await supabase
        .from('user_daily_usage')
        .select('image_count')
        .eq('user_id', user.id)
        .eq('date', today)
        .single();

      const currentImageCount = dailyUsage?.image_count || 0;
      const imagesRemaining = Math.max(
        0,
        quotaLimits.imagesPerDay - currentImageCount
      );

      const usage: QuotaUsage = {
        imagesUsed: currentImageCount,
        imagesRemaining,
        maxImages: quotaLimits.imagesPerDay,
        resetDate: today,
      };

      if (currentImageCount >= quotaLimits.imagesPerDay) {
        return {
          allowed: false,
          usage,
          reason: `Daily image limit of ${quotaLimits.imagesPerDay} reached for ${userPlan} plan`,
        };
      }

      return { allowed: true, usage };
    } catch (error) {
      this.log.error('Error checking image quota', { userId: user.id, error });
      throw new AppError(
        'Failed to check image quota',
        ErrorCode.INTERNAL,
        500
      );
    }
  }

  async incrementImageUsage(userId: string): Promise<void> {
    try {
      const supabase = await createClient();
      const today = this.getTodayDateString();

      // Use the existing increment function for atomicity
      const { error } = await supabase.rpc('increment_user_image_count', {
        p_user_id: userId,
        p_date: today,
      });

      if (error) {
        this.log.error('Error incrementing image usage', { userId, error });
        throw new AppError(
          'Failed to update image usage',
          ErrorCode.INTERNAL,
          500
        );
      }

      this.log.info('Image usage incremented', { userId, date: today });
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      this.log.error('Error incrementing image usage', { userId, error });
      throw new AppError(
        'Failed to update image usage',
        ErrorCode.INTERNAL,
        500
      );
    }
  }

  async decrementImageUsage(userId: string): Promise<void> {
    try {
      const supabase = await createClient();
      const today = this.getTodayDateString();

      // Decrement the usage (for refund scenarios)
      const { error } = await supabase.rpc('decrement_user_image_count', {
        p_user_id: userId,
        p_date: today,
      });

      if (error) {
        this.log.error('Error decrementing image usage', { userId, error });
        throw new AppError(
          'Failed to refund image usage',
          ErrorCode.INTERNAL,
          500
        );
      }

      this.log.info('Image usage decremented (refunded)', {
        userId,
        date: today,
      });
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      this.log.error('Error decrementing image usage', { userId, error });
      throw new AppError(
        'Failed to refund image usage',
        ErrorCode.INTERNAL,
        500
      );
    }
  }

  async getQuotaUsage(userId: string): Promise<QuotaUsage> {
    try {
      const supabase = await createClient();
      const today = this.getTodayDateString();

      // Get user's subscription plan
      const userPlan = await this.getUserSubscriptionPlan(userId);
      const quotaLimits = this.getQuotaLimits(userPlan);

      // Get current usage for today
      const { data: dailyUsage } = await supabase
        .from('user_daily_usage')
        .select('image_count')
        .eq('user_id', userId)
        .eq('date', today)
        .single();

      const currentImageCount = dailyUsage?.image_count || 0;
      const imagesRemaining = Math.max(
        0,
        quotaLimits.imagesPerDay - currentImageCount
      );

      return {
        imagesUsed: currentImageCount,
        imagesRemaining,
        maxImages: quotaLimits.imagesPerDay,
        resetDate: today,
      };
    } catch (error) {
      this.log.error('Error getting quota usage', { userId, error });
      throw new AppError('Failed to get quota usage', ErrorCode.INTERNAL, 500);
    }
  }

  private getCurrentYearMonth(): string {
    return new Date().toISOString().slice(0, 7); // '2024-03' format
  }

  private getMonthlyResetDate(): string {
    const now = new Date();
    const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);
    return nextMonth.toISOString().split('T')[0];
  }

  async getSubscription(userId: string): Promise<SubscriptionData> {
    const supabase = await createClient();
    const { data: subscription } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .single();

    return subscription || { plan: 'free', status: 'active' };
  }

  async getMonthlyTokenUsage(userId: string) {
    const supabase = await createClient();
    const yearMonth = this.getCurrentYearMonth();

    const { data: usage } = await supabase
      .from('user_monthly_usage')
      .select('*')
      .eq('user_id', userId)
      .eq('year_month', yearMonth)
      .single();

    return (
      usage || { tokens_used: 0, image_credits_used: 0, comparison_count: 0 }
    );
  }

  async checkTokenQuota(userId: string): Promise<TokenQuotaStatus> {
    try {
      const subscription = await this.getSubscription(userId);
      const usage = await this.getMonthlyTokenUsage(userId);

      if (subscription.plan === 'free') {
        // Free tier uses message limits, not token limits
        return {
          canUse: true, // Will be checked at message level
          tokensUsed: 0,
          tokensRemaining: 0,
          tokenQuota: null,
          resetDate: this.getMonthlyResetDate(),
        };
      }

      const quota = SUBSCRIPTION_PLANS[subscription.plan].tokenQuota || 0;
      const tokensUsed = usage.tokens_used || 0;
      const tokensRemaining = Math.max(0, quota - tokensUsed);

      return {
        canUse: tokensUsed < quota,
        tokensUsed,
        tokensRemaining,
        tokenQuota: quota,
        resetDate: this.getMonthlyResetDate(),
      };
    } catch (error) {
      this.log.error('Error checking token quota', { userId, error });
      throw new AppError(
        'Failed to check token quota',
        ErrorCode.INTERNAL,
        500
      );
    }
  }

  async handleTokenOverage(userId: string): Promise<ModelRecommendation> {
    try {
      const subscription = await this.getSubscription(userId);

      if (subscription.plan === 'starter') {
        // Fallback to GPT-3.5 for starter tier
        return {
          modelId: 'gpt-4o-mini',
          reason: 'quota_exceeded',
        };
      }

      if (subscription.plan === 'premium') {
        // Offer auto-purchase or manual top-up
        return {
          modelId: null,
          reason: 'overage_available',
          options: ['auto_purchase_100k', 'manual_topup'],
        };
      }

      return {
        modelId: null,
        reason: 'upgrade_required',
      };
    } catch (error) {
      this.log.error('Error handling token overage', { userId, error });
      throw new AppError(
        'Failed to handle token overage',
        ErrorCode.INTERNAL,
        500
      );
    }
  }

  async trackTokenUsage(
    userId: string,
    tokens: number,
    modelId: string
  ): Promise<void> {
    try {
      const supabase = await createClient();
      const yearMonth = this.getCurrentYearMonth();

      // Track monthly token usage
      const { error } = await supabase.rpc('increment_monthly_token_usage', {
        p_user_id: userId,
        p_year_month: yearMonth,
        p_tokens: tokens,
      });

      if (error) {
        this.log.error('Error tracking token usage', { userId, tokens, error });
        throw new AppError(
          'Failed to track token usage',
          ErrorCode.INTERNAL,
          500
        );
      }

      this.log.info('Token usage tracked', {
        userId,
        tokens,
        modelId,
        yearMonth,
      });
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      this.log.error('Error tracking token usage', { userId, tokens, error });
      throw new AppError(
        'Failed to track token usage',
        ErrorCode.INTERNAL,
        500
      );
    }
  }

  async checkComparisonQuota(
    userId: string
  ): Promise<{ canUse: boolean; count: number; limit: number }> {
    try {
      const subscription = await this.getSubscription(userId);

      if (subscription.plan !== 'premium') {
        return { canUse: false, count: 0, limit: 0 };
      }

      const supabase = await createClient();
      const today = this.getTodayDateString();

      // Get current daily comparison usage
      const { data: dailyUsage } = await supabase
        .from('user_daily_usage')
        .select('comparison_count')
        .eq('user_id', userId)
        .eq('date', today)
        .single();

      const comparisonCount = dailyUsage?.comparison_count || 0;
      const limit = 50; // 50 comparisons per day for premium

      return {
        canUse: comparisonCount < limit,
        count: comparisonCount,
        limit,
      };
    } catch (error) {
      this.log.error('Error checking comparison quota', { userId, error });
      throw new AppError(
        'Failed to check comparison quota',
        ErrorCode.INTERNAL,
        500
      );
    }
  }

  async incrementComparisonUsage(userId: string): Promise<void> {
    try {
      const supabase = await createClient();
      const today = this.getTodayDateString();

      const { data, error } = await supabase.rpc('increment_user_comparison_count', {
        p_user_id: userId as unknown as string, // Convert to UUID
        p_date: today,
      });

      if (error) {
        this.log.error('Error incrementing comparison usage', {
          userId,
          error,
        });
        throw new AppError(
          'Failed to track comparison usage',
          ErrorCode.INTERNAL,
          500
        );
      }

      if (!data || !data[0]?.success) {
        this.log.warn('No rows affected when incrementing comparison usage', {
          userId,
          date: today,
          data,
        });
        throw new AppError(
          'Failed to track comparison usage - no rows affected',
          ErrorCode.INTERNAL,
          500
        );
      }

      this.log.info('Comparison usage incremented', { userId, date: today });
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      this.log.error('Error incrementing comparison usage', { userId, error });
      throw new AppError(
        'Failed to track comparison usage',
        ErrorCode.INTERNAL,
        500
      );
    }
  }

  async decrementComparisonUsage(userId: string): Promise<void> {
    try {
      const supabase = await createClient();
      const today = this.getTodayDateString();

      const { data, error } = await supabase.rpc('decrement_user_comparison_count', {
        p_user_id: userId as unknown as string, // Convert to UUID
        p_date: today,
      });

      if (error) {
        this.log.error('Error decrementing comparison usage', {
          userId,
          error,
        });
        throw new AppError(
          'Failed to refund comparison usage',
          ErrorCode.INTERNAL,
          500
        );
      }

      if (!data || !data[0]?.success) {
        this.log.warn('No rows affected when decrementing comparison usage', {
          userId,
          date: today,
          data,
        });
        throw new AppError(
          'Failed to refund comparison usage - no rows affected',
          ErrorCode.INTERNAL,
          500
        );
      }

      this.log.info('Comparison usage decremented (refunded)', {
        userId,
        date: today,
      });
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      this.log.error('Error decrementing comparison usage', { userId, error });
      throw new AppError(
        'Failed to refund comparison usage',
        ErrorCode.INTERNAL,
        500
      );
    }
  }

  async getComparisonQuotaUsage(userId: string): Promise<ComparisonQuotaUsage> {
    try {
      const subscription = await this.getSubscription(userId);

      if (subscription.plan !== 'premium') {
        return {
          comparisonsUsed: 0,
          comparisonsRemaining: 0,
          maxComparisons: 0,
          resetDate: this.getTodayDateString(),
        };
      }

      const supabase = await createClient();
      const today = this.getTodayDateString();

      // Get current daily comparison usage
      const { data: dailyUsage } = await supabase
        .from('user_daily_usage')
        .select('comparison_count')
        .eq('user_id', userId)
        .eq('date', today)
        .single();

      const currentComparisonCount = dailyUsage?.comparison_count || 0;
      const maxComparisons = 50; // 50 comparisons per day for premium
      const comparisonsRemaining = Math.max(
        0,
        maxComparisons - currentComparisonCount
      );

      return {
        comparisonsUsed: currentComparisonCount,
        comparisonsRemaining,
        maxComparisons,
        resetDate: today,
      };
    } catch (error) {
      this.log.error('Error getting comparison quota usage', { userId, error });
      throw new AppError(
        'Failed to get comparison quota usage',
        ErrorCode.INTERNAL,
        500
      );
    }
  }
}
