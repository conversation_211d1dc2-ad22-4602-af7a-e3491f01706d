// services/anonymousSession.ts
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';
import { v4 as uuidv4 } from 'uuid';

export class AnonymousSessionService {
  private static instance: AnonymousSessionService;
  private supabaseClient;


  private constructor() {
    this.supabaseClient = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY! // Using service role to bypass RLS
    );
  }

  public static getInstance(): AnonymousSessionService {
    if (!AnonymousSessionService.instance) {
      AnonymousSessionService.instance = new AnonymousSessionService();
    }
    return AnonymousSessionService.instance;
  }

  // Get or create a session
  async getOrCreateSession(sessionId?: string): Promise<string> {
    // If no sessionId provided, check cookies or create new one
    if (!sessionId) {
      const cookieStore = await cookies();
      sessionId = cookieStore.get('anonymous_session_id')?.value;

      if (!sessionId) {
        sessionId = uuidv4();
        // Set cookie in middleware or API response
      }
    }

    // Check if session exists in DB
    const { data } = await this.supabaseClient
      .from('anonymous_sessions')
      .select('id')
      .eq('session_id', sessionId)
      .single();

    if (!data) {
      // Create new session
      await this.supabaseClient
        .from('anonymous_sessions')
        .insert({ session_id: sessionId, message_count: 0 });
    }

    return sessionId;
  }

  // Increment message count and check limit
  async incrementMessageCount(sessionId: string): Promise<{ canContinue: boolean, messageCount: number }> {
    // Get current count
    const { data: session } = await this.supabaseClient
      .from('anonymous_sessions')
      .select('message_count')
      .eq('session_id', sessionId)
      .single();

    if (!session) {
      throw new Error('Session not found');
    }

    const newCount = session.message_count + 1;

    // Update count
    await this.supabaseClient
      .from('anonymous_sessions')
      .update({ message_count: newCount })
      .eq('session_id', sessionId);

    return {
      canContinue: newCount <= 25, // 5 message limit
      messageCount: newCount
    };
  }

  // Get session DB ID from session ID string
  async getSessionDbId(sessionId: string): Promise<string | null> {
    const { data } = await this.supabaseClient
      .from('anonymous_sessions')
      .select('id')
      .eq('session_id', sessionId)
      .single();

    return data?.id || null;
  }

  async getAnonymousConversations(sessionDbId: string) {
    const { data, error } = await this.supabaseClient
      .from('conversations')
      .select('*')
      .eq('anonymous_session_id', sessionDbId)
      .eq('is_active', true)
      .eq('comparison_index', 0)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  }

  // Transfer anonymous conversation to user after login
  async transferToUser(sessionId: string, userId: string): Promise<void> {
    const sessionDbId = await this.getSessionDbId(sessionId);

    if (!sessionDbId) return;

    // Update all conversations from this anonymous session to the logged-in user
    await this.supabaseClient
      .from('conversations')
      .update({
        user_id: userId,
        anonymous_session_id: null
      })
      .eq('anonymous_session_id', sessionDbId);
  }
}