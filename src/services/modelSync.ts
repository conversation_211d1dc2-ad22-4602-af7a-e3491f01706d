import { DatabaseService, Model } from '@/lib/supabase/db';
import { createClient } from '@/utils/supabase/server';
import { logger } from '@/lib/logger';
import { AppError, ErrorCode } from '@/lib/error';
import { ModelMapper, OpenRouterModel } from './modelMapper';
import { SupabaseClient } from '@supabase/supabase-js';

const log = logger.child({ module: 'ModelSyncService' });

// tiers are ordered from cheapest → most expensive
const TIERS = ['free', 'starter', 'premium', 'enterprise'] as const;
type Tier = (typeof TIERS)[number];

export class ModelSyncService {
  private static instance: ModelSyncService;
  private openRouterApiKey: string;
  private db: DatabaseService | null = null;
  private supabase: SupabaseClient | null = null;

  private constructor() {
    this.openRouterApiKey = process.env.OPENROUTER_API_KEY || '';
  }

  public static getInstance(): ModelSyncService {
    if (!ModelSyncService.instance) {
      ModelSyncService.instance = new ModelSyncService();
    }
    return ModelSyncService.instance;
  }

  async initialize() {
    if (!this.supabase) {
      this.supabase = await createClient();
      this.db = DatabaseService.getInstance(this.supabase);
    }
  }

  /**
   * Syncs all OpenRouter models
   * @param options Configuration options for the sync
   */
  async syncOpenRouterModels(
    options: {
      onlyUpdateExisting?: boolean;
      defaultTier?: string;
      defaultPriority?: number;
    } = {}
  ) {
    try {
      await this.initialize();
      if (!this.supabase || !this.db) {
        throw new Error('Database not initialized');
      }

      // Fetch models from OpenRouter
      const openRouterModels = await this.fetchOpenRouterModels();

      // Get existing models from database
      const { data: existingModels } = await this.supabase
        .from('llm_models')
        .select('*, provider:llm_providers(*)');

      const existingModelMap = new Map(
        (existingModels || []).map((model) => [
          `${model.provider.openrouter_name}/${model.openrouter_name}`,
          model,
        ])
      );

      const existingProviders = new Set(
        existingModels?.map((model) => model.provider.openrouter_name)
      );

      // Process each model from OpenRouter
      for (const orModel of openRouterModels) {
        const existingModel = existingModelMap.get(orModel.id);

        if (existingModel) {
          // Update existing model
          const updatedModel = ModelMapper.mapOpenRouterModelToInternal(
            orModel,
            existingModel
          );

          delete updatedModel.provider;
          delete updatedModel.id;

          const response = await this.supabase
            .from('llm_models')
            .update({
              ...updatedModel,
            })
            .eq('id', existingModel.id);

          log.info(
            `Updated model: ${orModel.name} ${existingModel.id} ${
              response.error ? 'with error' : 'successfully'
            } ${response.error?.message}`
          );
        } else if (!options.onlyUpdateExisting) {
          // Create new model if we're not only updating existing ones
          const newModel = ModelMapper.mapOpenRouterModelToInternal(
            orModel
          ) as Model;

          const [openrouterProvider] = orModel.id.split('/');

          let providerId: string | null = null;

          if (!existingProviders.has(openrouterProvider)) {
            // Create new provider if it doesn't exist
            const { data: newProvider, error } = await this.supabase!
              .from('llm_providers')
              .insert({
                name: openrouterProvider,
                openrouter_name: openrouterProvider,
                is_active: false,
                supports_openrouter: true,
                supports_native: false,
              })
              .select()
              .single();

            if (error || !newProvider) {
              throw new AppError(
                `Failed to create provider ${openrouterProvider}: ${error?.message || 'Unknown error'}`,
                ErrorCode.INTERNAL,
                500,
                error as Error
              );
            }
            providerId = newProvider?.id;

            log.info(`Added new provider: ${newProvider?.name}`);
          } else {
            log.info(`Provider already exists: ${openrouterProvider}`);
            // find the provider id
            const { data: foundProvider } = await this.supabase!
              .from('llm_providers')
              .select('id')
              .eq('openrouter_name', openrouterProvider)
              .single();

            if (!foundProvider) {
              throw new Error(`Failed to find provider ${openrouterProvider}`);
            }

            providerId = foundProvider?.id;
          }

          const tier = await this.tierForModel(newModel);

          await this.supabase!.from('llm_models').insert({
            ...newModel,
            provider_id: providerId,
            tier: tier, // Default to premium tier
            priority: options.defaultPriority || 100, // Lower priority for auto-added models
            is_active: false, // Default to inactive until reviewed
          });

          log.info(`Added new model: ${orModel.name}`);
        }
      }

      log.info('Successfully synced OpenRouter models');
      return true;
    } catch (error) {
      log.error('Error syncing OpenRouter models', error);
      throw new AppError(
        'Failed to sync OpenRouter models',
        ErrorCode.INTERNAL,
        500,
        error as Error
      );
    }
  }

  /**
   * Fetches models from OpenRouter API
   */
  private async fetchOpenRouterModels(): Promise<OpenRouterModel[]> {
    try {
      const response = await fetch('https://openrouter.ai/api/v1/models', {
        headers: {
          Authorization: `Bearer ${this.openRouterApiKey}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(
          `Failed to fetch models from OpenRouter: ${response.status} ${response.statusText} - ${errorText}`
        );
      }

      const data = await response.json();
      return data.data as OpenRouterModel[];
    } catch (error) {
      log.error('Error fetching OpenRouter models', error);
      throw error;
    }
  }

  /**
   * Updates a specific model with the latest data from OpenRouter
   */
  async updateSingleModel(modelId: string) {
    try {
      await this.initialize();
      if (!this.supabase || !this.db) {
        throw new Error('Database not initialized');
      }

      // Get model from database
      const { data: model } = await this.supabase
        .from('llm_models')
        .select('*, provider:llm_providers(*)')
        .eq('id', modelId)
        .single();

      if (!model || !model.openrouter_name) {
        throw new Error(
          `Model not found or not an OpenRouter model: ${modelId}`
        );
      }

      // Fetch all models and find the matching one
      const openRouterModels = await this.fetchOpenRouterModels();
      const orModel = openRouterModels.find(
        (m) => m.id === model.openrouter_name
      );

      if (!orModel) {
        throw new Error(
          `Model not found in OpenRouter: ${model.openrouter_name}`
        );
      }

      // Update the model
      const updatedModel = ModelMapper.mapOpenRouterModelToInternal(
        orModel,
        model
      );

      await this.supabase
        .from('llm_models')
        .update(updatedModel)
        .eq('id', modelId);

      log.info(`Updated model: ${orModel.name}`);
      return true;
    } catch (error) {
      log.error(`Error updating model ${modelId}`, error);
      throw new AppError(
        `Failed to update model ${modelId}`,
        ErrorCode.INTERNAL,
        500,
        error as Error
      );
    }
  }

  /**
   * Compute the tier for a single model.
   * Heuristics:
   * 1.  Base tier comes from blended_cost_per_1k_tokens
   *     cost = (prompt + 2*completion) * 1000     (USD)
   *        ≤ 0.001        → free
   *        ≤ 0.01         → starter
   *        ≤ 0.05         → premium
   *        >  0.05        → enterprise
   *
   * 2.  Adjustment rules (applied AFTER cost band):
   *     +1 tier  if context_length ≥ 128 000
   *     +1 tier  if model supports images
   *
   * 3.  Result is clamped to valid tier indexes.
   */
  async tierForModel(model: Model): Promise<Tier> {
    // ———————————————————————————
    // 1. Blended cost per 1K tokens
    // ———————————————————————————
    const prompt = parseFloat(model.pricing?.prompt ?? '0') || 0;
    const completion = parseFloat(model.pricing?.completion ?? '0') || 0;
    const blendedCost = (prompt + 2 * completion) * 1000; // USD / 1K tokens

    let tierIdx: number;
    if (blendedCost <= 0.001) tierIdx = 0; // free
    else if (blendedCost <= 0.01) tierIdx = 1; // starter
    else if (blendedCost <= 0.05) tierIdx = 2; // premium
    else tierIdx = 3; // enterprise

    // ———————————————————————————
    // 2. Adjustments
    // ———————————————————————————
    const ctx = model.context_length ?? 0;
    if (ctx >= 128_000) tierIdx += 1;

    const modalities = model.architecture?.input_modalities ?? [];
    if (modalities.includes('image')) tierIdx += 1;

    // const looksOpenWeights =
    //   (model.license && /apache|mit|bsd|gpl/i.test(model.license)) ||
    //   /:free$/.test(model.id);
    // if (looksOpenWeights) tierIdx -= 1;

    // ———————————————————————————
    // 3. Clamp index and return
    // ———————————————————————————
    tierIdx = Math.max(0, Math.min(tierIdx, TIERS.length - 1));
    return TIERS[tierIdx];
  }
}
