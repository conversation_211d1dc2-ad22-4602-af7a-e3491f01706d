import { createClient } from '@/utils/supabase/server';
import { logger } from '@/lib/logger';
import { AppError, ErrorCode } from '@/lib/error';
import {
  SubscriptionPlan,
  SUBSCRIPTION_PLANS,
  Model,
  ModelTier,
} from '@/lib/supabase/types';
import { QuotaService } from './quota';

const log = logger.child({ service: 'ModelAccessService' });

export interface ModelAccessResult {
  canAccess: boolean;
  models: Model[];
  reason?: string;
  fallbackModels?: Model[];
}

export class ModelAccessService {
  private static instance: ModelAccessService;
  private quotaService: QuotaService;

  private constructor() {
    this.quotaService = QuotaService.getInstance();
  }

  public static getInstance(): ModelAccessService {
    if (!ModelAccessService.instance) {
      ModelAccessService.instance = new ModelAccessService();
    }
    return ModelAccessService.instance;
  }

  async getAvailableModels(userId: string): Promise<ModelAccessResult> {
    try {
      const supabase = await createClient();
      const subscription = await this.quotaService.getSubscription(userId);
      const quota = await this.quotaService.checkTokenQuota(userId);

      // Get all active models with their tiers
      const { data: allModels } = await supabase
        .from('llm_models')
        .select(
          `
          *,
          provider:llm_providers(*)
        `
        )
        .eq('is_active', true)
        .order('priority', { ascending: true });

      if (!allModels) {
        throw new AppError('No models found', ErrorCode.INTERNAL, 500);
      }

      // Filter models based on subscription tier
      const allowedTiers = SUBSCRIPTION_PLANS[subscription.plan].modelTiers;
      const availableModels = allModels.filter((model) =>
        allowedTiers.includes(model.tier as ModelTier)
      );

      // If over quota, filter to fallback models for starter tier
      if (!quota.canUse && subscription.plan === 'starter') {
        const fallbackModels = availableModels.filter(
          (model) => model.tier === 'free'
        );

        return {
          canAccess: true,
          models: fallbackModels,
          reason: 'quota_exceeded_fallback',
          fallbackModels: availableModels,
        };
      }

      return {
        canAccess: true,
        models: availableModels,
      };
    } catch (error) {
      log.error('Error getting available models', { userId, error });
      throw new AppError(
        'Failed to get available models',
        ErrorCode.INTERNAL,
        500
      );
    }
  }

  async canUserAccessModel(
    userId: string,
    modelId: string
  ): Promise<{ canAccess: boolean; reason?: string }> {
    try {
      const supabase = await createClient();
      const subscription = await this.quotaService.getSubscription(userId);
      const quota = await this.quotaService.checkTokenQuota(userId);

      // Get the specific model
      const { data: model } = await supabase
        .from('llm_models')
        .select('tier, is_active')
        .eq('id', modelId)
        .single();

      if (!model || !model.is_active) {
        return { canAccess: false, reason: 'model_not_found' };
      }

      // Check tier access
      const allowedTiers = SUBSCRIPTION_PLANS[subscription.plan].modelTiers;
      if (!allowedTiers.includes(model.tier as ModelTier)) {
        return { canAccess: false, reason: 'tier_restricted' };
      }

      // Check quota restrictions for starter tier
      if (!quota.canUse && subscription.plan === 'starter') {
        if (model.tier !== 'free') {
          return { canAccess: false, reason: 'quota_exceeded' };
        }
      }

      return { canAccess: true };
    } catch (error) {
      log.error('Error checking model access', { userId, modelId, error });
      return { canAccess: false, reason: 'error' };
    }
  }

  async getModelsByTier(subscription: SubscriptionPlan): Promise<Model[]> {
    try {
      const supabase = await createClient();
      const allowedTiers = SUBSCRIPTION_PLANS[subscription].modelTiers;

      const { data: models } = await supabase
        .from('llm_models')
        .select(
          `
          *,
          provider:llm_providers(*)
        `
        )
        .eq('is_active', true)
        .in('tier', allowedTiers)
        .order('priority', { ascending: true });

      return models || [];
    } catch (error) {
      log.error('Error getting models by tier', { subscription, error });
      return [];
    }
  }

  async checkModelCapabilities(
    userId: string,
    modelId: string
  ): Promise<{
    allowsFileUpload: boolean;
    allowsSearch: boolean;
    allowsComparison: boolean;
  }> {
    try {
      const supabase = await createClient();
      const subscription = await this.quotaService.getSubscription(userId);

      // Get model capabilities
      const { data: model } = await supabase
        .from('llm_models')
        .select('allows_file_upload, allows_search')
        .eq('id', modelId)
        .single();

      if (!model) {
        return {
          allowsFileUpload: false,
          allowsSearch: false,
          allowsComparison: false,
        };
      }

      // Check if user can use comparison mode
      const allowsComparison =
        SUBSCRIPTION_PLANS[subscription.plan].allowsComparison;

      return {
        allowsFileUpload: model.allows_file_upload || false,
        allowsSearch: model.allows_search || false,
        allowsComparison,
      };
    } catch (error) {
      log.error('Error checking model capabilities', {
        userId,
        modelId,
        error,
      });
      return {
        allowsFileUpload: false,
        allowsSearch: false,
        allowsComparison: false,
      };
    }
  }

  async getPriorityThroughput(userId: string): Promise<number> {
    try {
      const subscription = await this.quotaService.getSubscription(userId);
      return SUBSCRIPTION_PLANS[subscription.plan].priorityThroughput;
    } catch (error) {
      log.error('Error getting priority throughput', { userId, error });
      return 1; // Default to standard throughput
    }
  }

  async getUpgradeRecommendation(
    userId: string,
    requestedFeature: string
  ): Promise<{
    recommendedPlan: SubscriptionPlan;
    benefits: string[];
    reason: string;
  }> {
    try {
      const subscription = await this.quotaService.getSubscription(userId);
      const currentPlan = subscription.plan;

      if (requestedFeature === 'comparison' && currentPlan !== 'premium') {
        return {
          recommendedPlan: 'premium',
          benefits: [
            'Side-by-side model comparison (50/day)',
            '400K tokens/month',
            '2x priority throughput',
            'All premium models',
          ],
          reason: 'Comparison mode is only available on Premium plan',
        };
      }

      if (requestedFeature === 'premium_models' && currentPlan === 'free') {
        return {
          recommendedPlan: 'starter',
          benefits: [
            '150K tokens/month',
            'GPT-4o-mini, Gemini Flash, DeepSeek',
            'Workspace management',
            'Prompt library',
          ],
          reason: 'Premium models require a paid subscription',
        };
      }

      if (requestedFeature === 'tokens' && currentPlan === 'free') {
        return {
          recommendedPlan: 'starter',
          benefits: [
            '150K tokens/month (vs 20 messages/day)',
            'Access to starter models',
            'Unlimited conversations',
          ],
          reason: 'Token-based usage requires a paid subscription',
        };
      }

      // Default upgrade to next tier
      const nextPlan = currentPlan === 'free' ? 'starter' : 'premium';
      return {
        recommendedPlan: nextPlan,
        benefits: SUBSCRIPTION_PLANS[nextPlan].features,
        reason: 'Upgrade for more features and higher limits',
      };
    } catch (error) {
      log.error('Error getting upgrade recommendation', {
        userId,
        requestedFeature,
        error,
      });
      return {
        recommendedPlan: 'starter',
        benefits: ['Enhanced features', 'Higher limits'],
        reason: 'Upgrade for better experience',
      };
    }
  }
}
