/**
 * TypeScript type definitions for error monitoring services
 */

export interface ErrorMetadata {
  timestamp?: string;
  [key: string]: string | number | boolean | undefined;
}

export interface ErrorReportingOptions {
  tags?: Record<string, string>;
  extra?: Record<string, unknown>;
  context?: string;
  user?: {
    id?: string;
    email?: string;
    username?: string;
  };
}

export interface BugsnagEvent {
  addMetadata(section: string, key: string, value: unknown): void;
  addMetadata(section: string, metadata: Record<string, unknown>): void;
  setUser(id: string, email?: string, name?: string): void;
  context?: string;
  request?: {
    method?: string;
    url?: string;
    headers?: Record<string, string>;
  };
}

export interface BugsnagClient {
  notify(error: Error, onError?: (event: BugsnagEvent) => void | boolean): void;
  start(config: BugsnagConfig): void;
}

export interface BugsnagConfig {
  apiKey: string;
  enabledReleaseStages?: string[];
  releaseStage?: string;
  appVersion?: string;
  collectUserIp?: boolean;
  autoDetectErrors?: boolean;
  autoTrackSessions?: boolean;
  enabledBreadcrumbTypes?: string[];
  metadata?: Record<string, unknown>;
  onError?: (event: BugsnagEvent) => boolean | void;
}

export interface SentryOptions {
  tags?: Record<string, string>;
  extra?: Record<string, unknown>;
  contexts?: Record<string, unknown>;
  user?: {
    id?: string;
    email?: string;
    username?: string;
  };
}

// Environment variable types
declare global {
  interface ProcessEnv {
    BUGSNAG_API_KEY?: string;
    NEXT_PUBLIC_BUGSNAG_API_KEY?: string;
    APP_VERSION?: string;
    NEXT_PUBLIC_APP_VERSION?: string;
  }
}
