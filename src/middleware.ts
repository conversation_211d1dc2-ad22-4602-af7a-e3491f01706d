import { updateSession } from '@/utils/supabase/middleware';
import { NextRequest } from 'next/server';

export async function middleware(request: NextRequest) {
  return await updateSession(request);
}

export const config = {
  matcher: [
    '/auth/:path*',
    '/chat',
    '/c/:path*',
    '/settings',
    '/settings/:path*',
    '/api/chat/:path*',
    '/api/conversations/:path*',
    '/api/groupConversations/:path*',
  ],
};
