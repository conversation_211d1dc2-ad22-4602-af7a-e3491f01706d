import { authService } from '@/lib/supabase/auth';
import { useEffect, useState } from 'react';
import { useAnalytics } from '@/hooks/useAnalytics';
import { logger } from '@/lib/logger';
import { useRouter } from 'next/navigation';
const log = logger.child({
  module: 'useAuth',
});

export const useAuth = () => {
  const analytics = useAnalytics();
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string | null>(null);
  const [otp, setOtp] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');

  const validateEmail = (email: string) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  const handleGoogleSignIn = async () => {
    try {
      analytics.trackEvent('Button Clicked', { buttonName: 'Google Sign In' });
      const { error } = await authService.signInWithGoogle();

      if (error) {
        log.error('Error signing in with Google:', error.message);
        // TODO: Show user-friendly error message
      } else {
        // Supabase handles redirection, analytics on success might be tracked elsewhere (e.g., on initial load after sign-in)
      }
    } catch (error) {
      log.error('Unexpected error during Google sign in:', error);
      // TODO: Show user-friendly error message
    }
  };

  const handleEmailSignIn = async () => {
    // Reset previous states
    setError(null);
    setMessage(null);

    // Track analytics
    analytics.trackEvent('Button Clicked', {
      buttonName: 'Email Sign In',
      emailProvided: !!email,
    });

    // Validate email
    if (!email) {
      setError('Please enter your email address');
      log.warn('Email sign in attempted without an email.');
      return;
    }

    if (!validateEmail(email)) {
      setError('Please enter a valid email address');
      log.warn(`Invalid email format entered: ${email}`);
      return;
    }

    // Set loading state
    setIsLoading(true);

    try {
      log.info({ email }, 'Email sign-in attempt');

      const { error } = await authService.signInWithOtp({
        email,
        options: {
          emailRedirectTo: `${window.location.origin}/api/auth/callback`,
        },
      });

      if (error) {
        setIsLoading(false);
        setError(
          'Could not send sign-in link. Please check the email address and try again.'
        );
        log.error({ err: error, email }, 'Error sending magic link email');
        analytics.trackEvent('EmailSignInError', {
          emailProvided: true,
          errorCode: error.code,
          errorMessage: error.message,
        });
      } else {
        setIsLoading(false);
        // Save the email in localStorage for the verify page
        if (typeof window !== 'undefined') {
          localStorage.setItem('verificationEmail', email);
        }
        log.info(
          { email },
          'Magic link email sent successfully, redirecting to verify page.'
        );
        analytics.trackEvent('EmailSignInLinkSent', { emailProvided: true });

        // Redirect to the verify page
        router.push('/auth/verify');
      }
    } catch (error) {
      setIsLoading(false);
      setError('An unexpected error occurred. Please try again.');
      log.error({ err: error, email }, 'Unexpected error during email sign-in');
      analytics.trackEvent('EmailSignInError', {
        emailProvided: true,
        errorCode: 'unexpected',
        errorMessage: String(error),
      });
    }
  };

  // Load email from localStorage when component mounts
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedEmail = localStorage.getItem('verificationEmail');
      if (savedEmail) {
        setEmail(savedEmail);
      }
    }
  }, []);

  const handleVerifyOtp = async () => {
    // Reset previous states
    setError(null);
    setMessage(null);

    // Validate input
    if (!otp) {
      setError('Please enter the verification code');
      return;
    }

    if (!email) {
      setError('Please enter your email address');
      return;
    }

    setIsLoading(true);

    try {
      // Track analytics
      analytics.trackEvent('Button Clicked', {
        buttonName: 'Verify OTP',
        emailProvided: !!email,
      });

      log.info({ email }, 'OTP verification attempt');

      // Destructure data to get user information
      const { data, error: verifyError } = await authService.verifyOtp({
        email,
        token: otp,
      });

      if (verifyError) {
        setIsLoading(false);
        setError(
          'Invalid or expired code. Please try again or request a new code.'
        );
        log.error({ err: verifyError, email }, 'Error verifying OTP');
        analytics.trackEvent('OtpVerificationError', {
          emailProvided: true,
          errorCode: verifyError.code,
          errorMessage: verifyError.message,
        });
      } else {
        // Verification successful
        setIsLoading(false);
        setMessage('Verification successful! Redirecting...');
        log.info({ email }, 'OTP verified successfully');
        analytics.trackEvent('OtpVerificationSuccess', { emailProvided: true });

        // Clear the stored email
        if (typeof window !== 'undefined') {
          localStorage.removeItem('verificationEmail');
        }

        // Check user metadata for names
        const user = data?.user;
        const firstName = user?.user_metadata?.first_name;
        const lastName = user?.user_metadata?.last_name;

        if (firstName && lastName) {
          // Names exist, redirect to main app
          log.info('User details found, redirecting to chat.');
          // Add a small delay for the message to be visible
          setTimeout(() => {
            router.push('/chat');
          }, 1000);
        } else {
          // Names missing, redirect to details page
          log.info('User details missing, redirecting to details page.');
          // Add a small delay for the message to be visible
          setTimeout(() => {
            router.push('/auth/details');
          }, 1000);
        }
      }
    } catch (error) {
      setIsLoading(false);
      setError('An unexpected error occurred. Please try again.');
      log.error(
        { err: error, email },
        'Unexpected error during OTP verification'
      );
      analytics.trackEvent('OtpVerificationError', {
        emailProvided: true,
        errorCode: 'unexpected',
        errorMessage: String(error),
      });
    }
  };

  const resendEmail = async () => {
    // Reset previous states
    setError(null);
    setMessage(null); // Clear previous success messages

    if (!email) {
      setError('Email address not found. Please go back and enter your email.');
      log.warn('Resend OTP attempted without an email available in state.');
      return;
    }

    // Track analytics
    analytics.trackEvent('Button Clicked', { buttonName: 'Resend OTP' });

    // Validate email just in case, though it should be valid from the initial sign-in
    if (!validateEmail(email)) {
      setError('Invalid email address format.');
      log.warn(`Invalid email format found during resend: ${email}`);
      return;
    }

    // Set loading state
    setIsLoading(true);

    try {
      log.info({ email }, 'Resending OTP email');

      const { error: resendError } = await authService.signInWithOtp({
        email,
        options: {
          emailRedirectTo: `${window.location.origin}/api/auth/callback`,
          // We might want to ensure this resend is treated as the same flow
          // If Supabase supports it, consider adding data: { existing_flow: true } or similar
        },
      });

      if (resendError) {
        setIsLoading(false);
        setError(
          'Could not resend verification code. Please try again shortly.'
        );
        log.error({ err: resendError, email }, 'Error resending OTP email');
        analytics.trackEvent('ResendOtpError', {
          emailProvided: true,
          errorCode: resendError.code,
          errorMessage: resendError.message,
        });
      } else {
        setIsLoading(false);
        setMessage('A new verification code has been sent to your email.');
        log.info({ email }, 'New OTP email sent successfully.');
        analytics.trackEvent('ResendOtpSuccess', { emailProvided: true });
        // Do not redirect, user stays on the verify page
      }
    } catch (error) {
      setIsLoading(false);
      setError('An unexpected error occurred. Please try again.');
      log.error({ err: error, email }, 'Unexpected error during OTP resend');
      analytics.trackEvent('ResendOtpError', {
        emailProvided: true,
        errorCode: 'unexpected',
        errorMessage: String(error),
      });
    }
  };

  // Function to update user details
  const updateUserDetails = async () => {
    setError(null);
    setMessage(null);

    if (!firstName || !lastName) {
      setError('Please enter both first and last name.');
      return;
    }

    setIsLoading(true);
    analytics.trackEvent('Button Clicked', {
      buttonName: 'Update User Details',
    });
    log.info({ email }, 'Attempting to update user details'); // Assuming email is still relevant or user is fetched

    try {
      // Call the auth service to update user metadata
      const { error: updateError } = await authService.updateUserMetadata({
        firstName,
        lastName,
      });

      if (updateError) {
        setIsLoading(false);
        setError('Could not update details. Please try again.');
        log.error({ err: updateError }, 'Error updating user details');
        analytics.trackEvent('UpdateUserDetailsError', {
          errorCode: updateError.code,
          errorMessage: updateError.message,
        });
      } else {
        setIsLoading(false);
        setMessage('Details updated successfully! Redirecting...');
        log.info('User details updated successfully');
        analytics.trackEvent('UpdateUserDetailsSuccess');

        // Redirect to the main app area
        setTimeout(() => {
          router.push('/chat');
        }, 1500);
      }
    } catch (error) {
      setIsLoading(false);
      setError('An unexpected error occurred while updating details.');
      log.error({ err: error }, 'Unexpected error updating user details');
      analytics.trackEvent('UpdateUserDetailsError', {
        errorCode: 'unexpected',
        errorMessage: String(error),
      });
    }
  };

  return {
    email,
    isLoading,
    error,
    message,
    otp,
    setOtp,
    handleVerifyOtp,
    handleEmailSignIn,
    handleGoogleSignIn,
    validateEmail,
    setEmail,
    setError,
    setMessage,
    firstName,
    lastName,
    setFirstName,
    setLastName,
    updateUserDetails,
    resendEmail,
  };
};
