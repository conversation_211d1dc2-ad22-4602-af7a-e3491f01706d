'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2 } from 'lucide-react';

export default function VerifyOtpPage({
  error,
  message,
  isLoading,
  otp,
  setOtp,
  handleVerifyOtp,
  handleUseAnotherEmail,
  setError,
  resendEmail,
}: {
  error: string | null;
  message: string | null;
  isLoading: boolean;
  otp: string;
  setOtp: (otp: string) => void;
  handleVerifyOtp: () => void;
  handleUseAnotherEmail: () => void;
  setError: (error: string | null) => void;
  resendEmail: () => void;
}) {
  return (
    <>
      <CardHeader className='space-y-1 p-6 text-center relative'>
        <CardTitle className='text-2xl font-bold tracking-tight text-foreground'>
          Verify Your Email
        </CardTitle>
        <CardDescription className='text-sm text-foreground/70'>
          Enter the verification code sent to your email
        </CardDescription>
      </CardHeader>

      <CardContent className='space-y-4 p-6 pt-2'>
        {/* Error message */}
        {error && (
          <Alert variant='destructive' className='py-2'>
            <AlertDescription className='text-sm'>{error}</AlertDescription>
          </Alert>
        )}

        {/* Success message */}
        {message && (
          <Alert
            variant='default'
            className='bg-green-50 border-green-200 py-2 dark:bg-green-900/20 dark:border-green-800'
          >
            <AlertDescription className='text-sm text-green-700 dark:text-green-400'>
              {message}
            </AlertDescription>
          </Alert>
        )}

        {/* Email Input */}
        {/* <div className="space-y-2">
            <Input
              type="email"
              placeholder="Your email address"
              value={email}
              onChange={(e) => {
                setEmail(e.target.value);
                if (error) setError(null);
              }}
              className="h-11 w-full"
              disabled={isLoading}
            />
          </div> */}

        {/* OTP Input */}
        <div className='space-y-2'>
          <Input
            type='text'
            placeholder='Enter verification code'
            value={otp}
            onChange={(e) => {
              setOtp(e.target.value);
              if (error) setError(null);
            }}
            className='h-11 w-full text-center text-lg tracking-widest'
            disabled={isLoading}
            maxLength={6}
          />
        </div>

        {/* Verify Button */}
        <Button
          className='w-full h-11'
          onClick={handleVerifyOtp}
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Loader2 className='mr-2 h-4 w-4 animate-spin' />
              Verifying...
            </>
          ) : (
            'Verify Code'
          )}
        </Button>

        {/* Use Another Email Button */}
        <Button
          variant='ghost'
          className='w-full mt-2'
          onClick={handleUseAnotherEmail}
          disabled={isLoading}
        >
          Use Another Email
        </Button>
      </CardContent>

      <CardFooter className='flex flex-col items-center space-y-2 text-center text-xs text-foreground/70 p-6 pt-0'>
        <span>Didn&apos;t receive a code? Check your spam folder.</span>
        <Button
          variant='link'
          className='text-xs p-0 h-auto'
          onClick={resendEmail}
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Loader2 className='mr-1 h-3 w-3 animate-spin' />
              Sending...
            </>
          ) : (
            'Resend Code'
          )}
        </Button>
      </CardFooter>
    </>
  );
}
