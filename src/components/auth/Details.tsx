'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card<PERSON>ontent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2 } from 'lucide-react';
import { useAuth } from './useAuth'; // Assuming useAuth is in the same directory

export default function UserDetailsForm() {
  const {
    isLoading,
    error,
    message,
    firstName,
    lastName,
    setFirstName,
    setLastName,
    updateUserDetails,
    setError, // Get setError to clear errors on input change
  } = useAuth();

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    updateUserDetails();
  };

  return (
    <>
      <CardHeader className='space-y-1 p-6 text-center relative'>
        <CardTitle className='text-2xl font-bold tracking-tight text-foreground'>
          Complete Your Profile
        </CardTitle>
        <CardDescription className='text-sm text-foreground/70'>
          Please enter your first and last name.
        </CardDescription>
      </CardHeader>

      <CardContent className='space-y-4 p-6 pt-2'>
        {/* Error message */}
        {error && (
          <Alert variant='destructive' className='py-2'>
            <AlertDescription className='text-sm'>{error}</AlertDescription>
          </Alert>
        )}

        {/* Success message */}
        {message && (
          <Alert
            variant='default'
            className='bg-green-50 border-green-200 py-2 dark:bg-green-900/20 dark:border-green-800'
          >
            <AlertDescription className='text-sm text-green-700 dark:text-green-400'>
              {message}
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className='space-y-4'>
          <div className='space-y-2'>
            <Label htmlFor='firstName'>First Name</Label>
            <Input
              id='firstName'
              type='text'
              placeholder='Enter your first name'
              value={firstName}
              onChange={(e) => {
                setFirstName(e.target.value);
                if (error) setError(null); // Clear error on change
              }}
              className='h-11 w-full'
              disabled={isLoading}
              required
            />
          </div>

          <div className='space-y-2'>
            <Label htmlFor='lastName'>Last Name</Label>
            <Input
              id='lastName'
              type='text'
              placeholder='Enter your last name'
              value={lastName}
              onChange={(e) => {
                setLastName(e.target.value);
                if (error) setError(null); // Clear error on change
              }}
              className='h-11 w-full'
              disabled={isLoading}
              required
            />
          </div>

          <Button type='submit' className='w-full h-11' disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                Saving...
              </>
            ) : (
              'Save Details'
            )}
          </Button>
        </form>
      </CardContent>

      <CardFooter className='text-center text-xs text-foreground/70 p-6 pt-0'>
        This information helps personalize your experience.
      </CardFooter>
    </>
  );
}
