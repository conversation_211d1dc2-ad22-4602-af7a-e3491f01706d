'use client';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card<PERSON>ontent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import Image from 'next/image';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Mail, Loader2 } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { LegalModal } from '../legal-modal';
import { termsOfServiceContent } from '@/content/terms';
import { privacyPolicyContent } from '@/content/privacy';

export default function Login({
  loginWithGoogle,
  loginWithEmail,
  error,
  message,
  isLoading,
  email,
  setEmail,
  setError,
  setMessage,
}: {
  loginWithGoogle: () => void;
  loginWithEmail: () => void;
  error: string | null;
  message: string | null;
  isLoading: boolean;
  email: string;
  setEmail: (email: string) => void;
  setError: (error: string | null) => void;
  setMessage: (message: string | null) => void;
}) {
  return (
    <>
      <CardHeader className='space-y-1 p-6 text-center relative'>
        <CardTitle className='text-2xl font-bold tracking-tight text-foreground'>
          Welcome!
        </CardTitle>
        <CardDescription className='text-sm text-foreground/70'>
          Sign in to access the best in class models all in one place.
        </CardDescription>
      </CardHeader>

      <CardContent className='space-y-4 p-6 pt-2'>
        {/* Google Button */}
        <Button
          className='flex h-11 w-full items-center justify-center gap-3 rounded-md px-4 text-sm font-medium shadow-sm transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2'
          onClick={loginWithGoogle}
        >
          <Image
            src='/google-logo2.svg'
            alt='Google logo'
            width={20}
            height={20}
          />
          <span>Continue with Google</span>
        </Button>

        {/* Separator */}
        <div className='relative py-2'>
          <div className='absolute inset-0 flex items-center'>
            <Separator />
          </div>
          <div className='relative flex justify-center text-xs uppercase'>
            <span className='bg-background px-2 text-foreground/60'>Or</span>
          </div>
        </div>

        {/* Email Input and Button */}
        <div className='space-y-2'>
          {/* Error message */}
          {error && (
            <Alert variant='destructive' className='py-2'>
              <AlertDescription className='text-sm'>{error}</AlertDescription>
            </Alert>
          )}

          {/* Success message */}
          {message && (
            <Alert
              variant='default'
              className='bg-green-50 border-green-200 py-2 dark:bg-green-900/20 dark:border-green-800'
            >
              <AlertDescription className='text-sm text-green-700 dark:text-green-400'>
                {message}
              </AlertDescription>
            </Alert>
          )}

          <Input
            type='email'
            placeholder='Enter your email'
            value={email}
            onChange={(e) => {
              setEmail(e.target.value);
              // Clear error/message when user starts typing again
              if (error) setError(null);
              if (message) {
                setMessage(null);
              }
            }}
            className='h-11 w-full'
            disabled={isLoading}
          />
          <Button
            type='button'
            variant='outline'
            className='flex h-11 w-full items-center justify-center gap-2 mt-2'
            onClick={loginWithEmail}
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className='h-5 w-5 animate-spin' />
            ) : (
              <Mail className='h-5 w-5' />
            )}
            <span>{isLoading ? 'Sending...' : 'Continue with Email'}</span>
          </Button>
        </div>
      </CardContent>
      <CardFooter className='border-t border-border bg-muted px-6 py-4'>
        <p className='text-center text-xs text-foreground/70'>
          By continuing, you agree to our{' '}
          <LegalModal
            triggerText='Terms of Service'
            title='Terms of Service'
            content={termsOfServiceContent}
          />{' '}
          &{' '}
          <LegalModal
            triggerText='Privacy Policy'
            title='Privacy Policy'
            content={privacyPolicyContent}
          />
        </p>
      </CardFooter>
    </>
  );
}
