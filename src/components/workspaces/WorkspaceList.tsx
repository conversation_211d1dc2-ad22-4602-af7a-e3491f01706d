'use client';

import { useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { CreateWorkspaceModal } from './CreateWorkspaceModal';
import { WorkspaceListItem } from './WorkspaceListItem'; // Assuming this component exists
import { Skeleton } from '@/components/ui/skeleton';
import { ScrollArea } from '@/components/ui/scroll-area';
import { PlusIcon } from 'lucide-react'; // Using lucide-react for icons

// Define the shape of a workspace object based on your API/data model
interface Workspace {
  id: string;
  name: string;
  icon?: string | null;
  // Add other relevant fields, e.g., context indicator
  has_context?: boolean; // Example: needs to be fetched from API
  status?: 'initiated' | 'pending' | 'completed' | 'error';
}

export function WorkspaceList() {
  const [workspaces, setWorkspaces] = useState<Workspace[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const pathname = usePathname();
  const [isCreateWorkspaceModalOpen, setIsCreateWorkspaceModalOpen] = useState(false);
  useEffect(() => {
    async function fetchWorkspaces() {
      setIsLoading(true);
      setError(null);
      try {
        const response = await fetch('/api/workspaces');
        if (!response.ok) {
          throw new Error('Failed to fetch workspaces');
        }
        const data = await response.json();
        // TODO: Adjust based on the actual API response structure
        setWorkspaces(data.workspaces || data || []);
      } catch (err) {
        console.error(err);
        setError(
          err instanceof Error ? err.message : 'An unknown error occurred'
        );
      } finally {
        setIsLoading(false);
      }
    }
    fetchWorkspaces();
  }, [pathname]); // Refetch if path changes (e.g., after creation redirects)

  return (
    <div className='flex flex-col h-full'>
      <div className='p-4 flex justify-between items-center border-b'>
        <h2 className='text-lg font-semibold'>Workspaces</h2>
          <Button variant='ghost' size='icon' onClick={() => setIsCreateWorkspaceModalOpen(true)}>
            <PlusIcon className='h-5 w-5' />
            <span className='sr-only'>New Workspace</span>
          </Button>
      </div>

      {/* Optional Search Bar - Placeholder */}
      {/* <div className="p-2 border-b">
        <Input placeholder="Search workspaces..." />
      </div> */}

      <ScrollArea className='flex-grow'>
        <div className='p-2 space-y-1'>
          {isLoading && (
            <>
              {[...Array(3)].map((_, i) => (
                <Skeleton key={i} className='h-10 w-full rounded-md' />
              ))}
            </>
          )}
          {error && <p className='text-red-500 text-sm p-2'>{error}</p>}
          {!isLoading && !error && workspaces.length === 0 && (
            <p className='text-muted-foreground text-sm p-2 text-center'>
              No workspaces yet.
            </p>
          )}
          {!isLoading &&
            !error &&
            workspaces.map((ws) => (
              <WorkspaceListItem
                key={ws.id}
                id={ws.id}
                name={ws.name}
                icon={ws.icon}
                hasContext={ws.has_context}
                status={ws.status}
                isActive={pathname === `/workspaces/${ws.id}`}
              />
            ))}
        </div>
      </ScrollArea>
      <CreateWorkspaceModal
        // onSuccess={handleWorkspaceCreated}
        open={isCreateWorkspaceModalOpen}
        onOpenChange={setIsCreateWorkspaceModalOpen}
      />
    </div>
  );
}
