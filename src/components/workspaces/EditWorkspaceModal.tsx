'use client';

import { Workspace } from '@/lib/supabase/types';
import { WorkspaceFormModal } from './WorkspaceFormModal';

interface EditWorkspaceModalProps {
  workspace: Workspace;
  onSuccess?: () => void;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function EditWorkspaceModal({
  workspace,
  onSuccess,
  open,
  onOpenChange,
}: EditWorkspaceModalProps) {
  if (!open) {
    return null;
  }

  return (
    <WorkspaceFormModal
      mode='edit'
      workspace={workspace}
      onSuccess={onSuccess}
      open={open}
      onOpenChange={onOpenChange}
    />
  );
}
