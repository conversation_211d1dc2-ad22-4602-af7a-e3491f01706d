'use client';

import { useContext } from 'react';
import { ChatContext } from '@/providers/ChatProvider';
import { ModelSelector } from '@/components/chat/ModelSelector/ModelSelector';

interface WorkspaceModelSelectorProps {
  value?: string; // model ID
  onChange: (modelId: string | null) => void;
  disabled?: boolean;
  placeholder?: string;
  modal?: boolean;
}

export function WorkspaceModelSelector({
  value,
  onChange,
  disabled = false,
  placeholder = 'Select a default model for this workspace',
  modal = true,
}: WorkspaceModelSelectorProps) {
  const { providers } = useContext(ChatContext);

  return (
    <ModelSelector
      value={value}
      onChange={onChange}
      variant='popover'
      buttonVariant='outline'
      disabled={disabled}
      placeholder={placeholder}
      providers={providers}
      popoverModal={modal}
      className='w-full'
    />
  );
}
