'use client';

import { WorkspaceFormModal } from './WorkspaceFormModal';
import { useFeatureAccess } from '@/hooks/useFeatureAccess';
import { UpgradePrompt } from '@/components/ui/upgrade-prompt';

interface CreateWorkspaceModalProps {
  onSuccess?: (workspaceId: string) => void;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function CreateWorkspaceModal({
  onSuccess,
  open,
  onOpenChange,
}: CreateWorkspaceModalProps) {
  const { canCreateWorkspace } = useFeatureAccess();

  // If user can't create workspace, show upgrade prompt instead
  if (open && !canCreateWorkspace) {
    return (
      <UpgradePrompt
        isOpen={true}
        onClose={() => onOpenChange(false)}
        feature='Workspace Creation'
        requiredPlan='starter'
        title='Workspace limit reached'
        description='You have reached the maximum number of workspaces for your current plan. Upgrade to create unlimited workspaces.'
      />
    );
  }

  if (!open) {
    return null;
  }

  // Create a wrapper function to handle the optional workspaceId
  const handleSuccess = (workspaceId?: string) => {
    if (workspaceId && onSuccess) {
      onSuccess(workspaceId);
    }
  };

  return (
    <>
      <WorkspaceFormModal
        mode='create'
        onSuccess={handleSuccess}
        open={open}
        onOpenChange={onOpenChange}
      />
    </>
  );
}
