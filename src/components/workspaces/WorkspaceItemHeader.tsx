'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  PenSquare,
  Upload,
  Text,
  Trash2,
  FileText,
  AlertTriangle,
  Loader2,
  Settings,
  Plus,
  Clock,
} from 'lucide-react';
import {
  Workspace,
  WorkspaceFile,
  WorkspaceNote,
  GroupConversation,
} from '@/lib/supabase/types';
import { NoteModal } from '@/components/workspaces/NoteModal';
import { ContextUploadModal } from '@/components/workspaces/ContextUploadModal';
import { ContextPanel } from '@/components/workspaces/ContextPanel';
import { EditWorkspaceModal } from '@/components/workspaces/EditWorkspaceModal';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useState } from 'react';

type Params = {
  workspace: Workspace;
  files: WorkspaceFile[];
  notes: WorkspaceNote[];
  recentConversations: GroupConversation[];
  showConversations: boolean;
  fetchData: () => void;
  handleDeleteWorkspace: () => void;
  handleContextUpdate: () => void;
  handleNewChat: () => void;
  handleToggleConversations: () => void;
  isSubmitting: boolean;
  contextSheetOpen: boolean;
  setContextSheetOpen: (open: boolean) => void;
};

export default function WorkspaceItemHeader(props: Params) {
  const {
    workspace,
    files,
    notes,
    recentConversations,
    showConversations,
    fetchData,
    handleDeleteWorkspace,
    handleContextUpdate,
    handleNewChat,
    handleToggleConversations,
    isSubmitting,
    contextSheetOpen,
    setContextSheetOpen,
  } = props;

  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  const contextCount = files.length + notes.length;
  const isProcessing =
    workspace.status === 'initiated' || workspace.status === 'pending';
  const hasError = workspace.status === 'error';

  return (
    <>
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-4'>
          <div className='h-12 w-12 rounded-lg bg-primary/10 text-primary flex items-center justify-center text-xl font-semibold'>
            {workspace.icon || workspace.name.charAt(0).toUpperCase()}
          </div>
          <div className='flex flex-col'>
            <div className='flex items-center gap-3'>
              <h2 className='text-2xl font-bold'>{workspace.name}</h2>

              {/* Status indicators */}
              {isProcessing && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <div className='flex items-center gap-1.5 text-amber-500'>
                        <Loader2 className='h-4 w-4 animate-spin' />
                        <span className='text-xs font-medium'>Processing</span>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Files are being processed and embedded</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}

              {hasError && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <div className='flex items-center gap-1.5 text-destructive'>
                        <AlertTriangle className='h-4 w-4' />
                        <span className='text-xs font-medium'>Error</span>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>There was an error processing the workspace</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>

            {workspace.description && (
              <p className='text-muted-foreground text-sm mt-1'>
                {workspace.description}
              </p>
            )}
          </div>
        </div>

        <div className='flex items-center gap-3'>
          {/* Chat Actions */}
          <div className='flex items-center gap-2'>
            <Button
              onClick={handleNewChat}
              className='bg-primary hover:bg-primary/90 text-primary-foreground font-medium px-4 py-2'
            >
              <Plus className='mr-2 h-4 w-4' />
              New Chat
            </Button>

            {recentConversations.length > 0 && (
              <Button
                variant={showConversations ? 'secondary' : 'outline'}
                size='sm'
                onClick={handleToggleConversations}
                className='text-sm'
              >
                <Clock className='mr-2 h-3 w-3' />
                Recent ({recentConversations.length})
              </Button>
            )}
          </div>

          {/* Knowledge Hub */}
          <Sheet open={contextSheetOpen} onOpenChange={setContextSheetOpen}>
            <SheetTrigger asChild>
              <Button variant='outline' size='sm'>
                <FileText className='mr-2 h-4 w-4' />
                Knowledge Hub
                {contextCount > 0 && (
                  <Badge variant='secondary' className='ml-2'>
                    {contextCount}
                  </Badge>
                )}
              </Button>
            </SheetTrigger>
            <SheetContent className='sm:max-w-md'>
              <SheetHeader>
                <SheetTitle>Workspace Knowledge Hub</SheetTitle>
                <SheetDescription>
                  Files and notes providing context for this workspace.
                </SheetDescription>
              </SheetHeader>

              <div className='flex flex-wrap gap-2 mt-4 mb-6'>
                <ContextUploadModal
                  workspaceId={workspace.id}
                  onSuccess={handleContextUpdate}
                >
                  <Button variant='outline' size='sm' className='h-8'>
                    <Upload className='mr-2 h-3.5 w-3.5' /> Upload File
                  </Button>
                </ContextUploadModal>
                <NoteModal
                  mode='create'
                  workspaceId={workspace.id}
                  onSuccess={handleContextUpdate}
                >
                  <Button variant='outline' size='sm' className='h-8'>
                    <Text className='mr-2 h-3.5 w-3.5' /> Add Note
                  </Button>
                </NoteModal>
              </div>

              <div className='mt-2'>
                <ContextPanel
                  files={files}
                  notes={notes}
                  workspaceId={workspace.id}
                  onUpdate={handleContextUpdate}
                />
              </div>
            </SheetContent>
          </Sheet>

          {/* Settings Dropdown */}
          <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
            <DropdownMenuTrigger asChild>
              <Button variant='outline' size='sm' disabled={isSubmitting}>
                <Settings className='h-4 w-4' />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='end'>
              <DropdownMenuItem
                onSelect={() => {
                  setIsEditModalOpen(true);
                  setIsDropdownOpen(false);
                }}
              >
                  <PenSquare className='mr-2 h-4 w-4' />
                  Edit Workspace
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className='text-destructive focus:text-destructive'
                onClick={handleDeleteWorkspace}
                disabled={isSubmitting}
              >
                <Trash2 className='mr-2 h-4 w-4' />
                Delete Workspace
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <EditWorkspaceModal
        workspace={workspace}
        onSuccess={() => {
          fetchData();
          setIsEditModalOpen(false);
        }}
        open={isEditModalOpen}
        onOpenChange={setIsEditModalOpen}
      />
    </>
  );
}
