'use client';

import Link from 'next/link';
import { cn } from '@/lib/utils'; // Assuming utility for classnames
import { FileTextIcon, Loader2, AlertTriangle } from 'lucide-react'; // Icon for context indicator
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface WorkspaceListItemProps {
  id: string;
  name: string;
  icon?: string | null;
  hasContext?: boolean;
  isActive?: boolean;
  status?: 'initiated' | 'pending' | 'completed' | 'error';
}

export function WorkspaceListItem({
  id,
  name,
  icon,
  hasContext,
  isActive,
  status,
}: WorkspaceListItemProps) {
  const isProcessing = status === 'initiated' || status === 'pending';
  const hasError = status === 'error';

  return (
    <Link
      href={`/workspaces/${id}`}
      className={cn(
        'group flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors',
        isActive
          ? 'bg-primary/10 text-primary'
          : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
      )}
      aria-current={isActive ? 'page' : undefined}
    >
      <span className='flex h-6 w-6 items-center justify-center rounded-sm text-xs'>
        {icon || '📂'}
      </span>
      <span className='flex-grow truncate'>{name}</span>

      {/* Status indicators */}
      {isProcessing && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Loader2 className='h-4 w-4 flex-shrink-0 text-amber-500 animate-spin' />
            </TooltipTrigger>
            <TooltipContent side='right'>
              <p className='text-xs'>Processing workspace files</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}

      {hasError && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <AlertTriangle className='h-4 w-4 flex-shrink-0 text-destructive' />
            </TooltipTrigger>
            <TooltipContent side='right'>
              <p className='text-xs'>Error processing workspace</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}

      {hasContext && !isProcessing && !hasError && (
        <FileTextIcon className='h-4 w-4 flex-shrink-0 text-muted-foreground/70 group-hover:text-accent-foreground' />
      )}
    </Link>
  );
}
