'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
  DialogClose,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';
import { WorkspaceNote } from '@/lib/supabase/types';

type NoteModalMode = 'create' | 'edit' | 'add-message';

interface NoteModalProps {
  children: React.ReactNode;
  workspaceId: string;
  mode: NoteModalMode;
  onSuccess?: () => void;
  // For edit mode
  note?: WorkspaceNote;
  // For add-message mode
  messageContent?: string;
  messageRole?: 'user' | 'assistant';
}

const modalConfig = {
  create: {
    title: 'Add New Note',
    description:
      'Create a new note for this workspace. It will be added to the context.',
    submitText: 'Save Note',
    submittingText: 'Saving...',
    successMessage: (title: string) => `Note "${title}" created successfully`,
    maxWidth: 'sm:max-w-[425px]',
    textareaRows: 5,
  },
  edit: {
    title: 'Edit Note',
    description:
      'Update the note content. Changes will be reflected in the workspace context.',
    submitText: 'Update Note',
    submittingText: 'Updating...',
    successMessage: (title: string) => `Note "${title}" updated successfully`,
    maxWidth: 'sm:max-w-[425px]',
    textareaRows: 5,
  },
  'add-message': {
    title: 'Add Message to Workspace Context',
    description:
      'Add this message to the workspace context as a note. You can edit the content before saving.',
    submitText: 'Add to Workspace',
    submittingText: 'Adding...',
    successMessage: (title: string) =>
      `Message added to workspace context as "${title}"`,
    maxWidth: 'sm:max-w-[500px]',
    textareaRows: 8,
  },
};

export function NoteModal({
  children,
  workspaceId,
  mode,
  onSuccess,
  note,
  messageContent,
  messageRole,
}: NoteModalProps) {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [title, setTitle] = useState('');
  const [body, setBody] = useState('');

  const config = modalConfig[mode];

  // Initialize form data based on mode
  useEffect(() => {
    if (isOpen) {
      if (mode === 'edit' && note) {
        setTitle(note.title || '');
        setBody(note.body || '');
      } else if (mode === 'add-message' && messageContent) {
        const defaultTitle =
          messageRole === 'user' ? 'User Message' : 'Assistant Response';
        setTitle(defaultTitle);
        setBody(messageContent);
      } else {
        setTitle('');
        setBody('');
      }
    }
  }, [isOpen, mode, note, messageContent, messageRole]);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    if (!title.trim() || !body.trim()) {
      toast.error('Both title and body are required.');
      return;
    }

    setIsSubmitting(true);
    try {
      const requestBody = { title: title.trim(), body: body.trim() };
      const requestOptions = {
        method: mode === 'edit' ? 'PATCH' : 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(
          mode === 'edit' ? { ...requestBody, id: note?.id } : requestBody
        ),
      };

      const response = await fetch(
        `/api/workspaces/${workspaceId}/notes`,
        requestOptions
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.error ||
            `Failed to ${mode === 'edit' ? 'update' : 'create'} note`
        );
      }

      toast.success(config.successMessage(title));
      setIsOpen(false);
      if (onSuccess) onSuccess();
      router.refresh();
    } catch (error) {
      console.error(
        `Error ${mode === 'edit' ? 'updating' : 'creating'} note:`,
        error
      );
      toast.error(
        error instanceof Error
          ? error.message
          : `Failed to ${
              mode === 'edit'
                ? 'update'
                : mode === 'add-message'
                ? 'add message to workspace'
                : 'create note'
            }`
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      setTitle('');
      setBody('');
      setIsSubmitting(false);
    }
    setIsOpen(open);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className={config.maxWidth}>
        <DialogHeader>
          <DialogTitle>{config.title}</DialogTitle>
          <DialogDescription>{config.description}</DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className='space-y-4 py-4'>
          <div className='space-y-1.5'>
            <Label htmlFor='note-title'>Title</Label>
            <Input
              id='note-title'
              placeholder={
                mode === 'add-message'
                  ? 'Enter a title for this note...'
                  : 'Note Title'
              }
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              disabled={isSubmitting}
              required
            />
          </div>
          <div className='space-y-1.5'>
            <Label htmlFor='note-body'>Content</Label>
            <Textarea
              id='note-body'
              placeholder={
                mode === 'add-message'
                  ? 'Message content...'
                  : 'Type your note content here...'
              }
              value={body}
              onChange={(e) => setBody(e.target.value)}
              disabled={isSubmitting}
              required
              rows={config.textareaRows}
              className='resize-none'
            />
          </div>
          <DialogFooter className='pt-2'>
            <DialogClose asChild>
              <Button type='button' variant='outline' disabled={isSubmitting}>
                Cancel
              </Button>
            </DialogClose>
            <Button type='submit' disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className='mr-2 h-4 w-4 animate-spin' />{' '}
                  {config.submittingText}
                </>
              ) : (
                config.submitText
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
