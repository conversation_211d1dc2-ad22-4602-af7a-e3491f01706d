'use client';

import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { EmojiPicker } from '@ferrucc-io/emoji-picker';
import { Loader2 } from 'lucide-react';
import { Workspace } from '@/lib/supabase/types';
import { WorkspaceModelSelector } from './WorkspaceModelSelector';

const workspaceSchema = z.object({
  name: z
    .string()
    .min(1, 'Name is required')
    .max(80, 'Name must be 80 characters or less'),
  description: z
    .string()
    .max(500, 'Description must be 500 characters or less')
    .optional(),
  icon: z.string().optional(),
  default_model_id: z.string().optional(),
});

type WorkspaceFormData = z.infer<typeof workspaceSchema>;

interface WorkspaceFormModalProps {
  mode: 'create' | 'edit';
  workspace?: Workspace; // Optional for create, required for edit
  onSuccess?: (workspaceId?: string) => void;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function WorkspaceFormModal({
  mode,
  workspace,
  onSuccess,
  open,
  onOpenChange,
}: WorkspaceFormModalProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isEmojiPickerOpen, setIsEmojiPickerOpen] = useState(false);
  const emojiPickerRef = useRef<HTMLDivElement>(null);

  // Handle clicks outside of emoji picker
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        emojiPickerRef.current &&
        !emojiPickerRef.current.contains(event.target as Node)
      ) {
        setIsEmojiPickerOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const form = useForm<WorkspaceFormData>({
    resolver: zodResolver(workspaceSchema),
    defaultValues: {
      name: workspace?.name || '',
      description: workspace?.description || '',
      icon: workspace?.icon || '',
      default_model_id: workspace?.default_model_id || undefined,
    },
  });

  // Reset form data when workspace changes (for edit mode) and modal is opened
  useEffect(() => {
    if (open && workspace && mode === 'edit') {
      form.reset({
        name: workspace.name || '',
        description: workspace.description || '',
        icon: workspace.icon || '',
        default_model_id: workspace.default_model_id || undefined,
      });
    }
  }, [open, workspace, form, mode]);

  // Effect to close emoji picker when modal is closed externally
  useEffect(() => {
    if (!open) {
      setIsEmojiPickerOpen(false);
    }
  }, [open]);

  async function onSubmit(data: WorkspaceFormData) {
    setIsSubmitting(true);
    try {
      let response;

      if (mode === 'create') {
        response = await fetch('/api/workspaces', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        });
      } else {
        response = await fetch(`/api/workspaces/${workspace?.id}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        });
      }

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to ${mode} workspace`);
      }

      const result = await response.json();
      toast.success(
        `Workspace ${mode === 'create' ? 'created' : 'updated'} successfully`
      );
      onOpenChange(false);
      form.reset();
      router.refresh();

      if (onSuccess) {
        onSuccess(mode === 'create' ? result.workspace.id : undefined);
      }
    } catch (error) {
      console.error(`Error ${mode}ing workspace:`, error);
      toast.error(
        error instanceof Error ? error.message : `Failed to ${mode} workspace`
      );
    } finally {
      setIsSubmitting(false);
    }
  }

  const handleEmojiSelect = (emoji: string) => {
    form.setValue('icon', emoji);
    setIsEmojiPickerOpen(false);
  };

  // Renamed from handleOpenChangeInternal. This is called by Dialog's onOpenChange.
  const handleDialogInteraction = (newOpenState: boolean) => {
    // If Dialog requests to close (e.g., Escape, overlay click, DialogClose button),
    // clean up internal state like emoji picker.
    if (!newOpenState) {
      setIsEmojiPickerOpen(false);
    }
    // Propagate the open state change request to the parent component.
    onOpenChange(newOpenState);
  };

  return (
    <Dialog open={open} onOpenChange={handleDialogInteraction}>
      <DialogContent className='sm:max-w-[425px]'>
        <DialogHeader>
          <DialogTitle>
            {mode === 'create' ? 'Create New' : 'Edit'} Workspace
          </DialogTitle>
          <DialogDescription>
            {mode === 'create'
              ? 'Give your new workspace a name and optional details.'
              : 'Update workspace details'}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className='space-y-4 py-4'
          >
            <FormField
              control={form.control}
              name='name'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='My Project'
                      {...field}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='description'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder='What this workspace is about...'
                      className='resize-none min-h-[100px]'
                      {...field}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='default_model_id'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Default Model (Optional)</FormLabel>
                  <FormControl>
                    <WorkspaceModelSelector
                      value={field.value}
                      onChange={field.onChange}
                      disabled={isSubmitting}
                      placeholder='Select a default model for this workspace'
                      modal={true}
                    />
                  </FormControl>
                  <FormDescription>
                    This model will be pre-selected when starting conversations in this workspace
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='icon'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Icon (Optional)</FormLabel>
                  <FormControl>
                    <div className='relative' ref={emojiPickerRef}>
                      <Button
                        type='button'
                        variant='outline'
                        onClick={() => setIsEmojiPickerOpen(!isEmojiPickerOpen)}
                        className='w-20 text-center'
                      >
                        {field.value || 'Select Icon'}
                      </Button>
                      {isEmojiPickerOpen && (
                        <div className='absolute z-10 mt-2 w-full'>
                          <EmojiPicker
                            className="font-['Lato'] w-[380px] border-none"
                            emojisPerRow={9}
                            emojiSize={36}
                            onEmojiSelect={handleEmojiSelect}
                          >
                            <EmojiPicker.Header>
                              <EmojiPicker.Input
                                placeholder='Search all emoji'
                                className='h-[36px] bg-white dark:bg-zinc-800 border border-zinc-300 dark:border-zinc-600 w-full rounded-[8px] text-[15px] focus:shadow-[0_0_0_1px_#1d9bd1,0_0_0_6px_rgba(29,155,209,0.3)] dark:focus:shadow-[0_0_0_1px_#1d9bd1,0_0_0_6px_rgba(29,155,209,0.3)] focus:border-transparent focus:outline-none mb-1'
                                hideIcon
                              />
                            </EmojiPicker.Header>
                            <EmojiPicker.Group>
                              <EmojiPicker.List containerHeight={320} />
                            </EmojiPicker.Group>
                            <EmojiPicker.Preview>
                              {({ previewedEmoji }) => (
                                <>
                                  {previewedEmoji ? (
                                    <EmojiPicker.Content />
                                  ) : (
                                    <button>Add Emoji</button>
                                  )}
                                </>
                              )}
                            </EmojiPicker.Preview>
                          </EmojiPicker>
                        </div>
                      )}
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <DialogClose asChild>
                <Button type='button' variant='outline' disabled={isSubmitting}>
                  Cancel
                </Button>
              </DialogClose>
              <Button type='submit' disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                    {mode === 'create' ? 'Creating...' : 'Saving...'}
                  </>
                ) : mode === 'create' ? (
                  'Create Workspace'
                ) : (
                  'Save Changes'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
