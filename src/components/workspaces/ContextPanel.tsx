'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { WorkspaceFile, WorkspaceNote } from '@/lib/supabase/types';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  FileTextIcon,
  ChevronDown,
  ChevronUp,
  Search,
  X,
  MoreHorizontal,
  Edit,
  Trash2,
  FileIcon,
  Loader2,
  AlertCircle,
  CheckCircle2,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { toast } from 'sonner';
import { NoteModal } from './NoteModal';

interface ContextPanelProps {
  files: WorkspaceFile[];
  notes: WorkspaceNote[];
  workspaceId: string;
  onUpdate?: () => void; // Callback to refresh data
}

function formatFileSize(bytes?: number | null): string {
  if (!bytes) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  if (isNaN(i) || i < 0) return '0 B'; // Handle potential NaN or negative index for 0 bytes
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
}

interface FileDisplayProps {
  file: WorkspaceFile;
  workspaceId: string;
  onUpdate?: () => void;
}

interface NoteDisplayProps {
  note: WorkspaceNote;
  workspaceId: string;
  onUpdate?: () => void;
}

// File Display component with delete functionality
function FileDisplay({ file, workspaceId, onUpdate }: FileDisplayProps) {
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDeleteFile = async () => {
    if (
      !confirm(`Are you sure you want to delete the file "${file.filename}"?`)
    ) {
      return;
    }

    setIsDeleting(true);
    try {
      const response = await fetch(
        `/api/workspaces/${workspaceId}/files?fileId=${file.id}`,
        {
          method: 'DELETE',
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to delete file');
      }

      toast.success(`File "${file.filename}" deleted successfully`);
      if (onUpdate) onUpdate();
      router.refresh();
    } catch (error) {
      console.error('Error deleting file:', error);
      toast.error(
        error instanceof Error ? error.message : 'Failed to delete file'
      );
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div className='border rounded-md px-3 py-2.5 flex items-center gap-3 bg-background hover:bg-muted/30 transition-colors duration-200'>
      <FileIcon className='h-4 w-4 flex-shrink-0 text-muted-foreground/70' />
      <div className='flex-grow min-w-0'>
        <div
          className='font-medium truncate text-sm'
          title={file.filename || 'Unnamed File'}
        >
          {file.filename && file.filename.length > 30
            ? file.filename.slice(0, 30) + '...'
            : file.filename || 'Unnamed File'}
        </div>
        <div className='text-xs text-muted-foreground mt-0.5'>
          <span>{formatFileSize(file.byte_size)}</span>
          {file.mime_type && <span> • {file.mime_type}</span>}
        </div>
      </div>
      <Badge variant='outline' className='flex-shrink-0 px-1.5 h-5 text-xs'>
        File
      </Badge>
      {file.status === 'completed' && (
        <span title='Processed'>
          <CheckCircle2 className='h-3.5 w-3.5 text-green-500 flex-shrink-0' />
        </span>
      )}
      {file.status === 'processing' && (
        <span title='Processing...'>
          <Loader2 className='h-3.5 w-3.5 animate-spin text-blue-500 flex-shrink-0' />
        </span>
      )}
      {file.status === 'failed' && (
        <span title='Processing Failed'>
          <AlertCircle className='h-3.5 w-3.5 text-red-500 flex-shrink-0' />
        </span>
      )}

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant='ghost'
            size='sm'
            className='h-8 w-8 p-0 flex-shrink-0'
            disabled={isDeleting}
          >
            <MoreHorizontal className='h-4 w-4' />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align='end'>
          <DropdownMenuItem
            className='text-destructive focus:text-destructive'
            onClick={handleDeleteFile}
            disabled={isDeleting}
          >
            <Trash2 className='mr-2 h-4 w-4' />
            {isDeleting ? 'Deleting...' : 'Delete File'}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}

// Enhanced Note Display component with better styling
function NoteDisplay({ note, workspaceId, onUpdate }: NoteDisplayProps) {
  const router = useRouter();
  const [isExpanded, setIsExpanded] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDeleteNote = async () => {
    if (!confirm(`Are you sure you want to delete the note "${note.title}"?`)) {
      return;
    }

    setIsDeleting(true);
    try {
      const response = await fetch(
        `/api/workspaces/${workspaceId}/notes?noteId=${note.id}`,
        {
          method: 'DELETE',
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to delete note');
      }

      toast.success(`Note "${note.title}" deleted successfully`);
      if (onUpdate) onUpdate();
      router.refresh();
    } catch (error) {
      console.error('Error deleting note:', error);
      toast.error(
        error instanceof Error ? error.message : 'Failed to delete note'
      );
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div
      className={cn(
        'border rounded-md bg-background hover:bg-muted/30 transition-colors duration-200 overflow-hidden',
        isExpanded ? 'shadow-sm' : ''
      )}
    >
      <div className='flex items-center gap-3 px-3 py-2.5'>
        <div
          className='flex items-center gap-3 flex-grow cursor-pointer'
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <FileTextIcon className='h-4 w-4 flex-shrink-0 text-muted-foreground/70' />
          <span
            className='font-medium truncate flex-grow text-sm'
            title={note.title || 'Untitled Note'}
          >
            {note.title || 'Untitled Note'}
          </span>
          <Badge
            variant='secondary'
            className='flex-shrink-0 px-1.5 h-5 text-xs'
          >
            Note
          </Badge>
          {isExpanded ? (
            <ChevronUp className='h-3.5 w-3.5 text-muted-foreground' />
          ) : (
            <ChevronDown className='h-3.5 w-3.5 text-muted-foreground' />
          )}
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant='ghost'
              size='sm'
              className='h-8 w-8 p-0 flex-shrink-0'
              disabled={isDeleting}
            >
              <MoreHorizontal className='h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end'>
            <NoteModal
              mode='edit'
              note={note}
              workspaceId={workspaceId}
              onSuccess={onUpdate}
            >
              <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                <Edit className='mr-2 h-4 w-4' />
                Edit Note
              </DropdownMenuItem>
            </NoteModal>
            <DropdownMenuItem
              className='text-destructive focus:text-destructive'
              onClick={handleDeleteNote}
              disabled={isDeleting}
            >
              <Trash2 className='mr-2 h-4 w-4' />
              {isDeleting ? 'Deleting...' : 'Delete Note'}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      {isExpanded && (
        <div className='px-3 py-2 text-sm text-muted-foreground whitespace-pre-wrap border-t bg-muted/10'>
          {note.body || 'No content'}
        </div>
      )}
    </div>
  );
}

export function ContextPanel({
  files,
  notes,
  workspaceId,
  onUpdate,
}: ContextPanelProps) {
  const [searchTerm, setSearchTerm] = useState('');

  const filteredFiles = files.filter((file) =>
    (file.filename || '').toLowerCase().includes(searchTerm.toLowerCase())
  );
  const filteredNotes = notes.filter(
    (note) =>
      (note.title || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (note.body || '').toLowerCase().includes(searchTerm.toLowerCase())
  );

  const hasContext = filteredFiles.length > 0 || filteredNotes.length > 0;
  const totalCount = filteredFiles.length + filteredNotes.length;

  return (
    <div className='space-y-4'>
      <div className='relative'>
        <Search className='absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground/70' />
        <Input
          placeholder='Search context...'
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className='pl-9 h-9 text-sm'
        />
        {searchTerm && (
          <button
            onClick={() => setSearchTerm('')}
            className='absolute right-2.5 top-2.5 text-muted-foreground hover:text-foreground'
          >
            <X className='h-4 w-4' />
          </button>
        )}
      </div>

      {!hasContext && (
        <div className='text-center text-muted-foreground py-8 px-4 border rounded-md bg-muted/10'>
          {searchTerm ? (
            <>No context found for &quot;{searchTerm}&quot;.</>
          ) : (
            <>No context items have been added yet.</>
          )}
        </div>
      )}

      {hasContext && (
        <div>
          <div className='flex items-center justify-between mb-2'>
            <h4 className='text-sm font-medium text-foreground'>
              {searchTerm
                ? `${totalCount} result${totalCount !== 1 ? 's' : ''}`
                : 'All context items'}
            </h4>
          </div>

          <ScrollArea className='h-[calc(100vh-280px)] pr-4 -mr-4'>
            <div className='space-y-2'>
              {filteredFiles.length > 0 && (
                <div className='space-y-2'>
                  {filteredFiles.map((file) => (
                    <FileDisplay
                      key={`file-${file.id}`}
                      file={file}
                      workspaceId={workspaceId}
                      onUpdate={onUpdate}
                    />
                  ))}
                </div>
              )}

              {filteredNotes.length > 0 && (
                <div className='space-y-2'>
                  {filteredNotes.map((note) => (
                    <NoteDisplay
                      key={`note-${note.id}`}
                      note={note}
                      workspaceId={workspaceId}
                      onUpdate={onUpdate}
                    />
                  ))}
                </div>
              )}
            </div>
          </ScrollArea>
        </div>
      )}
    </div>
  );
}
