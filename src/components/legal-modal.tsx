'use client';

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader,
  Di<PERSON>Title,
  Di<PERSON>Trigger,
  DialogFooter,
  DialogClose
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import ReactMarkdown from 'react-markdown';

interface LegalModalProps {
  triggerText: string;
  title: string;
  content: string;
  triggerClassName?: string; // Optional class for the trigger link
}

export function LegalModal({
  triggerText,
  title,
  content,
  triggerClassName,
}: LegalModalProps) {
  return (
    <Dialog>
      <DialogTrigger asChild>
        {/* Using a span for inline text triggering, styled like a link */}
        <span
          className={`cursor-pointer font-medium text-indigo-600 hover:text-indigo-500 ${triggerClassName}`}
        >
          {triggerText}
        </span>
      </DialogTrigger>
      <DialogContent className='max-w-3xl max-h-[80vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle className='text-2xl'>{title}</DialogTitle>
        </DialogHeader>
        <div className='prose prose-sm max-w-none py-4 text-sm text-gray-600'>
          {/* Render markdown content */}
          <ReactMarkdown>{content}</ReactMarkdown>
        </div>
        <DialogFooter className='sm:justify-end'>
          <DialogClose asChild>
            <Button type='button' variant='secondary'>
              Close
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}