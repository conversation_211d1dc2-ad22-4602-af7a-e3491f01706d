import React from 'react';
import { Loader2 } from 'lucide-react';
import { motion } from 'framer-motion';
import { GroupConversation } from '@/lib/supabase/db';
import { SidebarState } from './AppSidebar';
import { useInfiniteScroll } from '@/components/sidebar/hooks/useInfiniteScroll';
import { ConversationItem } from './ConversationItem';
import { GroupHeading } from './GroupHeading';
import { EmptyConversation } from './EmptyConversation';

type ConversationListProps = {
  state: SidebarState;
  groupedConversations: Record<string, GroupConversation[]>;
  orderedGroups: string[];
  conversations: GroupConversation[];
  selectedConversation: GroupConversation | null;
  goToConversation: (conversation: GroupConversation) => void;
  hasMoreConversations: boolean;
  fetchMoreConversations: () => void;
  isFetchingMoreConversations: boolean;
  isConversationListLoading: boolean;
  hasInitiallyLoaded: boolean;
  sseConnections: Map<string, { groupConversationId?: string }> | undefined;
  openDropdownId: string | null;
  setOpenDropdownId: (id: string | null) => void;
  toggleFavorite: (conversation: GroupConversation) => void;
  archiveConversation: (conversation: GroupConversation) => void;
  handleRenameClick: (conversation: GroupConversation) => void;
  handleDeleteClick: (conversation: GroupConversation) => void;
  /** Determines if the date group headings within the list are collapsible (defaults to true) */
  areDateGroupsCollapsible?: boolean;
};

export function ConversationList({
  state,
  groupedConversations,
  orderedGroups,
  conversations,
  selectedConversation,
  goToConversation,
  hasMoreConversations,
  fetchMoreConversations,
  isFetchingMoreConversations,
  isConversationListLoading,
  hasInitiallyLoaded,
  sseConnections,
  openDropdownId,
  setOpenDropdownId,
  toggleFavorite,
  archiveConversation,
  handleRenameClick,
  handleDeleteClick,
  areDateGroupsCollapsible = true,
}: ConversationListProps) {
  const observerTarget = React.useRef<HTMLDivElement>(null);

  // Use the custom infinite scroll hook
  useInfiniteScroll(observerTarget, {
    hasMore: hasMoreConversations,
    fetchMore: fetchMoreConversations,
    isFetching: isFetchingMoreConversations,
  });

  // Loading state - only show full spinner on initial load
  if (isConversationListLoading && !hasInitiallyLoaded) {
    return (
      <div className='flex items-center justify-center h-full'>
        {state !== 'minimized' ? (
          <span className='flex items-center gap-2'>
            <Loader2 className='h-4 w-4 animate-spin' /> Loading...
          </span>
        ) : (
          <Loader2 className='h-5 w-5 animate-spin' />
        )}
      </div>
    );
  }

  // Empty state
  if (!conversations || conversations.length === 0) {
    return (
      <>
        {state !== 'minimized' ? (
          <EmptyConversation text='No conversations yet. Start a new chat!' />
        ) : (
          <div className='flex items-center justify-center h-full text-center text-sm text-muted-foreground px-4'>
            <div className='h-5 w-5' />
          </div>
        )}
      </>
    );
  }

  // Return full conversations grouped by date
  if (state !== 'minimized') {
    return (
      <>
        {/* Background refresh indicator */}
        {/* {isBackgroundRefreshing && (
          <div className='flex items-center justify-center py-1 mb-2'>
            <span className='flex items-center gap-2 text-xs text-muted-foreground'>
              <Loader2 className='h-3 w-3 animate-spin' /> Refreshing...
            </span>
          </div>
        )} */}

        {orderedGroups.map(
          (group) =>
            groupedConversations[group] && (
              <motion.div key={group} layout='position' className='space-y-1'>
                <GroupHeading
                  group={group}
                  isCollapsed={false}
                  isCollapsible={areDateGroupsCollapsible}
                />

                {groupedConversations[group].map((conversation) => (
                  <ConversationItem
                    key={conversation.id}
                    conversation={conversation}
                    isMinimized={false}
                    isSelected={selectedConversation?.id === conversation.id}
                    isDropdownOpen={openDropdownId === conversation.id}
                    sseConnections={sseConnections}
                    onSelect={() => goToConversation(conversation)}
                    onDropdownChange={(isOpen: boolean) =>
                      setOpenDropdownId(isOpen ? conversation.id : null)
                    }
                    onToggleFavorite={() => toggleFavorite(conversation)}
                    onArchive={() => archiveConversation(conversation)}
                    onRename={() => handleRenameClick(conversation)}
                    onDelete={() => handleDeleteClick(conversation)}
                    group={group}
                  />
                ))}
              </motion.div>
            )
        )}
        {/* Intersection Observer target element */}
        <div
          ref={observerTarget}
          className='h-8 w-full flex items-center justify-center'
        >
          {isFetchingMoreConversations && (
            <span className='flex items-center gap-2 text-xs text-muted-foreground'>
              <Loader2 className='h-3 w-3 animate-spin' /> Loading more...
            </span>
          )}
        </div>
      </>
    );
  }

  // Minimized view - flat list with icons only
  return (
    <>
      {/* Background refresh indicator for minimized view */}
      {/* {isBackgroundRefreshing && (
        <div className='flex justify-center py-1 mb-2'>
          <Loader2 className='h-3 w-3 animate-spin' />
        </div>
      )} */}

      {conversations
        .sort((a, b) => {
          // Show favorites first
          if (a.is_favorite && !b.is_favorite) return -1;
          if (!a.is_favorite && b.is_favorite) return 1;

          // Then sort by created_at (newest first)
          const dateA = new Date(a.created_at || 0).getTime();
          const dateB = new Date(b.created_at || 0).getTime();
          return dateB - dateA;
        })
        .map((conversation) => (
          <ConversationItem
            key={conversation.id}
            conversation={conversation}
            isMinimized={true}
            isSelected={selectedConversation?.id === conversation.id}
            isDropdownOpen={false}
            sseConnections={sseConnections}
            onSelect={() => goToConversation(conversation)}
          />
        ))}

      {/* Minimized loading indicator */}
      {isFetchingMoreConversations && (
        <div className='flex justify-center py-2'>
          <Loader2 className='h-4 w-4 animate-spin' />
        </div>
      )}

      {/* Observer for minimized view */}
      <div ref={observerTarget} className='h-4 w-full' />
    </>
  );
}
