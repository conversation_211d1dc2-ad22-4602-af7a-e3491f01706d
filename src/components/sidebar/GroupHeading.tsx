import React from 'react';
import { ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';

void React;

type GroupHeadingProps = {
  group: string;
  isCollapsed: boolean;
  isCollapsible?: boolean;
};

export function GroupHeading({
  group,
  isCollapsed,
  isCollapsible = true,
}: GroupHeadingProps) {
  const isFavorites = group === 'Favorite Chats';

  return (
    <div
      className={cn(
        'w-full flex items-center gap-1 px-2 pt-3 pb-1 text-xs font-semibold uppercase tracking-wider select-none mt-4',
        isFavorites ? 'text-yellow-500' : 'text-muted-foreground',
        !isCollapsible && 'cursor-default'
      )}
    >
      {/* Group title */}
      <span className='flex-1 text-left'>{group}</span>

      {/* Collapse / expand chevron - only render if collapsible */}
      {isCollapsible && (
        <ChevronDown
          className={cn(
            'h-3 w-3 shrink-0 transition-transform duration-200',
            isCollapsed && '-rotate-90'
          )}
        />
      )}
    </div>
  );
}
