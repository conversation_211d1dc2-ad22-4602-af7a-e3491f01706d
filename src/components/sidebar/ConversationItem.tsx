import React from 'react';
import {
  MessageSquare,
  Star,
  EllipsisIcon,
  PencilIcon,
  ArchiveIcon,
  TrashIcon,
  Briefcase,
} from 'lucide-react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { GroupConversation } from '@/lib/supabase/db';
import { useStreamingIndicator } from '@/components/sidebar/hooks/useStreamingIndicator';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from '@/components/ui/tooltip';
void React;

type ConversationItemProps = {
  conversation: GroupConversation;
  isMinimized: boolean;
  isSelected: boolean;
  isDropdownOpen?: boolean;
  sseConnections?: Map<string, { groupConversationId?: string }>;
  group?: string;
  onSelect: () => void;
  onDropdownChange?: (open: boolean) => void;
  onToggleFavorite?: () => void;
  onArchive?: () => void;
  onRename?: (e: React.MouseEvent<HTMLDivElement>) => void;
  onDelete?: (e: React.MouseEvent<HTMLDivElement>) => void;
};

export function ConversationItem({
  conversation,
  isMinimized,
  isSelected,
  isDropdownOpen = false,
  sseConnections,
  group,
  onSelect,
  onDropdownChange,
  onToggleFavorite,
  onArchive,
  onRename,
  onDelete,
}: ConversationItemProps) {
  const { isGroupConversationStreaming } =
    useStreamingIndicator(sseConnections);
  const isStreaming = isGroupConversationStreaming(conversation.id);
  const isFavorite = conversation.is_favorite;
  const isWorkspaceChat = !!conversation.workspace_id;
  const title = conversation.title || 'New Conversation';

  // Minimized version (icon only)
  if (isMinimized) {
    return (
      <motion.button
        layout='position'
        className={cn(
          'flex items-center justify-center p-2 rounded-md hover:bg-sidebar-hover text-left group/item w-full text-sm',
          {
            'bg-sidebar-active font-medium': isSelected,
            'text-sidebar-fg/90 hover:text-sidebar-fg': !isSelected,
          }
        )}
        onClick={onSelect}
        whileTap={{ scale: 0.98 }}
      >
        <Tooltip>
          <TooltipTrigger asChild>
            <div className='relative flex justify-center w-full gap-1'>
              {isFavorite && group !== 'Favorites' ? (
                <Star className='h-4 w-4 shrink-0 fill-yellow-400 text-yellow-400' />
              ) : isWorkspaceChat ? (
                <div className='flex items-center justify-center w-5 h-5 rounded-full bg-primary/10 text-primary'>
                  <Briefcase className='h-3 w-3 shrink-0' />
                </div>
              ) : (
                <MessageSquare className='h-4 w-4 shrink-0' />
              )}
              {isStreaming && (
                <span className='absolute -top-1 -right-1 h-2 w-2 rounded-full bg-blue-500 animate-pulse' />
              )}
            </div>
          </TooltipTrigger>
          <TooltipContent side='right'>
            <p>{title}</p>
            {isWorkspaceChat && (
              <p className='text-xs text-muted-foreground mt-1 flex items-center gap-1.5'>
                <Briefcase className='h-3 w-3' />
                Workspace chat
              </p>
            )}
          </TooltipContent>
        </Tooltip>
      </motion.button>
    );
  }

  // Full version
  return (
    <motion.button
      layout='position'
      className={cn(
        'flex items-center gap-2 p-2 rounded-md hover:bg-sidebar-hover text-left group/item w-full text-sm',
        {
          'bg-sidebar-active font-medium': isSelected,
          'text-sidebar-fg/90 hover:text-sidebar-fg': !isSelected,
          'bg-sidebar-hover': isDropdownOpen,
        }
      )}
      onClick={onSelect}
      whileTap={{ scale: 0.98 }}
    >
      <motion.div
        initial={{ opacity: 0, width: 0 }}
        animate={{ opacity: 1, width: '100%' }}
        exit={{ opacity: 0, width: 0 }}
        transition={{ duration: 0.15 }}
        className='flex flex-row items-center justify-between min-w-0 overflow-hidden group/dropdown text-sm'
      >
        <span className='truncate whitespace-nowrap flex items-center gap-2'>
          {/* Workspace indicator */}
          {isWorkspaceChat && (
            <div className='flex items-center rounded-full bg-primary/10 text-primary'>
              <Briefcase className='h-2.5 w-2.5 shrink-0' />
            </div>
          )}
          {title.trim()}
          {/* Streaming indicator */}
          {isStreaming && (
            <span className='ml-1 inline-block h-2 w-2 rounded-full bg-blue-500 animate-pulse' />
          )}
        </span>

        {/* Only render dropdown menu if handlers are provided */}
        {onDropdownChange &&
          onToggleFavorite &&
          onArchive &&
          onRename &&
          onDelete && (
            <DropdownMenu open={isDropdownOpen} onOpenChange={onDropdownChange}>
              <DropdownMenuTrigger asChild>
                <div
                  className='text-xs text-zinc-400 hover:text-zinc-500 opacity-0 transition-opacity duration-150 group-hover/item:opacity-100 data-[state=open]:opacity-100 data-[state=open]:text-zinc-500'
                  onClick={(e) => e.stopPropagation()}
                >
                  <EllipsisIcon className='h-4 w-4' />
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuRadioGroup>
                  <DropdownMenuRadioItem
                    value='rename'
                    className='data-[state=checked]:bg-sidebar-hover flex items-center gap-2 cursor-pointer'
                    onClick={(e) => {
                      e.stopPropagation();
                      setTimeout(() => onRename(e), 100);
                    }}
                  >
                    <PencilIcon className='h-4 w-4 shrink-0' />
                    <span>Rename</span>
                  </DropdownMenuRadioItem>
                  <DropdownMenuRadioItem
                    value='favorite'
                    className='data-[state=checked]:bg-sidebar-hover flex items-center gap-2 cursor-pointer'
                    onClick={(e) => {
                      e.stopPropagation();
                      onToggleFavorite();
                    }}
                  >
                    <Star
                      className={`h-4 w-4 shrink-0 ${
                        isFavorite ? 'fill-yellow-400 text-yellow-400' : ''
                      }`}
                    />
                    <span>
                      {isFavorite
                        ? 'Remove from favorites'
                        : 'Add to favorites'}
                    </span>
                  </DropdownMenuRadioItem>
                  <DropdownMenuRadioItem
                    value='archive'
                    className='data-[state=checked]:bg-sidebar-hover flex items-center gap-2 cursor-pointer'
                    onClick={(e) => {
                      e.stopPropagation();
                      onArchive();
                    }}
                  >
                    <ArchiveIcon className='h-4 w-4 shrink-0' />
                    <span>Archive</span>
                  </DropdownMenuRadioItem>
                  <DropdownMenuRadioItem
                    value='delete'
                    className='data-[state=checked]:bg-sidebar-hover flex items-center gap-2 cursor-pointer text-red-500 focus:bg-red-100 focus:text-red-600'
                    onClick={(e) => {
                      e.stopPropagation();
                      setTimeout(() => onDelete(e), 100);
                    }}
                  >
                    <TrashIcon className='h-4 w-4 shrink-0' />
                    <span>Delete</span>
                  </DropdownMenuRadioItem>
                </DropdownMenuRadioGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
      </motion.div>
    </motion.button>
  );
}
