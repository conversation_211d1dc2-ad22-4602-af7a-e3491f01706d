import React from 'react';
import { GroupConversation } from '@/lib/supabase/db';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';

// Use discriminated union for dialog state
type DialogState =
  | { kind: 'none' }
  | { kind: 'rename'; conversation: GroupConversation }
  | { kind: 'delete'; conversation: GroupConversation };

type DialogManagerProps = {
  onRename: (conversation: GroupConversation, newTitle: string) => void;
  onDelete: (conversation: GroupConversation) => void;
};

export function DialogManager({ onRename, onDelete }: DialogManagerProps) {
  const [dialogState, setDialogState] = React.useState<DialogState>({
    kind: 'none',
  });
  const [conversationTitle, setConversationTitle] = React.useState<string>('');

  // Handler for opening rename dialog
  const handleRenameClick = (conversation: GroupConversation) => {
    setConversationTitle(conversation.title || 'New Conversation');
    setDialogState({ kind: 'rename', conversation });
  };

  // Handler for opening delete dialog
  const handleDeleteClick = (conversation: GroupConversation) => {
    setDialogState({ kind: 'delete', conversation });
  };

  // Handler for closing any dialog
  const handleCloseDialog = () => {
    setDialogState({ kind: 'none' });
  };

  return {
    dialogs: (
      <>
        <Dialog
          open={dialogState.kind === 'rename'}
          onOpenChange={(open) => !open && handleCloseDialog()}
          key='rename-dialog'
        >
          <DialogContent className='sm:max-w-[425px]'>
            <DialogHeader>
              <DialogTitle>Rename Conversation</DialogTitle>
              <DialogDescription>
                Enter a new name for this conversation.
              </DialogDescription>
            </DialogHeader>
            <div className='grid gap-4 py-4'>
              <div className='grid grid-cols-4 items-center gap-4'>
                <Label htmlFor='name' className='text-right'>
                  Name
                </Label>
                <Input
                  id='name'
                  value={conversationTitle}
                  onChange={(e) => setConversationTitle(e.target.value)}
                  className='col-span-3'
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                type='submit'
                onClick={() => {
                  if (dialogState.kind === 'rename') {
                    onRename(dialogState.conversation, conversationTitle);
                    handleCloseDialog();
                  }
                }}
              >
                Save changes
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        <Dialog
          open={dialogState.kind === 'delete'}
          onOpenChange={(open) => !open && handleCloseDialog()}
          key='delete-dialog'
        >
          <DialogContent className='sm:max-w-[425px]'>
            <DialogHeader>
              <DialogTitle>Delete Conversation</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete this conversation? This action
                cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button
                variant='destructive'
                onClick={() => {
                  if (dialogState.kind === 'delete') {
                    onDelete(dialogState.conversation);
                    handleCloseDialog();
                  }
                }}
              >
                Delete
              </Button>
              <Button variant='outline' onClick={handleCloseDialog}>
                Cancel
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </>
    ),
    handlers: {
      handleRenameClick,
      handleDeleteClick,
    },
  };
}
