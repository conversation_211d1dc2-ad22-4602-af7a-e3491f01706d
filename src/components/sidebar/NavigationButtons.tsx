'use client';
import { But<PERSON> } from '@/components/ui/button';
import Link from 'next/link';
import { Briefcase, MessageSquare, BookOpen } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export function NavigationButtons() {
  const router = useRouter();

  // Prefetch navigation routes on component mount
  useEffect(() => {
    router.prefetch('/workspaces');
    router.prefetch('/chats');
    router.prefetch('/prompts');
  }, [router]);

  return (
    <div className="p-3 space-y-1">
      <Link href="/workspaces" passHref>
        <Button variant="ghost" className="w-full justify-start">
          <Briefcase className="mr-2 h-4 w-4" />
          Workspaces
        </Button>
      </Link>
      <Button
        variant="ghost"
        className="w-full justify-start"
        onClick={() => router.push('/chats')}
      >
        <MessageSquare className="mr-2 h-4 w-4" />
        Chats
      </Button>
      <Button
        variant="ghost"
        className="w-full justify-start"
        onClick={() => router.push('/prompts')}
      >
        <BookOpen className="mr-2 h-4 w-4" />
        Prompt Library
      </Button>
    </div>
  );
}
