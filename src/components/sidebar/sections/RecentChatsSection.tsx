import { Clock } from 'lucide-react';
import { CollapsibleSection } from './CollapsibleSection';
import { ConversationList } from '../ConversationList';
import { GroupConversation } from '@/lib/supabase/db';

interface StreamSubscriptionLike {
  groupConversationId?: string;
}

type RecentChatsSectionProps = {
  isExpanded: boolean;
  onToggleSection: () => void;
  sidebarState: 'full' | 'minimized' | 'hidden';
  groupedConversations: Record<string, GroupConversation[]>;
  orderedGroups: string[];
  conversations: GroupConversation[];
  selectedConversation: GroupConversation | null;
  goToConversation: (conversation: GroupConversation) => void;
  hasMoreConversations: boolean;
  fetchMoreConversations: () => Promise<void>;
  isFetchingMoreConversations: boolean;
  isConversationListLoading: boolean;
  hasInitiallyLoaded: boolean;
  sseConnections: Map<string, StreamSubscriptionLike>;
  openDropdownId: string | null;
  setOpenDropdownId: (id: string | null) => void;
  toggleFavorite: (conversation: GroupConversation) => void;
  archiveConversation: (conversation: GroupConversation) => void;
  handleRenameClick: (conversation: GroupConversation) => void;
  handleDeleteClick: (conversation: GroupConversation) => void;
  areDateGroupsCollapsible?: boolean;
};

export function RecentChatsSection({
  isExpanded,
  onToggleSection,
  sidebarState,
  groupedConversations,
  orderedGroups,
  conversations,
  selectedConversation,
  goToConversation,
  hasMoreConversations,
  fetchMoreConversations,
  isFetchingMoreConversations,
  isConversationListLoading,
  hasInitiallyLoaded,
  sseConnections,
  openDropdownId,
  setOpenDropdownId,
  toggleFavorite,
  archiveConversation,
  handleRenameClick,
  handleDeleteClick,
  areDateGroupsCollapsible = false
}: RecentChatsSectionProps) {
  return (
    <CollapsibleSection
      title='Recent Chats'
      icon={<Clock className='h-4 w-4' />}
      isExpanded={isExpanded}
      onToggle={onToggleSection}
    >
      <ConversationList
        state={sidebarState}
        groupedConversations={groupedConversations}
        orderedGroups={orderedGroups}
        conversations={conversations}
        selectedConversation={selectedConversation}
        goToConversation={goToConversation}
        hasMoreConversations={hasMoreConversations}
        fetchMoreConversations={fetchMoreConversations}
        isFetchingMoreConversations={isFetchingMoreConversations}
        isConversationListLoading={isConversationListLoading}
        hasInitiallyLoaded={hasInitiallyLoaded}
        sseConnections={sseConnections}
        openDropdownId={openDropdownId}
        setOpenDropdownId={setOpenDropdownId}
        toggleFavorite={toggleFavorite}
        archiveConversation={archiveConversation}
        handleRenameClick={handleRenameClick}
        handleDeleteClick={handleDeleteClick}
        areDateGroupsCollapsible={areDateGroupsCollapsible}
      />
    </CollapsibleSection>
  );
}