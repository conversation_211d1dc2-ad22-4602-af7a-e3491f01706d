'use client';
import React from 'react';
import { ChevronDown, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';

type CollapsibleSectionProps = {
  title: string;
  icon?: React.ReactNode;
  isExpanded: boolean;
  onToggle: () => void;
  children: React.ReactNode;
};

export function CollapsibleSection({
  title,
  // icon,
  isExpanded,
  onToggle,
  children,
}: CollapsibleSectionProps) {
  return (
    <div className='!mb-4'>
      <Button
        variant='ghost'
        size='sm'
        className='w-full justify-between text-sm font-medium'
        onClick={onToggle}
      >
        <div className='flex items-center gap-2'>
          {/* {icon} */}
          {title}
        </div>
        {isExpanded ? (
          <ChevronDown className='mr-2 h-4 w-4' />
        ) : (
          <ChevronRight className='mr-2 h-4 w-4' />
        )}
      </Button>
      {isExpanded && <div className='ml-4 mt-1'>{children}</div>}
    </div>
  );
}
