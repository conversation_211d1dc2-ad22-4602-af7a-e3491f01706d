import { useCallback } from 'react';
import { GroupConversation } from '@/lib/supabase/db';
import { ConversationItem } from '../ConversationItem';
import { Star } from 'lucide-react';
import { EmptyConversation } from '../EmptyConversation';
import { CollapsibleSection } from './CollapsibleSection';

// Shared icon component
export const StarIcon = <Star className='h-4 w-4 text-yellow-500 fill-yellow-500' />;

interface StreamSubscriptionLike {
  groupConversationId?: string;
}

type ConversationHandler = (conversation: GroupConversation) => void;

type FavoriteChatsProps = {
  isExpanded: boolean;
  conversations: GroupConversation[];
  selectedConversationId: string | undefined;
  openDropdownId: string | null;
  sseConnections: Map<string, StreamSubscriptionLike>;
  onToggleSection: () => void;
  onSelect: ConversationHandler;
  onDropdownChange: (id: string | null) => void;
  onToggleFavorite: ConversationHandler;
  onArchive: ConversationHandler;
  onRename: ConversationHandler;
  onDelete: ConversationHandler;
};

export function FavoriteChatsSection({
  isExpanded,
  conversations,
  selectedConversationId,
  openDropdownId,
  sseConnections,
  onToggleSection,
  onSelect,
  onDropdownChange,
  onToggleFavorite,
  onArchive,
  onRename,
  onDelete
}: FavoriteChatsProps) {
  const handleDropdownChange = useCallback((conversationId: string, isOpen: boolean) => {
    onDropdownChange(isOpen ? conversationId : null);
  }, [onDropdownChange]);

  return (
    <CollapsibleSection
      title='Favorite Chats'
      icon={StarIcon}
      isExpanded={isExpanded}
      onToggle={onToggleSection}
    >
      {isExpanded && (
        conversations.length > 0 ? (
          conversations.map((conversation) => (
            <ConversationItem
              key={conversation.id}
              conversation={conversation}
              isMinimized={false}
              isSelected={selectedConversationId === conversation.id}
              isDropdownOpen={openDropdownId === conversation.id}
              sseConnections={sseConnections}
              onSelect={() => onSelect(conversation)}
              onDropdownChange={(isOpen: boolean) => handleDropdownChange(conversation.id, isOpen)}
              onToggleFavorite={() => onToggleFavorite(conversation)}
              onArchive={() => onArchive(conversation)}
              onRename={() => onRename(conversation)}
              onDelete={() => onDelete(conversation)}
            />
          ))
        ) : (
          <EmptyConversation text='Favorite chats that you use often.' />
        )
      )}
    </CollapsibleSection>
  );
}