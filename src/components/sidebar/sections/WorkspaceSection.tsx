import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { SidebarContext } from '@/providers/SidebarProvider';
import { Workspace } from '@/lib/supabase/types';
import { Loader2, AlertTriangle } from 'lucide-react';
import { createClient } from '@/utils/supabase/client';
import { RealtimePostgresChangesPayload } from '@supabase/supabase-js';

export type FavoriteWorkspacesSectionProps = {
  isExpanded: boolean;
  onFavoritesChange?: (hasFavorites: boolean) => void;
};

export function WorkspaceSection({
  isExpanded,
  onFavoritesChange,
}: FavoriteWorkspacesSectionProps) {
  const router = useRouter();
  const { sidebarState } = React.useContext(SidebarContext);
  const [favoriteWorkspaces, setFavoriteWorkspaces] = React.useState<
    Workspace[]
  >([]);
  const [isLoading, setIsLoading] = React.useState(true);
  const supabase = createClient();

  useEffect(() => {
    fetchWorkspaces();

    // Set up real-time subscription for workspace status changes
    const channel = supabase
      .channel('sidebar-workspace-status-changes')
      .on(
        'postgres_changes',
        {
          event: '*', // Listen to all events
          schema: 'public',
          table: 'workspaces',
          filter: 'is_favorite=eq.true', // Only listen for favorite workspaces
        },
        (payload: RealtimePostgresChangesPayload<Workspace>) => {

          if (payload.eventType === 'UPDATE') {
            const updatedWorkspace = payload.new;

            // Update the workspace in the state
            setFavoriteWorkspaces((current) =>
              current.map((workspace) =>
                workspace.id === updatedWorkspace.id
                  ? { ...workspace, ...updatedWorkspace }
                  : workspace
              )
            );
          } else if (payload.eventType === 'INSERT') {
            // If a new favorite workspace was created
            fetchWorkspaces();
          } else if (payload.eventType === 'DELETE') {
            // If a favorite workspace was deleted
            const workspaceId = payload.old.id;
            setFavoriteWorkspaces((current) =>
              current.filter((workspace) => workspace.id !== workspaceId)
            );
          }
        }
      )
      .subscribe();

    // Clean up the subscription when component unmounts
    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  // Notify parent component when favorite workspaces change
  useEffect(() => {
    if (!isLoading) {
      onFavoritesChange?.(favoriteWorkspaces.length > 0);
    }
  }, [favoriteWorkspaces, isLoading, onFavoritesChange]);

  async function fetchWorkspaces() {
    setIsLoading(true);
    try {
      const response = await fetch('/api/workspaces');
      if (!response.ok) throw new Error('Failed to fetch workspaces');
      const data = await response.json();
      setFavoriteWorkspaces(
        data.workspaces?.filter((w: Workspace) => w.is_favorite) || []
      );
    } catch (error) {
      console.error('Error fetching workspaces:', error);
    } finally {
      setIsLoading(false);
    }
  }

  const truncatedFavoriteWorkspaces = favoriteWorkspaces.slice(0, 3);

  const navigateToWorkspace = (workspaceId: string) => {
    router.push(`/workspaces/${workspaceId}`);
  };

  const navigateToWorkspacesPage = () => {
    router.push('/workspaces');
  };

  const renderWorkspaceItem = (workspace: Workspace) => {
    // Minimized view is handled by AppSidebar not rendering this component or passing isExpanded=false
    // So, this renderItem is for the expanded view (when sidebar is not 'minimized')

    const isProcessing =
      workspace.status === 'initiated' || workspace.status === 'pending';
    const hasError = workspace.status === 'error';

    return (
      <button
        key={workspace.id}
        className='w-full text-left flex items-center gap-2 py-1.5 hover:bg-sidebar-hover rounded-md text-sidebar-fg text-sm group'
        onClick={() => navigateToWorkspace(workspace.id)}
      >
        <div className='h-5 w-5 rounded-md flex items-center justify-center bg-primary/10 text-primary shrink-0'>
          {workspace.icon || workspace.name.charAt(0).toUpperCase()}
        </div>
        <span className='truncate'>{workspace.name}</span>

        {isProcessing && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Loader2 className='h-3.5 w-3.5 text-amber-500 animate-spin ml-auto' />
            </TooltipTrigger>
            <TooltipContent side='right'>
              <p className='text-xs'>Processing</p>
            </TooltipContent>
          </Tooltip>
        )}

        {hasError && (
          <Tooltip>
            <TooltipTrigger asChild>
              <AlertTriangle className='h-3.5 w-3.5 text-destructive ml-auto' />
            </TooltipTrigger>
            <TooltipContent side='right'>
              <p className='text-xs'>Error processing</p>
            </TooltipContent>
          </Tooltip>
        )}
      </button>
    );
  };

  // renderSectionHeading removed as the heading/toggle is in AppSidebar.tsx

  // Handling for minimized state (icon-only view) should be done in AppSidebar
  // by rendering a different component or specific icons if this component isn't suitable.
  // For now, if sidebarState is 'minimized', AppSidebar won't render the expanded content (children of the toggle button).
  // This component will render its content if isExpanded is true.

  if (sidebarState === 'minimized') {
    // Render compact list of favorite workspace icons for minimized sidebar
    if (isLoading) {
      return (
        <div className='flex justify-center py-2'>
          <div className='h-4 w-4 rounded-full border-2 border-sidebar-fg/30 border-t-sidebar-fg animate-spin' />
        </div>
      );
    }
    return (
      <div className='px-2 py-1 space-y-1'>
        {truncatedFavoriteWorkspaces.map((workspace) => {
          const isProcessing =
            workspace.status === 'initiated' || workspace.status === 'pending';
          const hasError = workspace.status === 'error';

          return (
            <Tooltip key={workspace.id}>
              <TooltipTrigger asChild>
                <button
                  className='w-full flex justify-center py-2 hover:bg-sidebar-hover rounded-md relative'
                  onClick={() => navigateToWorkspace(workspace.id)}
                >
                  <div className='h-6 w-6 rounded-md flex items-center justify-center bg-primary/10 text-primary'>
                    {workspace.icon || workspace.name.charAt(0).toUpperCase()}
                  </div>

                  {isProcessing && (
                    <div className='absolute top-0 right-0 h-2.5 w-2.5 bg-amber-500 rounded-full animate-pulse' />
                  )}

                  {hasError && (
                    <div className='absolute top-0 right-0 h-2.5 w-2.5 bg-destructive rounded-full' />
                  )}
                </button>
              </TooltipTrigger>
              <TooltipContent side='right'>
                <div className='flex items-center gap-1.5'>
                  <p>{workspace.name}</p>
                  {isProcessing && (
                    <Loader2 className='h-3 w-3 text-amber-500 animate-spin' />
                  )}
                  {hasError && (
                    <AlertTriangle className='h-3 w-3 text-destructive' />
                  )}
                </div>
              </TooltipContent>
            </Tooltip>
          );
        })}
      </div>
    );
  }

  // This is the content for the expanded section, controlled by isExpanded prop from AppSidebar
  if (!isExpanded) {
    return null; // Don't render anything if not expanded (and not minimized)
  }

  return (
    <motion.div // Wrapped with motion.div, ensure AnimatePresence is in AppSidebar if needed for this specific component
      initial={{ height: 0, opacity: 0 }}
      animate={{ height: 'auto', opacity: 1 }}
      exit={{ height: 0, opacity: 0 }}
      transition={{ duration: 0.2 }}
      className='overflow-hidden pl-2' // Added pl-2 for slight indent from section header
    >
      {isLoading ? (
        <div className='flex py-1 px-2'>
          <div className='h-4 w-4 rounded-full border-2 border-sidebar-fg/30 border-t-sidebar-fg animate-spin' />
        </div>
      ) : (
        <>
          {favoriteWorkspaces.length === 0 && (
            <p className='text-xs text-muted-foreground px-2 py-1'>
              No favorite workspaces.
            </p>
          )}
          {truncatedFavoriteWorkspaces.map((workspace) => (
            <motion.div
              key={workspace.id}
              initial={{ opacity: 0, y: -5 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.15 }}
            >
              {renderWorkspaceItem(workspace)}
            </motion.div>
          ))}
          {favoriteWorkspaces.length > 3 && (
            <button
              className='w-full text-left px-2 py-1 text-xs text-primary hover:underline'
              onClick={navigateToWorkspacesPage}
            >
              View all ({favoriteWorkspaces.length})
            </button>
          )}
        </>
      )}
    </motion.div>
  );
}
