import { CollapsibleSection } from './CollapsibleSection';
import { WorkspaceSection } from './WorkspaceSection';
import { StarIcon } from './FavoriteChatsSection';

type FavoriteWorkspacesSectionProps = {
  isExpanded: boolean;
  onToggleSection: () => void;
  hasFavorites: boolean;
  onFavoritesChange: (hasFavorites: boolean) => void;
};

export function FavoriteWorkspacesSection({
  isExpanded,
  onToggleSection,
  hasFavorites,
  onFavoritesChange
}: FavoriteWorkspacesSectionProps) {
  if (!hasFavorites) {
    return null;
  }

  return (
    <CollapsibleSection
      title='Favorite Workspaces'
      icon={StarIcon}
      isExpanded={isExpanded}
      onToggle={onToggleSection}
    >
      <WorkspaceSection
        isExpanded={isExpanded}
        onFavoritesChange={onFavoritesChange}
      />
    </CollapsibleSection>
  );
}