import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { SidebarState } from './AppSidebar';
import React from 'react';

type SidebarShellProps = {
  state: SidebarState;
  isPeeking: boolean;
  isAnimatingOut: boolean;
  isMobile: boolean;
  children: React.ReactNode;
  onMouseLeave: () => void;
  onAnimationComplete: () => void;
};

export function SidebarShell({
  state,
  isPeeking,
  isAnimatingOut,
  isMobile,
  children,
  onMouseLeave,
  onAnimationComplete,
}: SidebarShellProps) {
  const width =
    state === 'minimized'
      ? '64px'
      : state === 'hidden'
      ? isPeeking
        ? isMobile
          ? '85%'
          : '256px'
        : '0px'
      : '256px';

  return (
    <motion.div
      initial={false}
      animate={{ width }}
      transition={{
        type: 'spring',
        stiffness: 200,
        damping: 25,
        duration: 0.2,
      }}
      className={cn(
        'h-full',
        state === 'hidden' && (isPeeking || isAnimatingOut)
          ? 'absolute z-20'
          : 'shrink-0'
      )}
      onMouseLeave={onMouseLeave}
      onAnimationComplete={onAnimationComplete}
    >
      <motion.div
        className={cn(
          'flex flex-col h-full bg-sidebar-bg text-sidebar-fg border-r border-border relative',
          state === 'hidden' && !isPeeking ? 'overflow-hidden' : '',
          isMobile && isPeeking ? 'shadow-xl' : ''
        )}
        animate={{ width }}
        transition={{
          type: 'spring',
          stiffness: 200,
          damping: 25,
          duration: 0.2,
        }}
      >
        {children}
      </motion.div>
    </motion.div>
  );
}
