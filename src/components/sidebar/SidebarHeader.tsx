import React from 'react';
import { MessageSquare, PlusIcon, SearchIcon, RefreshCw } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { SidebarState } from './AppSidebar';
import { Button } from '../ui/button';
import {
  TooltipProvider,
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from '@/components/ui/tooltip';
import { useRouter } from 'next/navigation';
void React;

type SidebarHeaderProps = {
  sidebarState: SidebarState;
  initializeNewChat: () => void;
  onRefresh?: () => void;
  isRefreshing?: boolean;
};

export function SidebarHeader({
  sidebarState,
  initializeNewChat,
  onRefresh,
  isRefreshing = false,
}: SidebarHeaderProps) {
  const router = useRouter();
  const isMinimized = sidebarState === 'minimized';

  return (
    <>
      <div className='p-4 border-b border-border flex items-center justify-between'>
        <AnimatePresence mode='wait'>
          {isMinimized ? (
            <motion.div
              key='icon-header'
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.15 }}
              className='flex justify-center w-full'
            >
              <MessageSquare className='h-6 w-6' />
            </motion.div>
          ) : (
            <motion.h2
              key='title'
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.15 }}
              className='text-lg font-semibold whitespace-nowrap'
            >
              Sabi Chat
            </motion.h2>
          )}
        </AnimatePresence>
      </div>

      <div className='p-2'>
        <SidebarButton
          minimized={isMinimized}
          icon={<PlusIcon className='h-4 w-4' />}
          label='New Chat'
          onClick={initializeNewChat}
          tooltipContent='New Chat'
        />
      </div>

      <div className='p-2'>
        <SidebarButton
          minimized={isMinimized}
          icon={<SearchIcon className='h-4 w-4' />}
          label='Search'
          onClick={() => router.push('/chat/search')}
          tooltipContent='Search'
        />
      </div>

      {onRefresh && (
        <div className='p-2'>
          <SidebarButton
            minimized={isMinimized}
            icon={
              <RefreshCw
                className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`}
              />
            }
            label='Refresh'
            onClick={onRefresh}
            tooltipContent='Refresh conversations'
          />
        </div>
      )}
    </>
  );
}

// Reusable button component that handles minimized vs full states
type SidebarButtonProps = {
  minimized: boolean;
  icon: React.ReactNode;
  label: string;
  onClick: () => void;
  tooltipContent?: string;
};

export function SidebarButton({
  minimized,
  icon,
  label,
  onClick,
  tooltipContent,
}: SidebarButtonProps) {
  return (
    <AnimatePresence mode='wait'>
      {minimized ? (
        <motion.div
          key={`${label}-icon`}
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          transition={{ duration: 0.15 }}
          className='flex justify-center'
        >
          <TooltipProvider delayDuration={0}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant='ghost'
                  size='icon'
                  className='h-9 w-9'
                  onClick={onClick}
                >
                  {icon}
                </Button>
              </TooltipTrigger>
              {tooltipContent && (
                <TooltipContent side='right'>
                  <p>{tooltipContent}</p>
                </TooltipContent>
              )}
            </Tooltip>
          </TooltipProvider>
        </motion.div>
      ) : (
        <motion.div
          key={`${label}-full`}
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.15 }}
        >
          <Button
            variant='ghost'
            className='w-full justify-start gap-2'
            onClick={onClick}
          >
            {icon}
            {label}
          </Button>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
