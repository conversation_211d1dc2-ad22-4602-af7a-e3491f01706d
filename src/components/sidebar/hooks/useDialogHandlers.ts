import { useCallback } from 'react';
import { GroupConversation } from '@/lib/supabase/db';
import { DialogManager } from '@/components/sidebar/DialogManager';

export function useDialogHandlers(
  renameConversation: (
    conversation: GroupConversation,
    newName: string
  ) => Promise<void>,
  deleteConversation: (conversation: GroupConversation) => Promise<void>
) {
  // Adapter function for delete operation that expects a conversation but we need to extract id
  const handleDelete = useCallback(
    (conversation: GroupConversation) => {
      deleteConversation(conversation).catch(console.error);
    },
    [deleteConversation]
  );

  // Adapter function for rename that adds Promise handling
  const handleRename = useCallback(
    (conversation: GroupConversation, newName: string) => {
      renameConversation(conversation, newName).catch(console.error);
    },
    [renameConversation]
  );

  // Use the DialogManager which manages its own internal state
  const { dialogs, handlers } = DialogManager({
    onRename: handleRename,
    onDelete: handleDelete,
  });

  return {
    dialogs,
    handlers,
  };
}
