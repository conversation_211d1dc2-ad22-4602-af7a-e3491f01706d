import { useContext, useState } from 'react';
import { SidebarContext } from '@/providers/SidebarProvider';
import { AppContext } from '@/providers/AppProvider';

export function useSidebarState() {
  const { isMobile } = useContext(AppContext);
  const { sidebarState, isPeeking, setIsPeeking, setSidebarState } = useContext(SidebarContext);
  const [isAnimatingOut, setIsAnimatingOut] = useState<boolean>(false);

  // Handle sidebar close on mobile
  const handleMobileClose = () => {
    if (isMobile && isPeeking) {
      setIsPeeking(false);
      setSidebarState('hidden');
    }
  };

  // Handle mouseLeave for desktop peek mode
  const handleMouseLeave = () => {
    if (!isMobile && sidebarState === 'hidden' && isPeeking) {
      setIsPeeking(false);
      setIsAnimatingOut(true);
    }
  };

  const handleAnimationComplete = () => {
    setIsAnimatingOut(false);
  };

  return {
    sidebarState,
    isPeeking,
    isAnimatingOut,
    isMobile,
    handleMobileClose,
    handleMouseLeave,
    handleAnimationComplete
  };
}