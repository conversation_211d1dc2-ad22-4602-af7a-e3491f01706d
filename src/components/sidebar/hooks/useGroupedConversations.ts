import { useMemo } from 'react';
import { GroupConversation } from '@/lib/supabase/db';
import {
  format,
  isToday,
  isYesterday,
  differenceInDays,
  differenceInMonths,
} from 'date-fns';

// Helper function to get date group
const getDateGroup = (dateString: string | null | undefined): string => {
  if (!dateString) return 'Older';
  const date = new Date(dateString);
  const now = new Date();

  if (isToday(date)) return 'Today';
  if (isYesterday(date)) return 'Yesterday';

  const daysDiff = differenceInDays(now, date);
  if (daysDiff <= 7) return 'Previous 7 Days';
  if (daysDiff <= 30) return 'Previous 30 Days';

  const monthDiff = differenceInMonths(now, date);
  if (monthDiff === 1) {
    // Conversations from last month (like May when we're in June) should still be "Previous 30 Days"
    return 'Previous 30 Days';
  }
  if (monthDiff < 12) {
    // Show month name for recent months within the last year (but not last month)
    return format(date, 'MMMM yyyy');
  }

  // For conversations older than a year, group by year
  return format(date, 'yyyy');
};

type GroupedResult = {
  groupedConversations: Record<string, GroupConversation[]>;
  orderedGroups: string[];
};

export function useGroupedConversations(
  conversations: GroupConversation[]
): GroupedResult {
  return useMemo(() => {
    // Group conversations by date with deduplication
    const groupedConversations = conversations.reduce((acc, conversation) => {
      // Skip this conversation if it's already been processed
      // (this covers cases where the same conversation might appear
      // multiple times in the array due to pagination/infinite scroll)
      const conversationAlreadyInGroups = Object.values(acc).some((group) =>
        group.some((c) => c.id === conversation.id)
      );

      if (conversationAlreadyInGroups) {
        return acc;
      }

      // Non-favorites are grouped by date as before
      const group = getDateGroup(conversation.created_at);
      if (!acc[group]) {
        acc[group] = [];
      }
      acc[group].push(conversation);
      return acc;
    }, {} as Record<string, GroupConversation[]>);

    // Sort each date group by created_at (most recent first)
    Object.keys(groupedConversations).forEach((group) => {
      groupedConversations[group].sort((a, b) => {
        const dateA = new Date(a.created_at || 0).getTime();
        const dateB = new Date(b.created_at || 0).getTime();
        return dateB - dateA;
      });
    });

    // Define the order of groups
    const groupOrder = [
      'Today',
      'Yesterday',
      'Previous 7 Days',
      'Previous 30 Days',
    ];

    // Get month/year groups and sort them chronologically (most recent first)
    const dynamicDateGroups = Object.keys(groupedConversations)
      .filter((group) => !groupOrder.includes(group) && group !== 'Older')
      .sort((a, b) => {
        // Simple date string comparison might work for 'MMMM yyyy' or 'yyyy' formats
        // For more robust sorting, parse dates if necessary
        const dateA = new Date(a.includes(' ') ? a : `${a}-01-01`); // Handle 'MMMM yyyy' and 'yyyy'
        const dateB = new Date(b.includes(' ') ? b : `${b}-01-01`);
        return dateB.getTime() - dateA.getTime(); // Descending order
      });

    const orderedGroups = [
      ...groupOrder,
      ...dynamicDateGroups,
      ...(groupedConversations['Older'] ? ['Older'] : []), // Add 'Older' group if it exists
    ];

    return {
      groupedConversations,
      orderedGroups,
    };
  }, [conversations]);
}
