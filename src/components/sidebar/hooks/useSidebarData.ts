import { useContext, useMemo, useCallback } from 'react';
import { ChatContext } from '@/providers/ChatProvider';
import { UserContext } from '@/providers/AuthProvider';
import { useRealtimeConversations } from './useRealtimeConversations';
import { useGroupedConversations } from './useGroupedConversations';
import { useConversationActions } from '@/components/sidebar/hooks/useConversationActions';

// Type to replace the typecast in the original code
export interface StreamSubscriptionLike {
  groupConversationId?: string;
}

export function useSidebarData() {
  const { user } = useContext(UserContext);
  const {
    fetchUserConversations,
    initializeNewChat,
    loadedConversations: conversations,
    selectedConversation,
    setUserConversations,
    isConversationListLoading,
    isBackgroundRefreshing,
    hasInitiallyLoaded,
    fetchMoreConversations,
    hasMoreConversations,
    isFetchingMoreConversations,
    sseConnections,
  } = useContext(ChatContext);

  // Update conversations in realtime
  useRealtimeConversations(user?.id, setUserConversations);

  // Conversation operations
  const {
    renameConversation,
    deleteConversation,
    archiveConversation,
    toggleFavorite,
  } = useConversationActions(fetchUserConversations, (deletedId: string) => {
    if (selectedConversation?.id === deletedId) {
      initializeNewChat();
    }
  });

  // Filter and group conversations
  const favoriteConversations = useMemo(
    () => conversations.filter((convo) => convo.is_favorite),
    [conversations]
  );

  const recentConversations = useMemo(
    () => conversations.filter((convo) => !convo.is_favorite),
    [conversations]
  );

  // Get grouped conversations
  const { groupedConversations, orderedGroups } =
    useGroupedConversations(recentConversations);

  // Safely typecast sseConnections without the double cast
  const typedSseConnections = sseConnections as unknown as Map<
    string,
    StreamSubscriptionLike
  >;

  // Add a function to manually refresh data
  const refreshSidebarData = useCallback(async () => {
    await fetchUserConversations(true); // Use background refresh for manual refresh
  }, [fetchUserConversations]);

  return {
    conversations,
    favoriteConversations,
    recentConversations,
    selectedConversation,
    groupedConversations,
    orderedGroups,
    isConversationListLoading,
    isBackgroundRefreshing,
    hasInitiallyLoaded,
    hasMoreConversations,
    isFetchingMoreConversations,
    sseConnections: typedSseConnections,
    fetchMoreConversations,
    renameConversation,
    deleteConversation,
    archiveConversation,
    toggleFavorite,
    refreshSidebarData,
  };
}
