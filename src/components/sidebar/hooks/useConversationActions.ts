'use client';
import { useRouter } from 'next/navigation';
import { useAnalytics } from '@/hooks/useAnalytics';
import { toast } from 'sonner';
import { GroupConversation } from '@/lib/supabase/db';

export function useConversationActions(
  fetchUserConversations: (isBackgroundRefresh?: boolean) => void,
  onAfterDelete?: (conversationId: string) => void
) {
  const router = useRouter();
  const analytics = useAnalytics();

  const renameConversation = async (
    conversation: GroupConversation,
    newTitle: string
  ) => {
    const response = await fetch(`/api/groupConversations/${conversation.id}`, {
      method: 'PUT',
      body: JSON.stringify({ title: newTitle }),
    });

    if (response.ok) {
      toast.success('Conversation renamed successfully');
      fetchUserConversations(true); // Background refresh after action
    } else {
      toast.error('Failed to rename conversation');
    }
  };

  const deleteConversation = async (conversation: GroupConversation) => {
    const response = await fetch(`/api/groupConversations/${conversation.id}`, {
      method: 'DELETE',
    });

    if (response.ok) {
      router.push('/chat');
      toast.success('Conversation deleted successfully');
      fetchUserConversations(true); // Background refresh after action
      analytics.trackConversationDeleted(conversation.id);
      onAfterDelete?.(conversation.id);
    } else {
      toast.error('Failed to delete conversation');
    }
  };

  const archiveConversation = async (conversation: GroupConversation) => {
    const response = await fetch(
      `/api/groupConversations/archive/${conversation.id}`,
      {
        method: 'POST',
      }
    );

    if (response.ok) {
      toast.success('Conversation archived successfully');
      fetchUserConversations(true); // Background refresh after action
      analytics.trackConversationArchived(conversation.id);
    } else {
      toast.error('Failed to archive conversation');
    }
  };

  const toggleFavorite = async (conversation: GroupConversation) => {
    try {
      const response = await fetch(
        `/api/groupConversations/${conversation.id}`,
        {
          method: 'PATCH',
        }
      );

      if (!response.ok) {
        throw new Error('Failed to toggle favorite status');
      }

      analytics.trackConversationFavorited(
        conversation.id,
        !conversation.is_favorite
      );
      fetchUserConversations(true); // Background refresh after action
    } catch (error) {
      console.error('Error toggling favorite:', error);
      toast.error('Failed to toggle favorite status');
    }
  };

  const bulkArchiveConversations = async (
    conversations: GroupConversation[]
  ) => {
    const conversationIds = conversations.map((conv) => conv.id);

    try {
      const response = await fetch('/api/groupConversations/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          operation: 'archive',
          conversationIds,
        }),
      });

      const result = await response.json();

      if (response.ok && result.data?.success) {
        toast.success(
          `${result.data.processedCount} conversation${
            result.data.processedCount === 1 ? '' : 's'
          } archived successfully`
        );
        fetchUserConversations(true); // Background refresh after action

        // Track analytics for each archived conversation
        conversations.forEach((conv) => {
          analytics.trackConversationArchived(conv.id);
        });

        // Stay on chats page after successful bulk archive
      } else {
        const failedCount = result.data?.failedIds?.length || 0;
        const successCount = result.data?.processedCount || 0;

        if (successCount > 0 && failedCount > 0) {
          toast.warning(
            `${successCount} conversation${
              successCount === 1 ? '' : 's'
            } archived, ${failedCount} failed`
          );
          fetchUserConversations(true); // Background refresh after action
        } else {
          toast.error(result.error || 'Failed to archive conversations');
        }
      }
    } catch (error) {
      console.error('Error bulk archiving conversations:', error);
      toast.error('Failed to archive conversations');
    }
  };

  const bulkDeleteConversations = async (
    conversations: GroupConversation[]
  ) => {
    const conversationIds = conversations.map((conv) => conv.id);

    try {
      const response = await fetch('/api/groupConversations/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          operation: 'delete',
          conversationIds,
        }),
      });

      const result = await response.json();

      if (response.ok && result.data?.success) {
        toast.success(
          `${result.data.processedCount} conversation${
            result.data.processedCount === 1 ? '' : 's'
          } deleted successfully`
        );
        fetchUserConversations(true); // Background refresh after action

        // Track analytics for each deleted conversation
        conversations.forEach((conv) => {
          analytics.trackConversationDeleted(conv.id);
          onAfterDelete?.(conv.id);
        });

        // Stay on chats page after successful bulk delete
      } else {
        const failedCount = result.data?.failedIds?.length || 0;
        const successCount = result.data?.processedCount || 0;

        if (successCount > 0 && failedCount > 0) {
          toast.warning(
            `${successCount} conversation${
              successCount === 1 ? '' : 's'
            } deleted, ${failedCount} failed`
          );
          fetchUserConversations(true); // Background refresh after action
        } else {
          toast.error(result.error || 'Failed to delete conversations');
        }
      }
    } catch (error) {
      console.error('Error bulk deleting conversations:', error);
      toast.error('Failed to delete conversations');
    }
  };

  return {
    renameConversation,
    deleteConversation,
    archiveConversation,
    toggleFavorite,
    bulkArchiveConversations,
    bulkDeleteConversations,
  };
}
