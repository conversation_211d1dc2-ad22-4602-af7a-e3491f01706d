import { useCallback } from 'react';

export function useStreamingIndicator(
  sseConnections: Map<string, { groupConversationId?: string }> | undefined
) {
  // Check if a group conversation has any streaming conversations inside it
  const isGroupConversationStreaming = useCallback(
    (groupConversationId: string) => {
      // If sseConnections doesn't exist, return false
      if (!sseConnections || sseConnections.size === 0) {
        return false;
      }

      // Check if any conversation in this group is streaming
      return Array.from(sseConnections.entries()).some((connection) => {
        return connection[1].groupConversationId === groupConversationId;
      });
    },
    [sseConnections]
  );

  return { isGroupConversationStreaming };
}
