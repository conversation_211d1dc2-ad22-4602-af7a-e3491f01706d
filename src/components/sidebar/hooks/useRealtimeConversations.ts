import { useCallback, useEffect } from 'react';
import { GroupConversation } from '@/lib/supabase/db';
import { createClient } from '@/utils/supabase/client';
import { SetStateAction } from 'react';

type RealtimePayload = {
  new: GroupConversation | null;
  old: Partial<GroupConversation> | null;
  eventType: 'INSERT' | 'UPDATE' | 'DELETE';
};

const supabase = createClient();

export function useRealtimeConversations(
  userId: string | undefined,
  setUserConversations: (value: SetStateAction<GroupConversation[]>) => void
) {
  // Handler for realtime changes
  const handleRealtimeChange = useCallback((payload: RealtimePayload) => {
    const stateUpdater: SetStateAction<GroupConversation[]> = (
      currentConversations: GroupConversation[]
    ): GroupConversation[] => {
      switch (payload.eventType) {
        case 'INSERT': {
          const newConversation = payload.new;
          if (!newConversation?.id) return currentConversations;

          // Only prepend new conversations if they're newer than what we have
          // This ensures new conversations appear at the top without affecting pagination
          const existsInsert = currentConversations.some(
            (conv: GroupConversation) => conv.id === newConversation.id
          );

          if (!existsInsert) {
            // For inserts, we always add to the start since we're sorting by created_at desc
            return [newConversation, ...currentConversations];
          }
          return currentConversations;
        }
        case 'UPDATE': {
          const updatedConversation = payload.new;
          if (!updatedConversation?.id) return currentConversations;

          // For updates, we modify the conversation in-place wherever it appears
          return currentConversations.map((conv: GroupConversation) =>
            conv.id === updatedConversation.id
              ? { ...conv, ...updatedConversation }
              : conv
          );
        }
        case 'DELETE': {
          const oldData = payload.old;
          const deletedId = oldData?.id;

          // For deletes, we remove the conversation if it's in our loaded set
          if (deletedId) {
            return currentConversations.filter(
              (conv: GroupConversation) => conv.id !== deletedId
            );
          }
          return currentConversations;
        }
        default:
          return currentConversations;
      }
    };
    setUserConversations(stateUpdater);
  }, []);

  // Set up Supabase realtime subscription
  useEffect(() => {
    if (!userId) {
      return;
    }

    const channel = supabase
      .channel('group-conversations')
      .on<GroupConversation>(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'group_conversations',
          filter: 'is_temporary=eq.false',
        },
        (payload) => handleRealtimeChange(payload as RealtimePayload)
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [userId, handleRealtimeChange]);
}
