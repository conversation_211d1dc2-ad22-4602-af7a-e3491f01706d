import { useEffect, RefObject } from 'react';

interface InfiniteScrollOptions {
  hasMore: boolean;
  fetchMore: () => void;
  isFetching?: boolean;
  threshold?: number;
}

export function useInfiniteScroll(
  targetRef: RefObject<HTMLElement | HTMLDivElement | null>,
  options: InfiniteScrollOptions
) {
  const { hasMore, fetchMore, isFetching = false, threshold = 0.1 } = options;

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting && hasMore && !isFetching) {
          fetchMore();
        }
      },
      { threshold }
    );

    const currentTarget = targetRef.current;
    if (currentTarget) {
      observer.observe(currentTarget);
    }

    return () => {
      if (currentTarget) {
        observer.unobserve(currentTarget);
      }
    };
  }, [targetRef, hasMore, isFetching, fetchMore, threshold]);
}
