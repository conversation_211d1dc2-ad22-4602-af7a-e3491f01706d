import React, { useContext, useEffect, useState } from 'react';
import { ChatContext } from '@/providers/ChatProvider';
import { Badge } from '@/components/ui/badge';
import { Info, CheckCircle, User, Bookmark, Clock, Settings, Zap } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ModelSelectionIndicatorProps {
  className?: string;
  variant?: 'toast' | 'badge' | 'inline';
  autoHide?: boolean;
  autoHideDelay?: number;
}

const getReasonIcon = (reason: string) => {
  switch (reason) {
    case 'prompt_default':
      return <Bookmark className="w-3 h-3" />;
    case 'workspace_default':
      return <Settings className="w-3 h-3" />;
    case 'user_preference':
      return <User className="w-3 h-3" />;
    case 'last_used':
      return <Clock className="w-3 h-3" />;
    case 'app_default':
      return <Zap className="w-3 h-3" />;
    case 'first_accessible':
      return <CheckCircle className="w-3 h-3" />;
    case 'explicit_selection':
      return <User className="w-3 h-3" />;
    default:
      return <Info className="w-3 h-3" />;
  }
};

const getReasonColor = (reason: string) => {
  switch (reason) {
    case 'prompt_default':
      return 'border-blue-200 bg-blue-50 text-blue-700 dark:border-blue-800 dark:bg-blue-950 dark:text-blue-300';
    case 'workspace_default':
      return 'border-purple-200 bg-purple-50 text-purple-700 dark:border-purple-800 dark:bg-purple-950 dark:text-purple-300';
    case 'user_preference':
      return 'border-green-200 bg-green-50 text-green-700 dark:border-green-800 dark:bg-green-950 dark:text-green-300';
    case 'last_used':
      return 'border-orange-200 bg-orange-50 text-orange-700 dark:border-orange-800 dark:bg-orange-950 dark:text-orange-300';
    case 'app_default':
      return 'border-gray-200 bg-gray-50 text-gray-700 dark:border-gray-800 dark:bg-gray-950 dark:text-gray-300';
    case 'first_accessible':
      return 'border-yellow-200 bg-yellow-50 text-yellow-700 dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-300';
    case 'explicit_selection':
      return 'border-indigo-200 bg-indigo-50 text-indigo-700 dark:border-indigo-800 dark:bg-indigo-950 dark:text-indigo-300';
    default:
      return 'border-gray-200 bg-gray-50 text-gray-700 dark:border-gray-800 dark:bg-gray-950 dark:text-gray-300';
  }
};

export function ModelSelectionIndicator({ 
  className,
  variant = 'toast',
  autoHide = true,
  autoHideDelay = 4000 
}: ModelSelectionIndicatorProps) {
  const { lastModelSelection, getModelSelectionReason } = useContext(ChatContext);
  const [isVisible, setIsVisible] = useState(false);
  const [reason, setReason] = useState<string | null>(null);

  useEffect(() => {
    if (lastModelSelection) {
      // Only show indicator for meaningful switches (prompt/workspace that differs from fallback)
      const shouldShow = (lastModelSelection.reason === 'prompt_default' || lastModelSelection.reason === 'workspace_default') &&
                        lastModelSelection.modelId !== lastModelSelection.fallbackModelId;
      
      if (shouldShow) {
        const reasonText = getModelSelectionReason();
        setReason(reasonText);
        setIsVisible(true);

        if (autoHide) {
          const timer = setTimeout(() => {
            setIsVisible(false);
          }, autoHideDelay);

          return () => clearTimeout(timer);
        }
      } else {
        setIsVisible(false);
      }
    }
  }, [lastModelSelection, getModelSelectionReason, autoHide, autoHideDelay]);

  if (!isVisible || !reason || !lastModelSelection) {
    return null;
  }

  const icon = getReasonIcon(lastModelSelection.reason);
  const colorClasses = getReasonColor(lastModelSelection.reason);

  if (variant === 'badge') {
    return (
      <Badge 
        variant="outline" 
        className={cn(
          'text-xs font-normal',
          colorClasses,
          className
        )}
      >
        {icon}
        <span className="ml-1">{reason}</span>
      </Badge>
    );
  }

  if (variant === 'inline') {
    return (
      <div className={cn(
        'flex items-center gap-1 text-sm',
        colorClasses.replace('border-', 'text-').replace(' bg-', ' ').replace(' text-', ' '),
        className
      )}>
        {icon}
        <span>{reason}</span>
      </div>
    );
  }

  // Default toast variant
  return (
    <div 
      className={cn(
        'fixed top-4 right-4 z-50 max-w-sm',
        'animate-in slide-in-from-top-2 duration-300',
        isVisible ? 'opacity-100' : 'opacity-0',
        className
      )}
    >
      <div className={cn(
        'flex items-center gap-2 rounded-lg border px-3 py-2 shadow-lg backdrop-blur-sm',
        colorClasses
      )}>
        {icon}
        <div className="flex-1">
          <p className="text-sm font-medium">{reason}</p>
        </div>
        <button
          onClick={() => setIsVisible(false)}
          className="text-current opacity-50 hover:opacity-100 transition-opacity"
        >
          <span className="sr-only">Close</span>
          ×
        </button>
      </div>
    </div>
  );
}