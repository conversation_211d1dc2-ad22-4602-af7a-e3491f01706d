import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { X, ChevronDown } from 'lucide-react';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { ChatSession } from '@/providers/ChatProvider';
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandList,
} from '@/components/ui/command';
import { Model as DbModel, Provider } from '@/lib/supabase/types';
import { ProviderFilterDropdown } from '../ModelSelector/ProviderFilterDropdown';
import { ProviderModelList } from '../ModelSelector/ProviderModelList';

type ModelRowProps = {
  chatSession: ChatSession;
  index: number;
  isOpen: boolean;
  providerFilter: string;
  providers: Provider[];
  checkModelVisibility: (model: DbModel) => boolean;
  canAccessModelTier: (tier: string) => boolean;
  getModelsForProvider: (providerId: string) => DbModel[];
  getModelName: (providerId: string, modelId: string) => string;
  onProviderFilterChange: (filter: string) => void;
  onOpenChange: (isOpen: boolean) => void;
  onModelSelect: (model: DbModel, index: number) => void;
  onRemoveComparison?: (index: number) => void;
  showRemoveButton?: boolean;
};

export const ModelRow: React.FC<ModelRowProps> = ({
  chatSession,
  index,
  isOpen,
  providerFilter,
  providers,
  checkModelVisibility,
  canAccessModelTier,
  getModelsForProvider,
  onProviderFilterChange,
  onOpenChange,
  onModelSelect,
  onRemoveComparison,
  showRemoveButton = false,
}) => {
  const handleModelSelect = (model: DbModel) => {
    onModelSelect(model, index);
  };

  let visibleProviders = providers;
  if (providerFilter !== 'all') {
    visibleProviders = providers.filter((p) => p.id === providerFilter);
  }

  // Common popover content styles
  const popoverContentStyle = {
    backgroundColor: 'var(--color-input-bg)',
    color: 'var(--color-input-fg)',
    backdropFilter: 'none',
    WebkitBackdropFilter: 'none',
    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
    border: '1px solid var(--color-border)',
  };

  return (
    <div className='grid grid-cols-3 items-center gap-4 border-b pb-3 last:border-b-0 last:pb-0'>
      <Label className='col-span-3'>Model {index > 0 ? index + 1 : ''}</Label>
      <div className='col-span-3'>
        <Popover open={isOpen} onOpenChange={onOpenChange}>
          <PopoverTrigger asChild>
            <Button
              variant='outline'
              role='combobox'
              aria-expanded={isOpen}
              className='w-full justify-between'
              style={{
                backgroundColor: 'var(--color-input-bg)',
                color: 'var(--color-input-fg)',
                border: '1px solid var(--color-input-border)',
              }}
            >
              {chatSession.model?.id
                ? chatSession.model.display_name
                : 'Select model...'}
              <ChevronDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
            </Button>
          </PopoverTrigger>
          <PopoverContent
            className='w-[300px] p-0'
            style={popoverContentStyle}
            align='start'
          >
            <ProviderFilterDropdown
              providers={providers}
              currentFilter={providerFilter}
              onFilterChange={onProviderFilterChange}
              checkModelVisibility={checkModelVisibility}
            />
            <Command>
              <CommandInput placeholder='Search model...' />
              <CommandList>
                <CommandEmpty>No model found.</CommandEmpty>
                <ProviderModelList
                  providers={visibleProviders}
                  chatSession={chatSession}
                  checkModelVisibility={checkModelVisibility}
                  canAccessModelTier={canAccessModelTier}
                  onModelSelect={handleModelSelect}
                  getModelsForProvider={getModelsForProvider}
                  currentProviderFilter={providerFilter}
                />
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      </div>
      {showRemoveButton && onRemoveComparison && (
        <div className='col-span-3 flex justify-end'>
          <Button
            variant='ghost'
            size='sm'
            className='text-red-500 hover:text-red-600'
            onClick={() => onRemoveComparison(index)}
          >
            <X className='h-4 w-4 mr-1' /> Remove Comparison
          </Button>
        </div>
      )}
    </div>
  );
};
