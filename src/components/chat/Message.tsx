import { cn } from '@/lib/utils';
import {
  ChevronLeft,
  ChevronRight,
  Copy,
  RefreshCcw,
  ChevronDown,
  LockIcon,
  PencilIcon,
  CheckIcon,
  XIcon,
  Share2Icon,
  FolderPlus,
} from 'lucide-react';
import { useContext, useEffect, useMemo, useRef, useState } from 'react';
import { CustomTooltip } from '@/components/ui/tooltip';
import MarkDownView from './MarkDownView';
import { toast } from 'sonner';
import { ChatContext } from '@/providers/ChatProvider';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '../ui/badge';
import { useSubscription } from '@/providers/SubscriptionProvider';
import { Textarea } from '../ui/textarea';
import { Button } from '../ui/button';
import Image from 'next/image';
import { LLMModel, MessageNode } from '@/lib/supabase/types';
import { ShareDialog } from '../share-dialog';
import {
  ASSISTANT_MESSAGE_METADATA_VERSION,
  VersionedAssistantMessageMetadata,
  type Provider,
  type Model,
} from '@/lib/supabase/types';
import { ScrollArea } from '../ui/scroll-area';
import { NoteModal } from '../workspaces/NoteModal';
import { Json } from '@/types/database.types';
import { CollapsibleMessage } from './CollapsibleMessage';

// Helper function to convert Model to LLMModel for components that expect the raw database type
function modelToLLMModel(model: Model): LLMModel {
  return {
    id: model.id,
    name: model.name,
    display_name: model.display_name,
    created_at: model.created_at,
    updated_at: model.updated_at,
    is_active: model.is_active,
    is_visible_by_default: model.is_visible_by_default,
    allows_file_upload: model.allows_file_upload,
    allows_search: model.allows_search,
    allows_tool_usage: model.allows_tool_usage,
    max_tokens: model.max_tokens,
    openrouter_name: model.openrouter_name,
    provider_id: model.provider_id,
    priority: model.priority,
    tier: model.tier,
    config: model.config,
    context_length: model.context_length,
    description: model.description,
    last_synced: model.last_synced,
    capabilities: model.capabilities as unknown as Json, // Convert back to Json
    supported_parameters: model.supported_parameters as unknown as Json, // Convert back to Json
    architecture: model.architecture as unknown as Json, // Convert back to Json
    pricing: model.pricing as unknown as Json, // Convert back to Json
    provider_specific_data: model.provider_specific_data as unknown as Json, // Convert back to Json
  };
}

const TypingIndicator = () => (
  <div className='flex justify-start'>
    <div className='max-w-[80%] p-1 rounded-xl bg-sidebar-bg text-foreground border border-border'>
      <div className='flex space-x-1'>
        <div className='w-2 h-2 bg-gray-400 rounded-full animate-bounce'></div>
        <div className='w-2 h-2 bg-gray-400 rounded-full animate-bounce delay-100'></div>
        <div className='w-2 h-2 bg-gray-400 rounded-full animate-bounce delay-200'></div>
      </div>
    </div>
  </div>
);

interface ChatMessageProps {
  message: MessageNode;
  isProcessing: boolean;
  isLastMessage: boolean;
  onMount: (modelId: string, id: string, element: HTMLDivElement) => void;
  minHeight: number | 'initial';
  siblingsSize: number | undefined;
  currentBranchIndex: number;
  handleBranchChange: (
    messageId: string,
    direction: number,
    siblingSize: number
  ) => void;
  parentMessageId: string | undefined;
  modelId: string;
  selectedLLMModel: LLMModel;
  retryMessage: (model?: LLMModel) => void;
  editMessage: (messageId: string, content: string) => void;
  conversationId: string;
  workspaceId?: string;
  jumpToMessage: (messageId: string, behavior: ScrollBehavior) => void;
}

function ChatMessage({
  message,
  isProcessing,
  isLastMessage,
  onMount,
  minHeight,
  siblingsSize,
  currentBranchIndex,
  handleBranchChange,
  parentMessageId,
  modelId,
  selectedLLMModel,
  retryMessage,
  editMessage,
  conversationId,
  workspaceId,
  jumpToMessage,
}: ChatMessageProps) {
  const messageRef = useRef<HTMLDivElement>(null);
  const mountedRef = useRef(false);
  const { canAccessModelTier } = useSubscription();
  const { providers: allProviders } = useContext(ChatContext);

  const [isEditing, setIsEditing] = useState(false);
  const [editedContent, setEditedContent] = useState(message.content);
  const [originalDimensions, setOriginalDimensions] = useState({ height: 0 });
  const contentRef = useRef<HTMLSpanElement>(null);
  const [shareDialogOpen, setShareDialogOpen] = useState(false);

  useEffect(() => {
    setEditedContent(message.content);
  }, [message.content]);

  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard');
  };

  const modelToShow = useMemo(() => {
    if (
      message.metadata &&
      typeof message.metadata === 'object' &&
      'version' in message.metadata &&
      (message.metadata as VersionedAssistantMessageMetadata).version ===
        ASSISTANT_MESSAGE_METADATA_VERSION
    ) {
      const versionedMetadata =
        message.metadata as VersionedAssistantMessageMetadata;
      const metadataModelIdentifier = versionedMetadata.data?.model;

      if (metadataModelIdentifier) {
        let nameToDisplay = metadataModelIdentifier;

        const parts = metadataModelIdentifier.split('/');
        const providerSlug = parts[0];
        const modelSlugOnly = parts.length > 1 ? parts[1] : undefined;

        if (modelSlugOnly) {
          nameToDisplay = modelSlugOnly;
        }

        if (
          providerSlug &&
          modelSlugOnly &&
          allProviders &&
          allProviders.length > 0
        ) {
          const provider = allProviders.find(
            (p: Provider) => p.name === providerSlug
          );
          if (provider) {
            const model = provider.models?.find(
              (m: Model) => m.name === modelSlugOnly
            );
            if (model?.id === selectedLLMModel.id) {
              return null;
            }
            if (model?.display_name) {
              nameToDisplay = model.display_name;
            }
          }
        }
        return nameToDisplay;
      }
    }

    if (message.modelData?.id === selectedLLMModel.id) {
      return null;
    }
    return message.modelData?.display_name;
  }, [message.metadata, message.modelData, selectedLLMModel, allProviders]);

  useEffect(() => {
    if (messageRef.current && message.role === 'user' && !mountedRef.current) {
      onMount(modelId, message.id, messageRef.current);
      mountedRef.current = true;
    }
  }, [onMount, message.id, modelId]);

  const isAssistantControl = message.role === 'assistant' && !isProcessing;

  const handleSaveEdit = () => {
    const trimmedContent = editedContent?.trim();
    if (trimmedContent && trimmedContent !== message.content) {
      editMessage(message.id, trimmedContent);
    }
    setIsEditing(false);
  };

  const handleCancelEdit = () => {
    setEditedContent(message.content);
    setIsEditing(false);
  };

  const handleStartEdit = () => {
    if (contentRef.current) {
      setOriginalDimensions({
        height: contentRef.current.offsetHeight,
      });
    }
    setIsEditing(true);
  };

  const handleRegenerate = () => {
    const models = allProviders
      .map((provider) => provider.models)
      .flat()
      .filter((model) => model.id !== selectedLLMModel.id)
      .map((model) => ({
        ...model,
        isAccessible: canAccessModelTier(model.tier),
      }))
      .sort((a, b) => (a.priority || 0) - (b.priority || 0));

    return (
      <CustomTooltip tooltip='Regenerate response'>
        <DropdownMenu>
          <DropdownMenuTrigger>
            <div className='flex items-center text-gray-400 hover:text-gray-600 transition-colors'>
              <RefreshCcw className='h-4 w-4' />
              <ChevronDown className='h-4 w-4' />
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuLabel>Change model</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <ScrollArea className='h-[300px]'>
              {models.map((model) => (
                <DropdownMenuItem
                  key={model.id}
                  onClick={() =>
                    !model.isAccessible
                      ? null
                      : retryMessage(modelToLLMModel(model))
                  }
                  disabled={!model.isAccessible}
                >
                  <div className='flex items-center'>
                    {model.display_name}
                    {!model.isAccessible && (
                      <LockIcon className='ml-2 h-3 w-3 text-amber-500' />
                    )}
                  </div>
                </DropdownMenuItem>
              ))}
            </ScrollArea>

            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => retryMessage()}>
              Retry with {selectedLLMModel.display_name}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </CustomTooltip>
    );
  };

  return (
    <div
      ref={messageRef}
      className={cn(
        'flex flex-col group',
        message.role === 'user' ? 'items-end' : 'items-start',
        isEditing ? 'w-full' : '',
        'break-words'
      )}
      style={{
        minHeight: minHeight === 'initial' ? undefined : `${minHeight}px`,
      }}
      data-message-id={message.id}
      data-message-role={message.role}
    >
      <div
        className={cn(
          'w-full py-2 rounded-2xl flex flex-col',
          message.role === 'user'
            ? `bg-user-bubble-bg text-user-bubble-fg rounded-br-none px-4 w-auto ${
                isEditing ? 'w-full' : 'max-w-[70%]'
              }`
            : 'bg-assistant-bubble-bg text-assistant-bubble-fg rounded-bl-none'
        )}
      >
        {message.role === 'user' &&
          message.attachments &&
          message.attachments.length > 0 && (
            <div className='mb-2 flex flex-wrap gap-2'>
              {message.attachments.map((att) => (
                <div
                  key={att.url}
                  className='w-20 h-20 rounded-md overflow-hidden border border-border flex items-center justify-center bg-muted'
                >
                  {att.type.startsWith('image/') ? (
                    <Image
                      src={att.url}
                      alt={att.name}
                      width={80}
                      height={80}
                      className='object-cover w-full h-full'
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.onerror = null; // Prevent infinite error loop
                        target.src = '/placeholder-image.png'; // Use a placeholder image
                      }}
                    />
                  ) : (
                    // Placeholder for non-image files (e.g., PDF)
                    // TODO: Replace with a proper file icon component
                    <div className='text-center p-1 text-xs text-muted-foreground break-words'>
                      📄
                      <span className='block truncate'>{att.name}</span>
                      {/* You could add a download link here too */}
                      {/* <a href={att.url} target="_blank" rel="noopener noreferrer">Download</a> */}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}

        {isEditing ? (
          <div className='flex flex-col gap-2 w-full'>
            <Textarea
              value={editedContent || ''}
              onChange={(e) => setEditedContent(e.target.value)}
              className='bg-card text-foreground border-border focus-visible:ring-primary resize-none overflow-y-auto w-full'
              style={{
                minHeight: `${originalDimensions.height}px`,
              }}
              autoFocus
              onKeyDown={(e) => {
                if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
                  e.preventDefault();
                  handleSaveEdit();
                } else if (e.key === 'Escape') {
                  handleCancelEdit();
                }
              }}
            />
            <div className='flex justify-end gap-1'>
              <Button variant='ghost' size='sm' onClick={handleCancelEdit}>
                <XIcon className='h-4 w-4 mr-1' /> Cancel
              </Button>
              <Button variant='ghost' size='sm' onClick={handleSaveEdit}>
                <CheckIcon className='h-4 w-4 mr-1' /> Save (⌘+↵)
              </Button>
            </div>
          </div>
        ) : (
          <>
            {/* Assistant image attachments */}
            {message.role === 'assistant' &&
              message.attachments &&
              message.attachments.length > 0 && (
                <div className='mb-4 px-4'>
                  {message.attachments.map((att, index) => {
                    if (att.type === 'image_url' && att.image_url?.url) {
                      return (
                        <div
                          key={att.image_url.url + index}
                          className='relative group cursor-pointer'
                          onClick={() => {
                            // Open image in new tab for full-size viewing
                            if (att.image_url?.url) {
                              window.open(att.image_url.url, '_blank');
                            }
                          }}
                        >
                          <Image
                            src={att.image_url.url}
                            alt='Generated image'
                            width={400}
                            height={400}
                            className='rounded-lg border border-border max-w-full h-auto object-contain hover:opacity-90 transition-opacity'
                            style={{ maxWidth: '100%', height: 'auto' }}
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.onerror = null;
                              target.src = '/placeholder-image.png';
                            }}
                          />
                          {/* Overlay with click hint */}
                          <div className='absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-10 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100'>
                            <span className='text-white text-sm bg-black bg-opacity-50 px-2 py-1 rounded'>
                              Click to view full size
                            </span>
                          </div>
                        </div>
                      );
                    }
                    return null;
                  })}
                </div>
              )}

            {message.content.trim().length > 0 ? (
              <span
                ref={contentRef}
                className={
                  message.role === 'assistant' ? 'animate-fade-in' : ''
                }
              >
                <div className={message.role === 'assistant' ? 'px-4' : ''}>
                  <CollapsibleMessage
                    content={
                      <>
                        <MarkDownView
                          message={message}
                          annotations={message.annotations}
                        />
                        {modelToShow &&
                          message.role === 'assistant' &&
                          isLastMessage && (
                            <Badge variant='outline' className='ml-2'>
                              {modelToShow}
                            </Badge>
                          )}
                      </>
                    }
                    isLong={message.content.length > 500}
                    messageId={message.id}
                    initiallyCollapsed={
                      message.role === 'user' && message.content.length > 500
                    }
                    jumpToMessage={jumpToMessage}
                  />
                </div>
              </span>
            ) : isProcessing && message.role === 'assistant' ? (
              <div className='px-4'>
                <TypingIndicator />
              </div>
            ) : null}
          </>
        )}
      </div>

      {!isEditing && (
        <div className='flex items-center gap-2 mt-1 mb-2 opacity-0 group-hover:opacity-100 transition-opacity'>
          {siblingsSize && siblingsSize > 1 && parentMessageId ? (
            <div className='flex items-center gap-2'>
              <button
                onClick={() =>
                  handleBranchChange(parentMessageId, -1, siblingsSize)
                }
                disabled={currentBranchIndex === 0}
              >
                <ChevronLeft
                  className={cn(
                    'h-4 w-4',
                    currentBranchIndex === 0 ? 'text-gray-400' : 'text-gray-600'
                  )}
                />
              </button>
            </div>
          ) : null}
          {siblingsSize && siblingsSize > 1 && parentMessageId ? (
            <div className='flex items-center gap-2'>
              <span className='text-sm text-gray-600'>
                {currentBranchIndex + 1}/{siblingsSize}
              </span>
            </div>
          ) : null}
          {siblingsSize && siblingsSize > 1 && parentMessageId ? (
            <div className='flex items-center gap-2'>
              <button
                onClick={() =>
                  handleBranchChange(parentMessageId, 1, siblingsSize)
                }
                disabled={currentBranchIndex === siblingsSize - 1}
              >
                <ChevronRight
                  className={cn(
                    'h-4 w-4',
                    currentBranchIndex === siblingsSize - 1
                      ? 'text-gray-400'
                      : 'text-gray-600'
                  )}
                />
              </button>
            </div>
          ) : null}

          <div className='h-4 flex items-center justify-center gap-2'>
            {isLastMessage && isAssistantControl && handleRegenerate()}
            {message.content && !isProcessing && (
              <CustomTooltip tooltip='Copy to clipboard'>
                <button
                  className='text-gray-400 hover:text-gray-600 transition-colors'
                  onClick={() => handleCopy(message.content)}
                >
                  <Copy className='h-4 w-4' />
                </button>
              </CustomTooltip>
            )}
            {message.role === 'user' && message.content && !isProcessing && (
              <CustomTooltip tooltip='Edit message'>
                <button
                  className='text-gray-400 hover:text-gray-600 transition-colors'
                  onClick={handleStartEdit}
                  disabled={isProcessing}
                >
                  <PencilIcon className='h-4 w-4' />
                </button>
              </CustomTooltip>
            )}
            {isLastMessage && isAssistantControl && (
              <CustomTooltip tooltip='Share this conversation'>
                <button
                  className='text-gray-400 hover:text-gray-600 transition-colors'
                  onClick={() => setShareDialogOpen(true)}
                >
                  <Share2Icon className='h-4 w-4' />
                </button>
              </CustomTooltip>
            )}
            {workspaceId && message.role === 'user' && !isProcessing && (
              <NoteModal
                mode='add-message'
                workspaceId={workspaceId}
                messageContent={message.content}
                messageRole={message.role as 'user' | 'assistant'}
              >
                <button
                  className='text-gray-400 hover:text-gray-600 transition-colors'
                  title='Add to workspace context'
                >
                  <FolderPlus className='h-4 w-4' />
                </button>
              </NoteModal>
            )}
          </div>
        </div>
      )}

      <ShareDialog
        isOpen={shareDialogOpen}
        onClose={() => setShareDialogOpen(false)}
        messageId={message.id}
        conversationId={conversationId}
      />
    </div>
  );
}

export default ChatMessage;
