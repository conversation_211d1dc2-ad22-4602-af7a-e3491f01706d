'use client';
import { ReactNode, useEffect } from 'react';
import SplitPane from 'react-split-pane';
import './split-pane.css';

interface SplitPaneWrapperProps {
  leftPane: ReactNode;
  rightPane: ReactNode;
  defaultSize: number | string;
  onChange?: (size: number) => void;
}

// A key for storing the split position in localStorage
const SPLIT_POSITION_KEY = 'app-builder-split-position';

export default function SplitPaneWrapper({
  leftPane,
  rightPane,
  defaultSize,
  onChange,
}: SplitPaneWrapperProps) {
  // Try to get the saved position from localStorage
  useEffect(() => {
    // This runs only in the browser
    try {
      const savedSize = localStorage.getItem(SPLIT_POSITION_KEY);
      if (savedSize && onChange) {
        onChange(parseInt(savedSize, 10));
      }
    } catch {
      // Ignore localStorage errors
    }
  }, [onChange]);

  // Save the position to localStorage when it changes
  const handleChange = (size: number) => {
    if (onChange) {
      onChange(size);

      // Save to localStorage
      try {
        localStorage.setItem(SPLIT_POSITION_KEY, size.toString());
      } catch {
        // Ignore localStorage errors
      }
    }
  };

  return (
    // @ts-expect-error - React Split Pane has type issues with React 18
    <SplitPane
      split='vertical'
      minSize={300}
      maxSize={-300}
      defaultSize={defaultSize}
      onChange={handleChange}
      style={{ position: 'relative' }}
      primary='second'
      pane2Style={{ overflow: 'hidden' }}
      pane1Style={{ overflow: 'hidden' }}
    >
      {leftPane}
      {rightPane}
    </SplitPane>
  );
}
