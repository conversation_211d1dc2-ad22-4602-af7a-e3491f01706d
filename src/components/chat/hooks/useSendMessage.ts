import { useEffect, useState, useRef, useCallback } from 'react';
import { ChatSession } from '@/providers/ChatProvider';
import {
  Provider,
  MessageNode,
  AttachmentPayload,
  UrlCitationAnnotation,
  StreamMetadataClientData,
} from '@/lib/supabase/types';
import { logger } from '@/lib/logger';
import { toast } from 'sonner';
import {
  addMessageToTree,
  updateMessageInTree,
  removeMessageFromTree,
} from '../utils/addMessageToTree';
import { v4 as uuidv4 } from 'uuid';
import {
  DUMMY_ROOT_MESSAGE_ID,
  PROCESSING_MODELS_STORAGE_KEY,
} from '@/constants/chat';
import { useAnalytics } from '@/hooks/useAnalytics';
import { ChatContext } from '@/providers/ChatProvider';
import { useContext } from 'react';
import { useAttachmentUpload } from './useAttachmentUpload';
import { initSse, updateSse } from '@/lib/chatApi';
import { Model } from '@/lib/supabase/types';
import { useSubscription } from '@/providers/SubscriptionProvider';

const log = logger.child({
  module: 'useSendMessage',
});

const messageNodeOtherFields = {
  conversation_id: null,
  metadata: {},
  model_id: null,
  parent_message_id: null,
  provider_id: null,
  attachments: [],
  tokens_used: null,
  updated_at: null,
  modelData: null,
  annotations: [],
};

// Function to get initial state from sessionStorage
const getInitialProcessingModels = (): Set<string> => {
  if (typeof window === 'undefined') {
    return new Set(); // Return empty set during SSR or build time
  }
  try {
    const storedValue = sessionStorage.getItem(PROCESSING_MODELS_STORAGE_KEY);
    if (storedValue) {
      const parsedArray: string[] = JSON.parse(storedValue);
      return new Set(parsedArray);
    }
  } catch (error) {
    log.error('Error reading processingModels from sessionStorage:', error);
  }
  return new Set();
};

// Batched update types
interface BatchedUpdate {
  content?: string;
  attachments?: AttachmentPayload[];
  annotations?: UrlCitationAnnotation[];
  metadata?: Record<string, unknown>;
}

export const useSendMessage = ({
  chatSessions,
  setChatSessions,
  groupConversationId,
  isTestMode,
  getFlattenedMessages,
  providers,
  setInputMessage,
  useWebSearch,
  useImageGeneration,
  isTemporary = false,
  workspaceId,
  onMessageSent,
}: {
  chatSessions: ChatSession[];
  setChatSessions: React.Dispatch<React.SetStateAction<ChatSession[]>>;
  groupConversationId?: string;
  isTestMode: boolean;
  getFlattenedMessages: (root: MessageNode | null) => MessageNode[];
  providers: Provider[];
  setInputMessage: React.Dispatch<React.SetStateAction<string>>;
  useWebSearch: boolean;
  useImageGeneration: boolean;
  isTemporary?: boolean;
  workspaceId?: string;
  onMessageSent?: () => void;
}) => {
  // Local state to hold the current group ID (initially from prop, then updated from server)
  const [localGroupConversationId, setLocalGroupConversationId] = useState<
    string | undefined
  >(groupConversationId);
  // Use ref for processingModels to avoid unnecessary re-renders
  const processingModels = useRef<Set<string>>(getInitialProcessingModels());
  const [processingModelsArray, setProcessingModelsArray] = useState<string[]>(
    Array.from(processingModels.current)
  );

  // Batched updates for performance
  const batchedUpdates = useRef<Record<string, Record<string, BatchedUpdate>>>(
    {}
  );
  const rafId = useRef<number | null>(null);
  const lastActivityRef = useRef<Record<string, number>>({});

  const {
    attachments,
    handleUpload,
    isUploading,
    handleRemoveFile,
    clearAttachments,
  } = useAttachmentUpload();
  const analytics = useAnalytics();
  const { subscribeToStream } = useContext(ChatContext);
  const { quotaStatus, canSendMessage, canUseComparison, getPlan } =
    useSubscription();

  // Keep a ref to current chatSessions to avoid unnecessary state updates in streaming callbacks
  const chatSessionsRef = useRef(chatSessions);
  useEffect(() => {
    chatSessionsRef.current = chatSessions;
  }, [chatSessions]);

  // Keep the local copy in sync when the prop changes.
  // We intentionally omit localGroupConversationId from dependencies to prevent
  // a feedback loop where server-provided updates are immediately overwritten
  // by a stale prop value.
  useEffect(() => {
    if (groupConversationId !== localGroupConversationId) {
      setLocalGroupConversationId(groupConversationId);
    }
  }, [groupConversationId]);

  // Helper functions for processing models management
  const addProcessing = useCallback((id: string) => {
    processingModels.current.add(id);
    lastActivityRef.current[id] = Date.now();
    setProcessingModelsArray(Array.from(processingModels.current));
  }, []);

  const removeProcessing = useCallback((id: string) => {
    processingModels.current.delete(id);
    delete lastActivityRef.current[id];
    setProcessingModelsArray(Array.from(processingModels.current));
  }, []);

  // Effect to persist processingModels to sessionStorage
  useEffect(() => {
    try {
      const modelsArray = Array.from(processingModels.current);
      sessionStorage.setItem(
        PROCESSING_MODELS_STORAGE_KEY,
        JSON.stringify(modelsArray)
      );
    } catch (error) {
      log.error('Error writing processingModels to sessionStorage:', error);
    }
  }, [processingModelsArray]);

  // Cleanup for abandoned streams
  useEffect(() => {
    const interval = setInterval(() => {
      const now = Date.now();
      const timeoutThreshold = 60_000; // 60 seconds

      setChatSessions((prev) =>
        prev.map((session) => {
          const lastActivity = lastActivityRef.current[session.id];
          const isProcessing = processingModels.current.has(session.id);

          if (
            isProcessing &&
            lastActivity &&
            now - lastActivity > timeoutThreshold
          ) {
            log.warn(`Stream timeout for session ${session.id}`);
            removeProcessing(session.id);
            return { ...session, conversationState: 'error' as const };
          }
          return session;
        })
      );
    }, 30_000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, [removeProcessing, setChatSessions]);

  // Batched update flushing
  const flushUpdates = useCallback(() => {
    const updates = batchedUpdates.current;
    if (Object.keys(updates).length === 0) return;

    setChatSessions((prev) => {
      let hasChanges = false;
      const newState = prev.map((session) => {
        const sessionUpdates = updates[session.id];
        if (!sessionUpdates) return session;

        hasChanges = true;
        let updatedNode = session.parentMessageNode;

        // Apply all batched updates for this session
        Object.entries(sessionUpdates).forEach(([messageId, update]) => {
          const nodeUpdates: Partial<MessageNode> = {};

          if (update.content !== undefined) {
            nodeUpdates.content = update.content;
          }
          if (update.attachments !== undefined) {
            nodeUpdates.attachments = update.attachments;
          }
          if (update.annotations !== undefined) {
            nodeUpdates.annotations = update.annotations;
          }
          if (update.metadata !== undefined) {
            nodeUpdates.metadata = update.metadata;
          }

          if (Object.keys(nodeUpdates).length > 0) {
            updatedNode = updateMessageInTree(
              updatedNode,
              messageId,
              nodeUpdates
            );
          }
        });

        return { ...session, parentMessageNode: updatedNode };
      });

      return hasChanges ? newState : prev;
    });

    batchedUpdates.current = {};
    rafId.current = null;
  }, [setChatSessions]);

  const scheduleFlush = useCallback(() => {
    if (rafId.current === null) {
      rafId.current = requestAnimationFrame(flushUpdates);
    }
  }, [flushUpdates]);

  // Improved updateSessionState with better change detection
  const updateSessionState = useCallback(
    (
      sessionId: string,
      updateFn: (prevSession: ChatSession) => Partial<ChatSession>
    ) => {
      setChatSessions((prev) => {
        let hasChanges = false;
        const newState = prev.map((session) => {
          if (session.id !== sessionId) return session;

          const calculatedUpdate = updateFn(session);
          // If the update function returns an empty object or null, skip the update
          if (!calculatedUpdate || Object.keys(calculatedUpdate).length === 0) {
            return session;
          }

          hasChanges = true;
          return { ...session, ...calculatedUpdate };
        });

        // Only return new state if there were actual changes
        return hasChanges ? newState : prev;
      });
    },
    [setChatSessions]
  );

  // Create unified callbacks for both SSE and NDJSON streaming with batching
  const makeStreamCallbacks = useCallback(
    (sessionId: string, assistantMessageId: string) => {
      const onDelta = (
        text: string,
        replace?: boolean,
        attachments?: unknown[]
      ) => {
        // Update last activity
        lastActivityRef.current[sessionId] = Date.now();

        // Ensure batch structure exists
        const sessionBatch = (batchedUpdates.current[sessionId] ||= {});
        const messageBatch = (sessionBatch[assistantMessageId] ||= {});

        // Build content from already-batched data first, fall back to rendered tree
        const baseContent = replace
          ? ''
          : (messageBatch.content ?? // Check batched content first
            (() => {
              const currentSession = chatSessionsRef.current.find(
                (s) => s.id === sessionId
              );
              if (currentSession) {
                const currentNode = findNodeById(
                  currentSession.parentMessageNode,
                  assistantMessageId
                );
                return currentNode?.content || '';
              }
              return '';
            })());

        // Update content by building on base content
        messageBatch.content = replace ? text : baseContent + text;

        // Handle attachments if provided
        if (attachments !== undefined) {
          messageBatch.attachments = attachments as AttachmentPayload[];
        }

        scheduleFlush();
      };

      const onAnnotation = (annotation: UrlCitationAnnotation) => {
        lastActivityRef.current[sessionId] = Date.now();

        // Ensure batch structure exists
        const sessionBatch = (batchedUpdates.current[sessionId] ||= {});
        const messageBatch = (sessionBatch[assistantMessageId] ||= {});

        // Build annotations from already-batched data first, fall back to rendered tree
        const baseAnnotations =
          messageBatch.annotations ?? // Check batched annotations first
          (() => {
            const currentSession = chatSessionsRef.current.find(
              (s) => s.id === sessionId
            );
            const currentNode = currentSession
              ? findNodeById(
                  currentSession.parentMessageNode,
                  assistantMessageId
                )
              : null;
            return currentNode?.annotations || [];
          })();

        // Append new annotation to base annotations
        messageBatch.annotations = [...baseAnnotations, annotation];

        scheduleFlush();
      };

      const onMetadata = (metadata: StreamMetadataClientData) => {
        lastActivityRef.current[sessionId] = Date.now();

        // Batch the update
        if (!batchedUpdates.current[sessionId]) {
          batchedUpdates.current[sessionId] = {};
        }

        batchedUpdates.current[sessionId][assistantMessageId] = {
          ...batchedUpdates.current[sessionId][assistantMessageId],
          metadata: metadata as Record<string, unknown>,
        };

        scheduleFlush();
      };

      const onError = (error: Error) => {
        log.error('Stream error:', error);
        toast.error(`Error: ${error.message}`);
        updateSessionState(sessionId, () => ({ conversationState: 'error' }));
        removeProcessing(sessionId);
      };

      const onDone = () => {
        log.info('Stream complete');
        updateSessionState(sessionId, () => ({ conversationState: 'healthy' }));
        removeProcessing(sessionId);
      };

      return { onDelta, onAnnotation, onError, onDone, onMetadata };
    },
    [updateSessionState, scheduleFlush, removeProcessing]
  );

  const handleAddSessionResponse = useCallback(
    async (
      session: ChatSession,
      index: number,
      messageText: string,
      lastMessageId: string | null,
      passedGroupConversationId: string,
      useWebSearch: boolean,
      useImageGeneration: boolean
    ) => {
      if (!session.model) {
        removeProcessing(session.id);
        return;
      }

      // Capture workspaceId at call time to prevent race conditions
      const stableWorkspaceId = workspaceId;

      try {
        const { newAssistantMessage, newUserMessage } = addNewMessageToModels(
          session.id,
          messageText,
          lastMessageId,
          attachments
        );

        // Delegate to chatApi for SSE streaming only
        {
          const {
            conversationId: newConversationId,
            groupConversationId: newGroupId,
            assistantMessageId,
            userMessageId,
            isNewConversation,
            isTemporary: isTemp,
          } = await initSse({
            message: messageText,
            model: session.model.id,
            conversationId: session.conversationId,
            comparisonIndex: chatSessions.length > 1 ? index : null,
            isComparison: chatSessions.length > 1,
            groupConversationId: passedGroupConversationId,
            isTestMode,
            parentMessageId: lastMessageId ?? undefined,
            attachments,
            useWebSearch,
            useImageGeneration,
            isTemporary,
            workspaceId: stableWorkspaceId,
          });

          // Update our local state so future messages reuse this group ID, if provided
          if (newGroupId) {
            setLocalGroupConversationId(newGroupId);
          }

          onMessageSent?.();

          // Sync IDs with server-assigned ones
          if (userMessageId !== newUserMessage.id) {
            updateSessionState(session.id, (prevSession) => ({
              parentMessageNode: updateMessageInTree(
                prevSession.parentMessageNode,
                newUserMessage.id,
                { id: userMessageId }
              ),
            }));
          }
          if (assistantMessageId !== newAssistantMessage.id) {
            updateSessionState(session.id, (prevSession) => ({
              parentMessageNode: updateMessageInTree(
                prevSession.parentMessageNode,
                newAssistantMessage.id,
                { id: assistantMessageId }
              ),
            }));
          }

          // Handle new conversation navigation
          if (isNewConversation && newGroupId) {
            updateSessionState(session.id, () => ({
              conversationId: newConversationId,
            }));
            if (!isTemp && !stableWorkspaceId) {
              window.history.replaceState({}, '', `/chat/${newGroupId}`);
            }
          }

          // Subscribe to SSE with unified callbacks
          const { onDelta, onAnnotation, onError, onDone, onMetadata } =
            makeStreamCallbacks(session.id, assistantMessageId);
          subscribeToStream(
            assistantMessageId,
            newConversationId!,
            session.model.id,
            isTestMode,
            useWebSearch,
            useImageGeneration,
            onDelta,
            onAnnotation,
            onError,
            onDone,
            onMetadata,
            stableWorkspaceId
          );
        }
      } catch (error) {
        log.error({ error }, 'Error sending message');
        toast.error('Sorry, there was an error processing your request.');
        setChatSessions((prev) => {
          const updated = [...prev];
          updated[index].conversationState = 'error';
          return updated;
        });

        setChatSessions((prev) => {
          const updated = [...prev];
          const currentSession =
            updated.find((s) => s.id === session.id) ?? updated[0];

          const currentBranch = getFlattenedMessages(
            currentSession.parentMessageNode
          );
          const lastMessage = currentBranch[currentBranch.length - 1];

          if (lastMessage?.role === 'assistant' && lastMessage.content === '') {
            const newTree = removeMessageFromTree(
              currentSession.parentMessageNode,
              lastMessage.id
            );
            const index = updated.findIndex((s) => s.id === currentSession.id);
            updated[index] = { ...currentSession, parentMessageNode: newTree };
          }

          return updated;
        });
      } finally {
        clearAttachments();
        // Track message sent event
        analytics.trackMessageSent(
          passedGroupConversationId,
          uuidv4(), // Generate a message ID since we don't have it yet
          chatSessions[0]?.model.display_name || 'unknown',
          messageText.length
        );
      }
    },
    [
      chatSessions,
      setChatSessions,
      getFlattenedMessages,
      attachments,
      useWebSearch,
      useImageGeneration,
      isTemporary,
      analytics,
      subscribeToStream,
      updateSessionState,
      workspaceId,
      makeStreamCallbacks,
      removeProcessing,
      onMessageSent,
      clearAttachments,
      setLocalGroupConversationId,
    ]
  );

  const handleUpdateSessionResponse = useCallback(
    async (
      session: ChatSession,
      lastUserMessageId: string,
      newContent?: string,
      useWebSearch?: boolean,
      useImageGeneration?: boolean,
      modelToRetry?: Model
    ) => {
      if (!session.model) {
        removeProcessing(session.id);
        return;
      }

      try {
        const assistantMessageId = uuidv4();
        const newAssistantMessage = {
          id: assistantMessageId,
          content: '',
          role: 'assistant',
          created_at: new Date().toISOString(),
          children: [],
          ...messageNodeOtherFields,
        };

        updateSessionState(session.id, (prevSession) => ({
          parentMessageNode: updateMessageInTree(
            prevSession.parentMessageNode,
            lastUserMessageId,
            {
              children: [newAssistantMessage],
            }
          ),
        }));

        const finalModelToRetry = modelToRetry ?? session.model;

        // Delegate update to chatApi for SSE streaming only
        {
          const {
            conversationId,
            assistantMessageId: updatedAssistantMessageId,
          } = await updateSse({
            messageId: lastUserMessageId,
            model: finalModelToRetry.id,
            isTestMode,
            newContent,
            isEdit: !!newContent,
            useWebSearch: useWebSearch ?? false,
            useImageGeneration: useImageGeneration ?? false,
          });

          if (updatedAssistantMessageId !== assistantMessageId) {
            updateSessionState(session.id, (prevSession) => ({
              parentMessageNode: updateMessageInTree(
                prevSession.parentMessageNode,
                assistantMessageId,
                { id: updatedAssistantMessageId }
              ),
            }));
          }

          // Subscribe to SSE with unified callbacks
          const { onDelta, onAnnotation, onError, onDone, onMetadata } =
            makeStreamCallbacks(session.id, updatedAssistantMessageId);
          subscribeToStream(
            updatedAssistantMessageId,
            conversationId!,
            finalModelToRetry.id,
            isTestMode,
            useWebSearch ?? false,
            useImageGeneration ?? false,
            onDelta,
            onAnnotation,
            onError,
            onDone,
            onMetadata
          );
        }
      } catch (error) {
        log.error({ error }, 'Error sending message');
        toast.error('Sorry, there was an error processing your request.');
        setChatSessions((prev) => {
          const updated = [...prev];
          const session =
            updated.find((s) => s.id === modelToRetry?.id) ?? updated[0];
          session.conversationState = 'error';
          return updated;
        });

        setChatSessions((prev) => {
          const updated = [...prev];
          const currentSession =
            updated.find((s) => s.id === session.id) ?? updated[0];

          const currentBranch = getFlattenedMessages(
            currentSession.parentMessageNode
          );
          const lastMessage = currentBranch[currentBranch.length - 1];

          if (lastMessage?.role === 'assistant' && lastMessage.content === '') {
            const newTree = removeMessageFromTree(
              currentSession.parentMessageNode,
              lastMessage.id
            );
            const index = updated.findIndex((s) => s.id === currentSession.id);
            updated[index] = { ...currentSession, parentMessageNode: newTree };
          }

          return updated;
        });
      } finally {
        clearAttachments();
        // Track message sent event
        analytics.trackMessageRetried(
          lastUserMessageId,
          session.model.display_name
        );
      }
    },
    [
      chatSessions,
      groupConversationId,
      setChatSessions,
      getFlattenedMessages,
      providers,
      useWebSearch,
      subscribeToStream,
      updateSessionState,
      makeStreamCallbacks,
      removeProcessing,
      clearAttachments,
      analytics,
    ]
  );

  const addNewMessageToModels = useCallback(
    (
      sessionId: string,
      messageText: string,
      lastMessageId: string | null,
      attachments?: AttachmentPayload[]
    ): { newAssistantMessage: MessageNode; newUserMessage: MessageNode } => {
      const newAssistantMessage: MessageNode = {
        id: uuidv4(),
        content: '',
        role: 'assistant',
        created_at: new Date().toISOString(),
        children: [],
        ...messageNodeOtherFields,
      };

      let newMessage: MessageNode;

      const newUserMessage: MessageNode = {
        id: uuidv4(),
        content: messageText,
        role: 'user',
        created_at: new Date().toISOString(),
        children: [newAssistantMessage],
        ...messageNodeOtherFields,
        attachments: attachments || [],
      };

      // if lastMessageId is undefined, create a new dummy root message and use the newUserMessage as the first child
      if (!lastMessageId) {
        newMessage = {
          id: DUMMY_ROOT_MESSAGE_ID,
          content: '',
          role: 'system',
          created_at: new Date().toISOString(),
          children: [newUserMessage],
          ...messageNodeOtherFields,
          attachments: [],
        };
      } else {
        newMessage = newUserMessage;
      }

      setChatSessions((prev) =>
        prev.map((s) => {
          if (s.id !== sessionId) return s;

          const withUserMessage = addMessageToTree(
            s.parentMessageNode,
            lastMessageId ?? undefined,
            newMessage
          );

          return {
            ...s,
            parentMessageNode: withUserMessage,
          };
        })
      );

      return { newAssistantMessage, newUserMessage };
    },
    [setChatSessions, attachments]
  );

  const postProcessSuccess = async (session: ChatSession) => {
    updateSessionState(session.id, () => ({
      conversationState: 'healthy',
    }));
  };

  // Handle sending a message
  const handleSendMessage = useCallback(
    async (inputMessage: string) => {
      const trimmedMessage = inputMessage.trim();
      if (
        (!trimmedMessage && (!attachments || attachments.length === 0)) ||
        chatSessions.length === 0
      )
        return;

      // Check basic message sending permission (free tier daily limits)
      if (!canSendMessage) {
        toast.error(
          'You have reached your daily message limit. Please upgrade your plan or try again tomorrow.'
        );
        return;
      }

      // Check token quota for paid tiers
      const currentPlan = getPlan();
      if (currentPlan !== 'free' && quotaStatus?.tokens) {
        const { remaining, total } = quotaStatus.tokens;
        const usagePercent =
          total > 0 ? ((total - remaining) / total) * 100 : 0;

        if (remaining <= 0) {
          if (currentPlan === 'starter') {
            toast.error(
              'Token quota exceeded. Please use GPT-3.5 or upgrade to Premium for more tokens.'
            );
            return;
          } else if (currentPlan === 'premium') {
            toast.error(
              'Token quota exceeded. You can purchase additional tokens in billing settings.'
            );
            return;
          }
        } else if (usagePercent >= 90) {
          toast.warning(
            `Token quota warning: ${remaining.toLocaleString()} tokens remaining (${Math.round(
              100 - usagePercent
            )}%)`
          );
        }
      }

      // Check comparison mode restrictions
      const isComparisonMessage = chatSessions.length > 1;
      if (isComparisonMessage && !canUseComparison) {
        if (currentPlan !== 'premium') {
          toast.error(
            'Side-by-side comparison requires Premium plan. Please upgrade to continue.'
          );
          return;
        } else {
          toast.error(
            'Daily comparison limit reached (50/day). Try again tomorrow.'
          );
          return;
        }
      }

      setInputMessage('');

      const lastMessages: MessageNode[] = chatSessions.map(
        (session) =>
          getFlattenedMessages(session.parentMessageNode).at(-1) as MessageNode
      );

      // Add all sessions to processing
      chatSessions.forEach((session) => addProcessing(session.id));

      // Determine if this is a new conversation by checking if any session has messages
      const isNewConversation = chatSessions.every((session) => {
        const messages = getFlattenedMessages(session.parentMessageNode);
        return messages.length === 0;
      });

      // Generate a new group ID for new conversations, otherwise use existing one
      const currentGroupId = isNewConversation
        ? uuidv4()
        : (localGroupConversationId ?? uuidv4());

      const sessionPromises = chatSessions.map((session, index) =>
        handleAddSessionResponse(
          session,
          index,
          trimmedMessage,
          lastMessages[index]?.id ?? null,
          currentGroupId,
          useWebSearch,
          useImageGeneration
        )
      );

      await Promise.all(sessionPromises);
    },
    [
      localGroupConversationId,
      chatSessions,
      handleAddSessionResponse,
      getFlattenedMessages,
      attachments,
      useWebSearch,
      useImageGeneration,
      setInputMessage,
      canSendMessage,
      quotaStatus,
      canUseComparison,
      getPlan,
      addProcessing,
    ]
  );

  const handleRetryMessage = useCallback(
    async (sessionId: string, modelToRetry?: Model) => {
      // Avoid unnecessary re-render by checking if already processing
      const activeSession =
        chatSessions.find((session) => session.id === sessionId) ??
        chatSessions[0];
      if (activeSession) {
        addProcessing(activeSession.id);
      }

      const flattenedMessages = getFlattenedMessages(
        activeSession?.parentMessageNode
      );
      const lastUserMessage = flattenedMessages
        .slice()
        .reverse()
        .find((message) => message.role === 'user');
      if (!lastUserMessage) return;

      if (modelToRetry && modelToRetry.id !== activeSession.model.id) {
        updateSessionState(activeSession.id, () => ({
          model: modelToRetry,
        }));
      }

      // Process only the selected session instead of all sessions
      await handleUpdateSessionResponse(
        activeSession,
        lastUserMessage.id,
        undefined,
        useWebSearch,
        useImageGeneration,
        modelToRetry
      );
    },
    [
      chatSessions,
      handleUpdateSessionResponse,
      getFlattenedMessages,
      providers,
      updateSessionState,
      useWebSearch,
      useImageGeneration,
      addProcessing,
    ]
  );

  const editMessage = useCallback(
    async (sessionId: string, messageId: string, newContent: string) => {
      // Assuming edits happen on the primary model/branch for now
      const targetSession = chatSessions.find(
        (session) => session.id === sessionId
      );
      if (!targetSession) return;

      log.info({ messageId, sessionId: targetSession.id }, 'Editing message');
      addProcessing(targetSession.id);

      const flattenedMessages = getFlattenedMessages(
        targetSession?.parentMessageNode
      );

      const selectedMessageIndex = flattenedMessages.findIndex(
        (message) => message.id === messageId
      );

      if (selectedMessageIndex === -1) return;

      const selectedMessageParentId =
        flattenedMessages[selectedMessageIndex - 1]?.id;

      const newAssistantMessageId = uuidv4();
      const placeholderAssistantMessage: MessageNode = {
        id: newAssistantMessageId,
        content: '',
        role: 'assistant',
        created_at: new Date().toISOString(),
        children: [],
        ...messageNodeOtherFields,
      };

      const newUserMessage: MessageNode = {
        id: uuidv4(),
        content: newContent,
        role: 'user',
        created_at: new Date().toISOString(),
        children: [placeholderAssistantMessage],
        ...messageNodeOtherFields,
      };

      // 1. Update user message content and replace its children locally
      updateSessionState(targetSession.id, (prevSession) => ({
        parentMessageNode: updateMessageInTree(
          prevSession.parentMessageNode,
          selectedMessageParentId,
          {
            children: [newUserMessage], // Replace children with placeholder
          }
        ),
      }));

      try {
        // Use SSE for edits when enabled
        const {
          conversationId,
          assistantMessageId: updatedAssistantMessageId,
        } = await updateSse({
          messageId: messageId,
          model: targetSession.model.id,
          isTestMode,
          newContent,
          isEdit: true,
          useWebSearch,
          useImageGeneration,
        });
        // Sync ID if server-assigned changed
        if (updatedAssistantMessageId !== newAssistantMessageId) {
          updateSessionState(targetSession.id, (prevSession) => ({
            parentMessageNode: updateMessageInTree(
              prevSession.parentMessageNode,
              newAssistantMessageId,
              { id: updatedAssistantMessageId }
            ),
          }));
        }
        // Subscribe to SSE stream for edit
        const { onDelta, onAnnotation, onError, onDone, onMetadata } =
          makeStreamCallbacks(targetSession.id, updatedAssistantMessageId);
        subscribeToStream(
          updatedAssistantMessageId,
          conversationId!,
          targetSession.model.id,
          isTestMode,
          useWebSearch,
          useImageGeneration,
          onDelta,
          onAnnotation,
          onError,
          onDone,
          onMetadata
        );
      } catch (error) {
        log.error({ error }, 'Error editing message');
        toast.error('Sorry, there was an error editing your message.');
        // TODO: Revert local changes or show error state
        updateSessionState(targetSession.id, () => ({
          conversationState: 'error',
        }));
      } finally {
        clearAttachments();
        // Mark this session as no longer processing
        removeProcessing(targetSession.id);
      }
    },
    [
      chatSessions,
      setChatSessions,
      updateSessionState,
      postProcessSuccess,
      isTestMode,
      useWebSearch,
      useImageGeneration,
      getFlattenedMessages,
      subscribeToStream,
      makeStreamCallbacks,
      addProcessing,
      removeProcessing,
      clearAttachments,
    ]
  );

  // Cleanup RAF on unmount
  useEffect(() => {
    return () => {
      if (rafId.current !== null) {
        cancelAnimationFrame(rafId.current);
      }
    };
  }, []);

  return {
    handleSendMessage,
    processingModels: processingModelsArray,
    handleRetryMessage,
    editMessage,
    handleUpload,
    isUploading,
    handleRemoveFile,
  };
};

// Helper function to find a node in the tree (implement or import)
function findNodeById(
  node: MessageNode | null,
  id: string
): MessageNode | null {
  if (!node) return null;
  if (node.id === id) return node;
  if (node.children) {
    for (const child of node.children) {
      const found = findNodeById(child, id);
      if (found) return found;
    }
  }
  return null;
}
