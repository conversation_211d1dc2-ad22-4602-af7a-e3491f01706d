import { ChatSession } from '@/providers/ChatProvider';
import { debounce } from 'lodash';
import { useCallback, useEffect, useState, RefObject } from 'react';

export const useMessageNav = (
  chatContainerRefs: RefObject<Record<string, HTMLDivElement | null>>,
  chatSessions: ChatSession[],
  isChatLoading: boolean
) => {
  const [navigationState, setNavigationState] = useState<
    Record<string, { atFirst: boolean; atLast: boolean }>
  >({});

  // Debounced navigation state update
  const debouncedUpdateNavigationState = useCallback(
    debounce((sessionId: string) => {
      const container = chatContainerRefs.current[sessionId];
      if (!container) return;

      const messageElements = Array.from(
        container.querySelectorAll('[data-message-id]')
      );
      if (messageElements.length === 0) return;

      // Get the container's viewport bounds once
      const containerRect = container.getBoundingClientRect();
      const containerTop = containerRect.top;
      const containerBottom = containerRect.bottom;

      // Calculate visibilities in a single pass
      let firstMessageVisible = false;
      let lastMessageVisible = false;

      // Only check the first and last messages
      if (messageElements.length > 0) {
        const firstMessageRect = messageElements[0].getBoundingClientRect();
        firstMessageVisible =
          firstMessageRect.top >= containerTop &&
          firstMessageRect.top < containerBottom;

        const lastMessageRect =
          messageElements[messageElements.length - 1].getBoundingClientRect();
        lastMessageVisible =
          lastMessageRect.bottom <= containerBottom &&
          lastMessageRect.bottom > containerTop;
      }

      setNavigationState((prev) => ({
        ...prev,
        [sessionId]: { atFirst: firstMessageVisible, atLast: lastMessageVisible },
      }));
    }, 100),
    []
  );

  // Function to check and update navigation button states - simplified version
  const updateNavigationState = useCallback(
    (sessionId: string) => {
      debouncedUpdateNavigationState(sessionId);
    },
    [debouncedUpdateNavigationState]
  );

  // Scroll Navigation Logic
  const handleScrollNavigation = useCallback(
    (sessionId: string, direction: 'up' | 'down') => {
      const container = chatContainerRefs.current[sessionId];
      if (!container) return;

      // Get all message elements in the container - do this only once
      const messageElements = Array.from(
        container.querySelectorAll('[data-message-id]')
      );
      if (messageElements.length === 0) return;

      // Get the container's viewport bounds - do this only once
      const containerRect = container.getBoundingClientRect();
      const containerTop = containerRect.top;
      const containerBottom = containerRect.bottom;

      // Collect all message positions in a single pass to avoid multiple reflows
      const messagePositions = messageElements.map((element, index) => {
        const rect = element.getBoundingClientRect();
        return {
          index,
          element,
          top: rect.top,
          bottom: rect.bottom,
          visible: rect.top >= containerTop && rect.top < containerBottom,
          distance: Math.abs(rect.top - containerTop),
        };
      });

      // Find the current visible message
      let currentVisibleMessage = messagePositions.find((msg) => msg.visible);

      // If no message is fully visible, find the closest one
      if (!currentVisibleMessage) {
        currentVisibleMessage = messagePositions.reduce(
          (closest, current) =>
            current.distance < closest.distance ? current : closest,
          messagePositions[0]
        );
      }

      // Calculate target index
      const currentIndex = currentVisibleMessage.index;
      let targetIndex;

      if (direction === 'up') {
        targetIndex = Math.max(0, currentIndex - 1);
      } else {
        targetIndex = Math.min(messageElements.length - 1, currentIndex + 1);
      }

      // Don't scroll if we're already at the edge
      if (targetIndex === currentIndex) return;

      // Scroll to the target message
      const targetElement = messageElements[targetIndex] as HTMLElement;
      targetElement.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });

      // Update navigation state after scrolling
      setTimeout(() => updateNavigationState(sessionId), 500);
    },
    [updateNavigationState]
  );

  // Effect to initialize navigation state and add scroll listeners
  useEffect(() => {
    if (!isChatLoading) {
      const cleanupFunctions: (() => void)[] = [];

      chatSessions.forEach((session) => {
        const container = chatContainerRefs.current[session.id];
        if (container) {
          // Initialize navigation state
          updateNavigationState(session.id);

          // Use debounced scroll handler
          const handleScroll = () => updateNavigationState(session.id);
          container.addEventListener('scroll', handleScroll, { passive: true });

          cleanupFunctions.push(() => {
            container.removeEventListener('scroll', handleScroll);
          });
        }
      });

      return () => {
        cleanupFunctions.forEach((cleanup) => cleanup());
      };
    }
  }, [chatSessions, isChatLoading, updateNavigationState]);

  return { navigationState, handleScrollNavigation };
};
