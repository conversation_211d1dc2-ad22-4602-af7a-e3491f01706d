import { useState, useRef } from 'react';
import { toast } from 'sonner';
import { logger } from '@/lib/logger';
import {
  ALLOWED_FILE_TYPES,
  MAX_FILE_SIZE_MB,
  MAX_FILE_SIZE_BYTES,
} from '@/constants/attachment';

const log = logger.child({ hook: 'useFileAttach' });

// Define interface for file previews (can be kept here or moved to types file)
export interface FilePreview {
  name: string;
  type: string;
  size: number;
  previewUrl: string | null; // Base64 for images, null for others
  isImage: boolean; // Flag to differentiate
  file: File; // Keep the original file object
}

interface UseFileUploadOptions {
  maxFiles?: number;
  allowedTypes?: string[];
  maxSizeBytes?: number;
  maxSizeMb?: number;
  onFilesAdded?: (files: FilePreview[]) => void; // Callback when files are successfully processed
  onFileRemoved?: (fileName: string) => void; // Callback when a file is removed
  canUpload?: boolean;
}

export function useFileAttach({
  maxFiles = 5,
  onFilesAdded,
  onFileRemoved,
  canUpload,
}: UseFileUploadOptions = {}) {
  const [selectedFiles, setSelectedFiles] = useState<FilePreview[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const processAndAddFiles = (filesToAdd: File[]) => {
    // Check if uploads are allowed
    if (!canUpload) {
      toast.error('File uploads are not supported by the current model(s)');
      return;
    }

    const currentFilesCount = selectedFiles.length;
    const availableSlots = maxFiles - currentFilesCount;

    if (availableSlots <= 0) {
      toast.info('Maximum number of files reached.');
      return;
    }

    const filesToProcess = Array.from(filesToAdd).slice(0, availableSlots);

    const processedPreviews: FilePreview[] = [];
    const filesAdded: FilePreview[] = []; // Track files actually added in this call

    filesToProcess.forEach((file) => {
      // --- Validation ---
      if (!ALLOWED_FILE_TYPES.includes(file.type)) {
        toast.error(`File type ${file.type} is not allowed.`);
        log.warn(`Skipping disallowed file type: ${file.name} (${file.type})`);
        return; // Skip
      }
      if (file.size > MAX_FILE_SIZE_BYTES) {
        toast.error(
          `File ${file.name} is too large (max ${MAX_FILE_SIZE_MB}MB).`
        );
        log.warn(
          { type: file.type, size: file.size },
          'Skipping disallowed file'
        );
        return; // Skip
      }
      // --- Check if already selected ---
      if (
        selectedFiles.some((f) => f.name === file.name && f.size === file.size)
      ) {
        log.warn(`Skipping already selected file: ${file.name}`);
        // Optionally: toast.info(`File ${file.name} is already selected.`);
        return; // Skip
      }

      const isImage = file.type.startsWith('image/');
      const newFilePreview: FilePreview = {
        name: file.name,
        type: file.type,
        size: file.size,
        previewUrl: null,
        isImage: isImage,
        file,
      };

      processedPreviews.push(newFilePreview); // Add valid file preview

      if (isImage) {
        const reader = new FileReader();
        reader.onloadend = () => {
          // Update the specific preview in state once loaded
          setSelectedFiles((prevFiles) =>
            prevFiles.map((p) =>
              p.name === newFilePreview.name && p.size === newFilePreview.size
                ? { ...p, previewUrl: reader.result as string }
                : p
            )
          );
        };
        reader.readAsDataURL(file);
      }
    });

    // Add all *synchronously* processed previews to state at once
    if (processedPreviews.length > 0) {
      setSelectedFiles((prevFiles) => [...prevFiles, ...processedPreviews]);
      filesAdded.push(...processedPreviews); // Track successfully initiated additions
    }

    // Call handleUpload with the files that were *attempted* to be added (passed validation)
    if (filesAdded.length > 0) {
      onFilesAdded?.(filesAdded);
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    // Check if uploads are allowed
    if (!canUpload) {
      toast.error('File uploads are not supported by the current model(s)');
      return;
    }

    processAndAddFiles(Array.from(files));

    // Reset file input
    if (event.target) {
      event.target.value = '';
    }
  };

  const handlePaste = (event: React.ClipboardEvent<HTMLTextAreaElement>) => {
    const files = event.clipboardData.files;
    if (files && files.length > 0) {
      if (!canUpload) {
        toast.error('File uploads are not supported by the current model(s)');
        return;
      }
      event.preventDefault(); // Prevent default paste behavior (like inserting file path)
      log.info({ count: files.length }, 'Processing pasted files');
      processAndAddFiles(Array.from(files));
    }
    // If no files, or cannot upload, allow default paste behavior (text pasting)
  };

  const onRemoveFile = (fileNameToRemove: string) => {
    setSelectedFiles((prevFiles) =>
      prevFiles.filter((file) => file.name !== fileNameToRemove)
    );
    onFileRemoved?.(fileNameToRemove);
  };

  return {
    selectedFiles,
    setSelectedFiles,
    fileInputRef,
    handleFileChange,
    handlePaste,
    onRemoveFile,
  };
}
