import { useContext, useCallback, useEffect, useState } from 'react';
import { ChatContext } from '@/providers/ChatProvider';
import { toast } from 'sonner';
import { UserContext } from '@/providers/AuthProvider';
import {
    Model as DbModel,
    Model,
    ModelCapabilities,
} from '@/lib/supabase/types';
export function useChatModels() {
  const { providers, chatSessions, setChatSessions } = useContext(ChatContext);
  const { me } = useContext(UserContext);

  const [canSearch, setCanSearch] = useState(false);
  const [canUpload, setCanUpload] = useState(false);
  const [useWebSearch, setUseWebSearch] = useState(false);
  const [canGenerateImages, setCanGenerateImages] = useState(false);
  const [useImageGeneration, setUseImageGeneration] = useState(false);

  // Helper to get available models for a provider
  const getModelsForProvider = useCallback(
    (providerId: string) => {
      const provider = providers.find((p) => p.id === providerId);
      return provider?.models || [];
    },
    [providers]
  );

  // Update model selection
  const handleModelChange = useCallback(
    (model: DbModel, index: number) => {
      setChatSessions((prev) => {
        const updated = [...prev];
        updated[index] = {
          ...updated[index],
          model,
        };
        return updated;
      });
      if (!me?.preferences?.default_model_id) {
        localStorage.setItem('last_used_model_id', model.id);
      }
    },
    [setChatSessions, me]
  );

  // Remove a model from comparison
  const removeComparisonModel = useCallback(
    async (index: number) => {
      const conversationId = chatSessions[index]?.conversationId;
      setChatSessions((prev) => prev.filter((_, i) => i !== index));

      // archive the conversation if it exists
      if (conversationId) {
        const response = await fetch(
          `/api/conversations/archive/${conversationId}`,
          {
            method: 'POST',
          }
        );
        if (!response.ok) {
          toast.error('Failed to remove comparison model');
        }
      }
    },
    [setChatSessions, chatSessions]
  );

  /**
   * Helper function to check if a model supports a specific capability
   */
  const modelSupportsCapability = useCallback(
    (model: Model | null | undefined, capability: string): boolean => {
      if (!model) return false;

      // Use new capabilities structure if available
      if (model.capabilities && typeof model.capabilities === 'object') {
        const caps = model.capabilities as ModelCapabilities;
        if (caps[capability as keyof ModelCapabilities] !== undefined) {
          return !!caps[capability as keyof ModelCapabilities];
        }
      }

      // Fallback to legacy fields
      switch (capability) {
        case 'file_upload':
          return model.allows_file_upload ?? false;
        case 'web_search':
          // return model.allows_search ?? false;
          return true;
        case 'function_calling':
          return model.allows_tool_usage ?? false;
        case 'image_generation':
          return model.allows_tool_usage ?? false; // Use tool usage as fallback for legacy models
        case 'structured_output':
          return false; // Default to false for legacy models
        default:
          return false;
      }
    },
    []
  );

  const checkCanSearchUpload = useCallback(() => {
    if (chatSessions.length === 0) {
      setCanSearch(false);
      setCanUpload(false);
      setCanGenerateImages(false);
      return;
    }
    const modelList = providers.flatMap((provider) => provider.models);
    const chatSessionModelDetails = chatSessions
      .map((cs) => modelList.find((m) => m.id === cs.model?.id))
      .filter((m): m is NonNullable<typeof m> => m != null); // Find details for each active model and filter out nulls

    // Check if *all* active models found have the capability
    const allCanSearch =
      chatSessionModelDetails.length === chatSessions.length && // Ensure all active models were found
      chatSessionModelDetails.every((model) =>
        modelSupportsCapability(model, 'web_search')
      );
    const allCanUpload =
      chatSessionModelDetails.length === chatSessions.length && // Ensure all active models were found
      chatSessionModelDetails.every((model) =>
        modelSupportsCapability(model, 'file_upload')
      );
    const allCanGenerateImages =
      chatSessionModelDetails.length === chatSessions.length && // Ensure all active models were found
      chatSessionModelDetails.every((model) =>
        modelSupportsCapability(model, 'image_generation')
      );

    setCanSearch(allCanSearch);
    setCanUpload(allCanUpload);
    setCanGenerateImages(allCanGenerateImages);

    if (!allCanSearch && useWebSearch) {
      setUseWebSearch(false);
    }
    if (!allCanGenerateImages && useImageGeneration) {
      setUseImageGeneration(false);
    }
  }, [
    chatSessions,
    providers,
    useWebSearch,
    useImageGeneration,
    modelSupportsCapability,
  ]);

  useEffect(() => {
    checkCanSearchUpload();
  }, [checkCanSearchUpload]);

  /**
   * Check if all active models support a specific capability
   */
  const checkAllModelsSupport = useCallback(
    (capability: string): boolean => {
      if (chatSessions.length === 0) return false;

      const modelList = providers.flatMap((provider) => provider.models);
      const chatSessionModelDetails = chatSessions
        .map((cs) => modelList.find((m) => m.id === cs.model?.id))
        .filter((m): m is NonNullable<typeof m> => m != null);

      return (
        chatSessionModelDetails.length === chatSessions.length &&
        chatSessionModelDetails.every((model) =>
          modelSupportsCapability(model, capability)
        )
      );
    },
    [chatSessions, providers, modelSupportsCapability]
  );

  return {
    providers,
    chatSessions,
    setChatSessions,
    getModelsForProvider,
    handleModelChange,
    removeComparisonModel,
    canSearch,
    canUpload,
    useWebSearch,
    setUseWebSearch,
    canGenerateImages,
    useImageGeneration,
    setUseImageGeneration,
    checkAllModelsSupport,
    modelSupportsCapability,
  };
}
