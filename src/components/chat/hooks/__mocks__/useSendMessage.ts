import { useState } from 'react';

// Mock implementation of useSendMessage hook
export const useSendMessage = () => {
  const [processingModels, setProcessingModels] = useState<Set<string>>(new Set());
  const [isUploading, setIsUploading] = useState(false);

  const handleSendMessage = jest.fn(async () => {
    // Simulate processing time
    setProcessingModels(new Set(['mock-model-id']));

    // After a short delay, update the state to simulate message sent
    setTimeout(() => {
      setProcessingModels(new Set());
    }, 100);

    return Promise.resolve();
  });

  const handleRetryMessage = jest.fn(async (modelId: string) => {
    setProcessingModels(new Set([modelId]));

    setTimeout(() => {
      setProcessingModels(new Set());
    }, 100);

    return Promise.resolve();
  });

  const editMessage = jest.fn(async (modelId: string) => {
    setProcessingModels(new Set([modelId]));

    setTimeout(() => {
      setProcessingModels(new Set());
    }, 100);

    return Promise.resolve();
  });

  const handleUpload = jest.fn(async () => {
    setIsUploading(true);

    // Simulate upload delay
    setTimeout(() => {
      setIsUploading(false);
    }, 100);

    return Promise.resolve();
  });

  const handleRemoveFile = jest.fn(() => {
    // Mock implementation - just a no-op function that can be spied on
  });

  return {
    handleSendMessage,
    processingModels,
    setProcessingModels,
    handleRetryMessage,
    editMessage,
    handleUpload,
    isUploading,
    handleRemoveFile,
  };
};

export default useSendMessage;