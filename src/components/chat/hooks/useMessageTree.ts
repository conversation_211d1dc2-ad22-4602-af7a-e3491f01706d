import { useCallback } from 'react';
import { MessageNode } from '@/lib/supabase/types';
import { useState } from 'react';

export const useMessageTree = () => {
  // Track selected branch indices for each parent message
  const [selectedBranches, setSelectedBranches] = useState<Map<string, number>>(
    new Map()
  );

  const getFlattenedMessages = useCallback(
    (root: MessageNode | null): MessageNode[] => {
      const result: MessageNode[] = [];
      let current = root;

      while (current) {
        result.push(current);
        if (current.children?.length) {
          const selectedIndex =
            selectedBranches.get(current.id) ?? current.children.length - 1; // Default to latest
          current = current.children[selectedIndex];
        } else {
          current = null;
        }
      }

      return result;
    },
    [selectedBranches]
  );

  const handleBranchChange = (
    messageId: string,
    direction: number,
    siblingSize: number
  ) => {
    setSelectedBranches((prev) => {
      const currentIndex = prev.get(messageId) ?? siblingSize - 1;
      const newIndex = Math.max(0, currentIndex + direction);
      return new Map(prev).set(messageId, newIndex);
    });
  };

  return {
    selectedBranches,
    getFlattenedMessages,
    handleBranchChange,
  };
};
