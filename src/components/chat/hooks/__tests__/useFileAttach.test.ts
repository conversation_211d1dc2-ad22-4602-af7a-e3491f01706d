import { renderHook, act } from '@testing-library/react';
import { useFileAttach } from '../useFileAttach';
import { toast } from 'sonner';

// Mock the toast
jest.mock('sonner', () => ({
  toast: {
    info: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock the logger
jest.mock('@/lib/logger', () => ({
  logger: {
    child: () => ({
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
    }),
  },
}));

describe('useFileAttach Hook', () => {
  // Create test file objects
  const createMockFile = (name: string, type: string, size: number) => {
    const file = new File(['test'], name, { type });
    Object.defineProperty(file, 'size', { value: size });
    return file;
  };

  const mockImageFile = createMockFile('image.png', 'image/png', 1024 * 1024); // 1MB
  const mockPdfFile = createMockFile('document.pdf', 'application/pdf', 2 * 1024 * 1024); // 2MB
  const mockLargeFile = createMockFile('large.pdf', 'application/pdf', 101 * 1024 * 1024); // 101MB
  const mockUnsupportedFile = createMockFile('script.js', 'text/javascript', 512); // Unsupported mime type

  beforeEach(() => {
    jest.clearAllMocks();
    // @ts-expect-error Mocking FileReader missing static properties
    global.FileReader = jest.fn().mockImplementation(() => ({
      readAsDataURL: jest.fn(),
      onloadend: null,
      result: 'data:image/png;base64,fakeimagedataurl',
    }));
  });

  test('initializes with empty selected files array', () => {
    const { result } = renderHook(() => useFileAttach());

    expect(result.current.selectedFiles).toEqual([]);
    expect(result.current.fileInputRef).toBeDefined();
  });

  test('processes valid files correctly', () => {
    const onFilesAdded = jest.fn();
    const { result } = renderHook(() => useFileAttach({ onFilesAdded, canUpload: true }));

    // Create a synthetic change event
    const changeEvent = {
      target: {
        files: [mockImageFile, mockPdfFile],
        value: '',
      },
    } as unknown as React.ChangeEvent<HTMLInputElement>;

    act(() => {
      result.current.handleFileChange(changeEvent);
    });

    expect(result.current.selectedFiles.length).toBe(2);
    expect(result.current.selectedFiles[0].name).toBe('image.png');
    expect(result.current.selectedFiles[0].isImage).toBe(true);
    expect(result.current.selectedFiles[1].name).toBe('document.pdf');
    expect(result.current.selectedFiles[1].isImage).toBe(false);
    expect(onFilesAdded).toHaveBeenCalledTimes(1);
  });

  test('rejects files that are too large', () => {
    const onFilesAdded = jest.fn();
    const { result } = renderHook(() => useFileAttach({ onFilesAdded, canUpload: true }));

    // Create a synthetic change event with one valid and one too large file
    const changeEvent = {
      target: {
        files: [mockImageFile, mockLargeFile],
        value: '',
      },
    } as unknown as React.ChangeEvent<HTMLInputElement>;

    act(() => {
      result.current.handleFileChange(changeEvent);
    });

    expect(result.current.selectedFiles.length).toBe(1);
    expect(result.current.selectedFiles[0].name).toBe('image.png');
    expect(toast.error).toHaveBeenCalledWith(expect.stringContaining('too large'));
  });

  test('rejects unsupported file types', () => {
    const onFilesAdded = jest.fn();
    const { result } = renderHook(() => useFileAttach({ onFilesAdded, canUpload: true }));

    // Create a synthetic change event with one valid and one unsupported file
    const changeEvent = {
      target: {
        files: [mockImageFile, mockUnsupportedFile],
        value: '',
      },
    } as unknown as React.ChangeEvent<HTMLInputElement>;

    act(() => {
      result.current.handleFileChange(changeEvent);
    });

    expect(result.current.selectedFiles.length).toBe(1);
    expect(result.current.selectedFiles[0].name).toBe('image.png');
    expect(toast.error).toHaveBeenCalledWith(expect.stringContaining('not allowed'));
  });

  test('limits the number of files that can be added', () => {
    const onFilesAdded = jest.fn();
    const { result } = renderHook(() => useFileAttach({ maxFiles: 2, onFilesAdded, canUpload: true }));

    // Create a synthetic change event with three files when max is 2
    const changeEvent = {
      target: {
        files: [mockImageFile, mockPdfFile, mockImageFile],
        value: '',
      },
    } as unknown as React.ChangeEvent<HTMLInputElement>;

    act(() => {
      result.current.handleFileChange(changeEvent);
    });

    expect(result.current.selectedFiles.length).toBe(2);
    expect(onFilesAdded).toHaveBeenCalledTimes(1);
  });

  test('shows warning when maximum files reached', () => {
    const { result } = renderHook(() => useFileAttach({ maxFiles: 2, canUpload: true }));

    // First, add 2 files (the max)
    const changeEvent1 = {
      target: {
        files: [mockImageFile, mockPdfFile],
        value: '',
      },
    } as unknown as React.ChangeEvent<HTMLInputElement>;

    act(() => {
      result.current.handleFileChange(changeEvent1);
    });

    // Then try to add another
    const changeEvent2 = {
      target: {
        files: [mockImageFile],
        value: '',
      },
    } as unknown as React.ChangeEvent<HTMLInputElement>;

    act(() => {
      result.current.handleFileChange(changeEvent2);
    });

    expect(toast.info).toHaveBeenCalledWith('Maximum number of files reached.');
  });

  test('removes a file correctly', () => {
    const onFileRemoved = jest.fn();
    const { result } = renderHook(() => useFileAttach({ onFileRemoved, canUpload: true }));

    // Add files
    const changeEvent = {
      target: {
        files: [mockImageFile, mockPdfFile],
        value: '',
      },
    } as unknown as React.ChangeEvent<HTMLInputElement>;

    act(() => {
      result.current.handleFileChange(changeEvent);
    });

    // Remove a file
    act(() => {
      result.current.onRemoveFile('image.png');
    });

    expect(result.current.selectedFiles.length).toBe(1);
    expect(result.current.selectedFiles[0].name).toBe('document.pdf');
    expect(onFileRemoved).toHaveBeenCalledWith('image.png');
  });

  test('handles paste events with files', () => {
    const onFilesAdded = jest.fn();
    const { result } = renderHook(() => useFileAttach({
      onFilesAdded,
      canUpload: true
    }));

    // Create a synthetic paste event
    const pasteEvent = {
      clipboardData: {
        files: [mockImageFile],
      },
      preventDefault: jest.fn(),
    } as unknown as React.ClipboardEvent<HTMLTextAreaElement>;

    act(() => {
      result.current.handlePaste(pasteEvent);
    });

    expect(result.current.selectedFiles.length).toBe(1);
    expect(result.current.selectedFiles[0].name).toBe('image.png');
    expect(pasteEvent.preventDefault).toHaveBeenCalled();
    expect(onFilesAdded).toHaveBeenCalledTimes(1);
  });

  test('does not process pasted files when canUpload is false', () => {
    const onFilesAdded = jest.fn();
    const { result } = renderHook(() => useFileAttach({
      onFilesAdded,
      canUpload: false
    }));

    // Create a synthetic paste event
    const pasteEvent = {
      clipboardData: {
        files: [mockImageFile],
      },
      preventDefault: jest.fn(),
    } as unknown as React.ClipboardEvent<HTMLTextAreaElement>;

    act(() => {
      result.current.handlePaste(pasteEvent);
    });

    expect(result.current.selectedFiles.length).toBe(0);
    expect(pasteEvent.preventDefault).not.toHaveBeenCalled();
    expect(onFilesAdded).not.toHaveBeenCalled();
  });
});