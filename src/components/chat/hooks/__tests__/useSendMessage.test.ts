import React from 'react';
import { renderHook, act } from '@testing-library/react';
import { useSendMessage } from '../useSendMessage';
import { toast } from 'sonner';
import { ChatSession } from '@/providers/ChatProvider';
import { MessageNode, Model } from '@/lib/supabase/types';

// Add setImmediate polyfill
if (typeof setImmediate === 'undefined') {
  (
    global as unknown as {
      setImmediate: (callback: () => void) => NodeJS.Timeout;
    }
  ).setImmediate = (callback: () => void) => setTimeout(callback, 0);
}

// Mock logger
jest.mock('@/lib/logger', () => ({
  logger: {
    child: () => ({
      error: jest.fn(),
      info: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
    }),
  },
}));

// Mock dependencies
jest.mock('uuid', () => ({
  v4: jest.fn(() => 'mock-uuid'),
}));

jest.mock('sonner', () => ({
  toast: {
    error: jest.fn(),
  },
}));

jest.mock('@/hooks/useAnalytics', () => ({
  useAnalytics: () => ({
    trackMessageSent: jest.fn(),
    trackMessageRetried: jest.fn(),
  }),
}));

jest.mock('react', () => ({
  ...jest.requireActual('react'),
  useContext: jest.fn(),
}));

jest.mock('@/providers/SubscriptionProvider', () => ({
  useSubscription: () => ({
    quotaStatus: null,
    canSendMessage: true,
    canUseComparison: true,
    getPlan: () => 'free',
  }),
}));

// Helper function to create a mock Model
const createMockModel = (overrides: Partial<Model> = {}) => {
  return {
    id: 'gpt-4',
    name: 'gpt-4',
    display_name: 'GPT-4',
    created_at: null,
    updated_at: null,
    is_active: true,
    is_visible_by_default: true,
    allows_file_upload: false,
    allows_search: false,
    allows_tool_usage: false,
    max_tokens: 8192,
    openrouter_name: 'openai/gpt-4',
    provider_id: 'openai',
    priority: 1,
    tier: 'premium' as const,
    config: {},
    context_length: 8192,
    description: 'GPT-4 model for testing',
    last_synced: new Date().toISOString(),
    capabilities: {
      file_upload: false,
      web_search: false,
      visible_by_default: true,
      image_generation: false,
      code_generation: true,
      function_calling: false,
      reasoning: false,
      structured_output: false,
    },
    supported_parameters: ['temperature', 'max_tokens'],
    architecture: {
      modality: 'text',
      input_modalities: ['text'],
      output_modalities: ['text'],
      tokenizer: 'gpt-4',
      instruct_type: null,
    },
    pricing: {
      prompt: '0.03',
      completion: '0.06',
    },
    provider_specific_data: {
      context_length: 8192,
      max_completion_tokens: 4096,
      is_moderated: false,
      per_request_limits: null,
    },
    ...overrides,
  };
};

// Helper function to create a mock MessageNode
const createMockMessageNode = (
  overrides: Partial<MessageNode>
): MessageNode => {
  return {
    id: 'default-id',
    role: 'user', // Default role
    content: '',
    created_at: new Date().toISOString(),
    children: [],
    parent_message_id: null,
    conversation_id: 'conv-1', // Default conversation_id
    model_id: null,
    provider_id: null,
    tokens_used: 0,
    metadata: {},
    annotations: [],
    attachments: null,
    file_annotations: null,
    updated_at: null,
    modelData: null,
    ...overrides,
  } as MessageNode; // Cast if MessageNode has complex subtypes not fully mocked
};

describe('useSendMessage', () => {
  // Setup common test variables
  const mockSetChatSessions = jest.fn();
  const mockSetInputMessage = jest.fn();
  const mockSubscribeToStream = jest.fn();

  const mockChatSession = {
    id: 'model-1',
    model: createMockModel(),
    conversationId: 'conv-1',
    conversationState: 'healthy' as const,
    parentMessageNode: {
      id: 'root',
      role: 'system',
      content: '',
      created_at: new Date().toISOString(),
      children: [],
      conversation_id: null,
      model_id: null,
      parent_message_id: null,
      provider_id: null,
      metadata: {},
      annotations: [],
      updated_at: null,
      tokens_used: 0,
      attachments: null,
      modelData: {
        id: 'gpt-4',
        name: 'gpt-4',
        display_name: 'GPT-4',
        created_at: null,
        updated_at: null,
        is_active: true,
        is_visible_by_default: true,
        allows_file_upload: false,
        allows_search: false,
        allows_tool_usage: false,
        max_tokens: 8192,
        openrouter_name: 'openai/gpt-4',
        provider_id: 'openai',
        priority: 1,
        tier: 'premium' as const,
        config: {},
        context_length: 8192,
        description: 'GPT-4 model for testing',
        last_synced: new Date().toISOString(),
        capabilities: {
          file_upload: false,
          web_search: false,
          visible_by_default: true,
          image_generation: false,
          code_generation: true,
          function_calling: false,
          reasoning: false,
          structured_output: false,
        },
        supported_parameters: ['temperature', 'max_tokens'],
        architecture: {
          modality: 'text',
          input_modalities: ['text'],
          output_modalities: ['text'],
          tokenizer: 'gpt-4',
          instruct_type: null,
        },
        pricing: {
          prompt: '0.03',
          completion: '0.06',
        },
        provider_specific_data: {
          context_length: 8192,
          max_completion_tokens: 4096,
          is_moderated: false,
          per_request_limits: null,
        },
      },
    },
  };

  const mockProviders = [
    {
      id: 'provider-1',
      name: 'Provider 1',
      display_name: 'Provider 1',
      created_at: null,
      updated_at: null,
      is_active: true,
      openrouter_name: null,
      supports_native: true,
      supports_openrouter: false,
      supports_tool_usage: false,
      config: {},
      models: [createMockModel()],
    },
  ];

  const mockGetFlattenedMessages = jest.fn(() => [
    createMockMessageNode({
      id: 'user-1',
      role: 'user',
      content: 'Original message',
      // Other necessary fields are covered by createMockMessageNode defaults or can be overridden
    }),
  ]);

  const defaultProps = {
    chatSessions: [mockChatSession],
    setChatSessions: mockSetChatSessions,
    groupConversationId: 'group-1',
    isTestMode: false,
    getFlattenedMessages: mockGetFlattenedMessages,
    providers: mockProviders,
    setInputMessage: mockSetInputMessage,
    useWebSearch: false,
    useImageGeneration: false,
    isTemporary: false,
  };

  // Helper to create a mock Response
  const createMockResponse = (options: Partial<Response>): Response => ({
    ok: true,
    status: 200,
    statusText: 'OK',
    headers: new Headers(),
    redirected: false,
    type: 'basic',
    url: '',
    body: null,
    bodyUsed: false,
    clone: () => createMockResponse(options),
    arrayBuffer: () => Promise.resolve(new ArrayBuffer(0)),
    blob: () => Promise.resolve(new Blob()),
    formData: () => Promise.resolve(new FormData()),
    json: () => Promise.resolve({}),
    text: () => Promise.resolve(''),
    bytes: () => Promise.resolve(new Uint8Array()),
    ...options,
  });

  // Helper to mock fetch responses
  const mockFetchResponses = (
    responses: Array<[string, Partial<Response>]>
  ) => {
    global.fetch = jest.fn().mockImplementation((url) => {
      for (const [pattern, response] of responses) {
        if (url.includes(pattern)) {
          return Promise.resolve(createMockResponse(response));
        }
      }
      return Promise.resolve(
        createMockResponse({ ok: false, statusText: 'Not found' })
      );
    });
  };

  // Helper to create a mock readable stream
  const createMockStream = (chunks: string[]): ReadableStream<Uint8Array> => {
    let chunkIndex = 0;
    const readerProperties = {
      read: jest.fn().mockImplementation(() => {
        if (chunkIndex < chunks.length) {
          const chunk = chunks[chunkIndex];
          chunkIndex++;
          const value = new TextEncoder().encode(chunk ?? ''); // Handle null/undefined chunks
          return Promise.resolve({ value, done: false });
        }
        return Promise.resolve({ value: undefined, done: true });
      }),
      closed: new Promise<void>(() => {}), // Mock for an open stream
      cancel: jest.fn(() => Promise.resolve()),
      releaseLock: jest.fn(),
    };

    return {
      getReader: () => readerProperties,
      locked: false,
      cancel: jest.fn(() => readerProperties.cancel()),
      pipeTo: jest.fn().mockResolvedValue(undefined),
      pipeThrough: jest
        .fn()
        .mockImplementation(
          () => createMockStream([]) as ReadableStream<Uint8Array>
        ),
      tee: jest
        .fn()
        .mockImplementation(() => [
          createMockStream([]) as ReadableStream<Uint8Array>,
          createMockStream([]) as ReadableStream<Uint8Array>,
        ]),
    } as ReadableStream<Uint8Array>;
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock useContext to return different values for SSE vs NDJSON tests
    jest.spyOn(React, 'useContext').mockImplementation(() => ({
      subscribeToStream: mockSubscribeToStream,
      isSSEEnabled: false, // Default to NDJSON for initial tests
    }));

    // Default session storage mock
    Object.defineProperty(window, 'sessionStorage', {
      value: {
        getItem: jest.fn(() => null),
        setItem: jest.fn(),
      },
      writable: true,
    });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Server-Sent Events (SSE) Streaming', () => {
    beforeEach(() => {
      // Enable SSE for these tests
      jest.spyOn(React, 'useContext').mockImplementation(() => ({
        subscribeToStream: mockSubscribeToStream,
        isSSEEnabled: true,
      }));
    });

    test('should send a message using SSE streaming', async () => {
      mockFetchResponses([
        [
          '/api/chat/stream-sse/init',
          {
            ok: true,
            json: () =>
              Promise.resolve({
                conversationId: 'conv-1',
                groupConversationId: 'group-1',
                assistantMessageId: 'assistant-1',
                userMessageId: 'user-1',
                isNewConversation: false,
                isTemporary: false,
              }),
          },
        ],
      ]);

      const { result } = renderHook(() => useSendMessage(defaultProps));

      await act(async () => {
        await result.current.handleSendMessage('Test message');
      });

      // Verify fetch was called with correct endpoint
      expect(global.fetch).toHaveBeenCalledWith(
        '/api/chat/stream-sse/init',
        expect.objectContaining({
          method: 'POST',
          body: expect.stringContaining('Test message'),
        })
      );

      // Verify subscribeToStream was called correctly
      expect(mockSubscribeToStream).toHaveBeenCalledWith(
        'assistant-1',
        'conv-1',
        'gpt-4',
        expect.any(Boolean), // isTestMode
        expect.any(Boolean), // useWebSearch
        expect.any(Boolean), // useImageGeneration
        expect.any(Function), // onDelta
        expect.any(Function), // onAnnotation
        expect.any(Function), // onError
        expect.any(Function), // onDone
        expect.any(Function), // onMetadata
        undefined // workspaceId
      );
    });

    test('should handle errors during SSE initialization', async () => {
      mockFetchResponses([
        [
          '/api/chat/stream-sse/init',
          {
            ok: false,
            statusText: 'Server Error',
          },
        ],
      ]);

      // Store the state updater functions so we can examine them
      let errorStateSet = false;
      mockSetChatSessions.mockImplementation((updater) => {
        if (typeof updater === 'function') {
          // Apply the update to a test model and check if it sets error state
          const testSession = { ...mockChatSession };
          const updatedSessions = updater([testSession]);
          if (updatedSessions[0]?.conversationState === 'error') {
            errorStateSet = true;
          }
        }
        // Return a mock result
        return [];
      });

      const { result } = renderHook(() => useSendMessage(defaultProps));

      await act(async () => {
        await result.current.handleSendMessage('Test message');
      });

      // Verify error toast was displayed
      expect(toast.error).toHaveBeenCalled();

      // Verify the mockSetChatSessions was called with a function that sets error state
      expect(errorStateSet).toBe(true);

      // Direct test of the error state handling function
      const initialState = [{ ...mockChatSession }];
      // Manually simulate updating the session state to error
      const errorUpdater = (sessions: ChatSession[]) =>
        sessions.map((session: ChatSession) => ({
          ...session,
          conversationState: 'error',
        }));
      const finalState = errorUpdater(initialState);
      expect(finalState[0]?.conversationState).toBe('error');
    });

    test('should retry a message using SSE streaming', async () => {
      mockFetchResponses([
        [
          '/api/chat/stream-sse/init',
          {
            ok: true,
            json: () =>
              Promise.resolve({
                conversationId: 'conv-1',
                assistantMessageId: 'assistant-2',
              }),
          },
        ],
      ]);

      // Mock getFlattenedMessages to return a user message
      const localMockGetFlattenedMessagesSSE = jest.fn(() => [
        createMockMessageNode({
          id: 'user-1',
          role: 'user',
          content: 'Original message',
          created_at: new Date().toISOString(),
          // ensure all required fields for MessageNode are present
        }),
      ]);

      const { result } = renderHook(() =>
        useSendMessage({
          ...defaultProps,
          getFlattenedMessages: localMockGetFlattenedMessagesSSE, // Use the local mock here
        })
      );

      await act(async () => {
        await result.current.handleRetryMessage('model-1');
      });

      // Verify fetch was called with PUT method to the SSE init endpoint
      expect(global.fetch).toHaveBeenCalledWith(
        '/api/chat/stream-sse/init',
        expect.objectContaining({
          method: 'PUT',
          body: expect.stringContaining('messageId'),
        })
      );

      // Verify subscribeToStream was called (retry doesn't pass workspaceId)
      expect(mockSubscribeToStream).toHaveBeenCalledWith(
        'assistant-2',
        'conv-1',
        'gpt-4',
        expect.any(Boolean), // isTestMode
        expect.any(Boolean), // useWebSearch
        expect.any(Boolean), // useImageGeneration
        expect.any(Function), // onDelta
        expect.any(Function), // onAnnotation
        expect.any(Function), // onError
        expect.any(Function), // onDone
        expect.any(Function) // onMetadata
      );
    });

    test('should update state when SSE sends deltas', async () => {
      // Mock requestAnimationFrame to execute immediately in tests
      const originalRAF = global.requestAnimationFrame;
      global.requestAnimationFrame = jest.fn((callback) => {
        callback(0);
        return 0;
      });

      // Set up proper mock response for SSE init
      mockFetchResponses([
        [
          '/api/chat/stream-sse/init',
          {
            ok: true,
            json: () =>
              Promise.resolve({
                conversationId: 'conv-1',
                groupConversationId: 'group-1',
                assistantMessageId: 'assistant-1',
                userMessageId: 'user-1',
                isNewConversation: false,
              }),
          },
        ],
      ]);

      // Create an initial message tree where the assistant-1 message will be inserted
      const mockGetFlattenedMessagesWithStructure = jest
        .fn()
        .mockImplementation((_root?: MessageNode | null) => {
          // Create a simulated message tree with an assistant message that will be updated
          if (_root) {
            // Find and return the user-1 and assistant-1 messages from the tree
            const messages = [];
            const userMessage = findNodeById(_root, 'user-1');
            if (userMessage) messages.push(userMessage);
            const assistantMessage = findNodeById(_root, 'assistant-1');
            if (assistantMessage) messages.push(assistantMessage);
            return messages;
          }
          // Default empty array
          return [];
        });

      // Set up initial session state to include our message structure
      const initialSession = {
        ...mockChatSession,
        parentMessageNode: createMockMessageNode({
          id: 'root',
          role: 'system',
          children: [
            createMockMessageNode({
              id: 'user-1',
              role: 'user',
              content: 'Test message',
              children: [
                createMockMessageNode({
                  id: 'assistant-1',
                  role: 'assistant',
                  content: '', // Initially empty
                }),
              ],
            }),
          ],
        }),
      };

      // Mock subscribeToStream to immediately update the assistant message with content
      mockSubscribeToStream.mockImplementation(
        (id, convId, model, isTest, useWeb, useImageGen, onDelta) => {
          expect(id).toBe('assistant-1'); // Verify correct ID is being used
          // Call the delta callback to simulate receiving a message
          // Wrap in act to ensure React updates are processed
          act(() => {
            onDelta('Test response');
          });
          return jest.fn(); // Return mock unsubscribe function
        }
      );

      const { result } = renderHook(() =>
        useSendMessage({
          ...defaultProps,
          chatSessions: [initialSession],
          getFlattenedMessages: mockGetFlattenedMessagesWithStructure,
        })
      );

      await act(async () => {
        await result.current.handleSendMessage('Test message');
      });

      // Get the final state after updates
      const finalState = evaluateStateUpdaters(mockSetChatSessions, [
        initialSession,
      ]);

      // Find the assistant message node and check content
      const finalRootNode = finalState[0]?.parentMessageNode;
      const assistantNode = findNodeById(finalRootNode, 'assistant-1');

      expect(assistantNode).not.toBeNull();
      expect(assistantNode?.content).toBe('Test response');

      // Restore original RAF
      global.requestAnimationFrame = originalRAF;
    });

    test('should handle SSE completion', async () => {
      mockFetchResponses([
        [
          '/api/chat/stream-sse/init',
          {
            ok: true,
            json: () =>
              Promise.resolve({
                conversationId: 'conv-1',
                assistantMessageId: 'assistant-1',
                userMessageId: 'user-1',
              }),
          },
        ],
      ]);

      // Mock that subscribeToStream calls the done callback
      mockSubscribeToStream.mockImplementation(
        (
          id,
          convId,
          model,
          isTest,
          useWeb,
          useImageGen,
          onDelta,
          onAnnotation,
          onError,
          onDone,
          onMetadata
        ) => {
          // Call the done callback to simulate completion
          onDone();
          onMetadata({
            version: '1',
            data: {
              test: 'test',
            },
          });
          return;
        }
      );

      const { result } = renderHook(() => useSendMessage(defaultProps));

      await act(async () => {
        await result.current.handleSendMessage('Test message');
      });

      // Verify model state was updated to healthy
      let healthyStateWasSet = false;
      mockSetChatSessions.mock.calls.forEach((call) => {
        const updateFn = call[0];
        if (typeof updateFn === 'function') {
          // Test the function with a copy of our model
          const testState = [{ ...mockChatSession }];
          const updatedState = updateFn(testState);

          // Check if any of these updates set the state to healthy
          if (updatedState[0]?.conversationState === 'healthy') {
            healthyStateWasSet = true;
          }
        }
      });

      expect(healthyStateWasSet).toBe(true);
    });

    test('should handle SSE error events', async () => {
      mockFetchResponses([
        [
          '/api/chat/stream-sse/init',
          {
            ok: true,
            json: () =>
              Promise.resolve({
                conversationId: 'conv-1',
                groupConversationId: 'group-1',
                assistantMessageId: 'assistant-1',
                userMessageId: 'user-1',
              }),
          },
        ],
      ]);

      // Mock that subscribeToStream calls the error callback
      mockSubscribeToStream.mockImplementation(
        (
          id,
          convId,
          model,
          isTest,
          useWeb,
          useImageGen,
          onDelta,
          onAnnotation,
          onError
        ) => {
          // Call the error callback to simulate an error
          onError(new Error('SSE connection error'));
          return;
        }
      );

      const { result } = renderHook(() => useSendMessage(defaultProps));

      await act(async () => {
        await result.current.handleSendMessage('Test message');
      });

      // Verify error toast was displayed
      expect(toast.error).toHaveBeenCalledWith('Error: SSE connection error');

      // Verify model state was updated to error
      let errorStateWasSet = false;
      mockSetChatSessions.mock.calls.forEach((call) => {
        const updateFn = call[0];
        if (typeof updateFn === 'function') {
          // Test the function with a copy of our model
          const testState = [{ ...mockChatSession }];
          const updatedState = updateFn(testState);

          // Check if any of these updates set the state to error
          if (updatedState[0]?.conversationState === 'error') {
            errorStateWasSet = true;
          }
        }
      });

      expect(errorStateWasSet).toBe(true);
    });

    test('should generate new group ID for new conversations and reuse existing for ongoing ones', async () => {
      // Test 1: New conversation (no messages in session)
      const emptyChatSession = {
        ...mockChatSession,
        parentMessageNode: null,
      };

      const emptySessionProps = {
        ...defaultProps,
        chatSessions: [emptyChatSession],
        groupConversationId: undefined, // No existing group ID
      };

      mockFetchResponses([
        [
          '/api/chat/stream-sse/init',
          {
            ok: true,
            json: () =>
              Promise.resolve({
                conversationId: 'conv-new',
                groupConversationId: 'group-new',
                assistantMessageId: 'assistant-new',
                userMessageId: 'user-new',
                isNewConversation: true,
                isTemporary: false,
              }),
          },
        ],
      ]);

      const mockGetFlattenedMessagesEmpty = jest.fn(() => []); // No messages = new conversation

      const { result: newResult } = renderHook(() =>
        useSendMessage({
          ...emptySessionProps,
          getFlattenedMessages: mockGetFlattenedMessagesEmpty,
        })
      );

      await act(async () => {
        await newResult.current.handleSendMessage(
          'First message in new conversation'
        );
      });

      // Verify that a new UUID was generated (not reusing any existing group ID)
      const firstCallBody = JSON.parse(
        (global.fetch as jest.Mock).mock.calls[0][1].body
      );
      expect(firstCallBody.groupConversationId).toBeDefined();
      expect(firstCallBody.groupConversationId).not.toBe('group-1'); // Should be a new UUID

      // Test 2: Existing conversation (has messages in session)
      jest.clearAllMocks();

      const existingChatSession = {
        ...mockChatSession,
        parentMessageNode: createMockMessageNode({
          id: 'existing-message',
          role: 'user',
          content: 'Previous message',
        }),
      };

      const existingSessionProps = {
        ...defaultProps,
        chatSessions: [existingChatSession],
        groupConversationId: 'existing-group-id',
      };

      mockFetchResponses([
        [
          '/api/chat/stream-sse/init',
          {
            ok: true,
            json: () =>
              Promise.resolve({
                conversationId: 'conv-existing',
                groupConversationId: 'existing-group-id',
                assistantMessageId: 'assistant-existing',
                userMessageId: 'user-existing',
                isNewConversation: false,
                isTemporary: false,
              }),
          },
        ],
      ]);

      const mockGetFlattenedMessagesExisting = jest.fn(() => [
        createMockMessageNode({
          id: 'existing-message',
          role: 'user',
          content: 'Previous message',
        }),
      ]); // Has messages = existing conversation

      const { result: existingResult } = renderHook(() =>
        useSendMessage({
          ...existingSessionProps,
          getFlattenedMessages: mockGetFlattenedMessagesExisting,
        })
      );

      await act(async () => {
        await existingResult.current.handleSendMessage(
          'Follow-up message in existing conversation'
        );
      });

      // Verify that the existing group ID was reused
      const secondCallBody = JSON.parse(
        (global.fetch as jest.Mock).mock.calls[0][1].body
      );
      expect(secondCallBody.groupConversationId).toBe('existing-group-id'); // Should reuse existing
    });
  });

  describe('Attachment Handling', () => {
    test('should include attachments in message request', async () => {
      const mockAttachments = [
        {
          name: 'test.pdf',
          type: 'application/pdf',
          url: 'http://example.com/test.pdf',
        },
      ];

      // Mock fetch response
      mockFetchResponses([
        [
          '/api/chat/stream',
          {
            ok: true,
            headers: new Headers({
              'X-Conversation-Id': 'conv-1',
              'X-Assistant-Message-Id': 'assistant-1',
            }),
            body: createMockStream([
              JSON.stringify({ type: 'delta', content: 'Response' }) + '\n',
              '',
            ]),
          },
        ],
      ]);

      const { result } = renderHook(() => useSendMessage(defaultProps));

      // Set attachments
      act(() => {
        result.current.handleUpload = jest.fn().mockImplementation(() => {
          return mockAttachments;
        });
      });

      // Since we can't directly set the attachments state, we'll mock it through the dependencies
      global.fetch = jest.fn().mockImplementation((url, options) => {
        const body = JSON.parse(options.body);
        // Verify attachments are included
        expect(body.attachments).toContainEqual(
          expect.objectContaining({
            name: 'test.pdf',
          })
        );

        return Promise.resolve({
          ok: true,
          headers: new Headers({
            'X-Conversation-Id': 'conv-1',
            'X-Assistant-Message-Id': 'assistant-1',
          }),
          body: createMockStream([
            JSON.stringify({ type: 'delta', content: 'Response' }) + '\n',
            '',
          ]),
        });
      });

      await act(async () => {
        await result.current.handleSendMessage('Test message with attachment');
      });

      expect(global.fetch).toHaveBeenCalled();
    });
  });

  describe('General Behavior', () => {
    beforeEach(() => {
      // Ensure SSE mode for general behavior tests
      jest.spyOn(React, 'useContext').mockImplementation(() => ({
        subscribeToStream: mockSubscribeToStream,
        isSSEEnabled: true,
      }));
      // Mock SSE init endpoint
      mockFetchResponses([
        [
          '/api/chat/stream-sse/init',
          {
            ok: true,
            json: () =>
              Promise.resolve({
                conversationId: 'conv-1',
                assistantMessageId: 'assistant-1',
                userMessageId: 'user-1',
                isNewConversation: false,
                isTemporary: false,
              }),
          },
        ],
      ]);
      // Auto-complete SSE stream
      mockSubscribeToStream.mockImplementation(
        (
          _id,
          _convId,
          _model,
          _isTest,
          _useSearch,
          _useImageGen,
          _onDelta,
          _onAnnotation,
          _onError,
          onDone,
          onMetadata
        ) => {
          onDone();
          onMetadata({
            version: '1',
            data: {
              test: 'test',
            },
          });
        }
      );
    });

    test('should not send empty messages', async () => {
      const { result } = renderHook(() => useSendMessage(defaultProps));

      await act(async () => {
        await result.current.handleSendMessage('   ');
      });

      expect(global.fetch).not.toHaveBeenCalled();
    });

    test('should update processing state correctly', async () => {
      // ProcessingModels should clear after SSE completion (onDone)
      const { result } = renderHook(() => useSendMessage(defaultProps));

      // Should start with empty processing models
      expect(result.current.processingModels.length).toBe(0);

      await act(async () => {
        await result.current.handleSendMessage('Test message');
      });

      // Should have cleared processing after completion
      expect(result.current.processingModels.length).toBe(0);
    });

    test('should update input message on send', async () => {
      mockFetchResponses([
        [
          '/api/chat/stream',
          {
            ok: true,
            headers: new Headers({
              'X-Conversation-Id': 'conv-1',
              'X-Assistant-Message-Id': 'assistant-1',
            }),
            body: createMockStream([
              JSON.stringify({ type: 'delta', content: 'Response' }) + '\n',
              '',
            ]),
          },
        ],
      ]);

      const { result } = renderHook(() => useSendMessage(defaultProps));

      await act(async () => {
        await result.current.handleSendMessage('Test message');
      });

      // Verify the input was cleared
      expect(mockSetInputMessage).toHaveBeenCalledWith('');
    });
  });
});

// Helper function to evaluate state updaters
function evaluateStateUpdaters(mockFn: jest.Mock, initialState: ChatSession[]) {
  const updateFunctions = mockFn.mock.calls.map((call) => call[0]);
  return updateFunctions.reduce((state, updateFn) => {
    if (typeof updateFn === 'function') {
      return updateFn(state);
    }
    return state;
  }, initialState);
}

// Helper function to find a node in the tree (replicated from useSendMessage.ts)
function findNodeById(
  node: MessageNode | null,
  id: string
): MessageNode | null {
  if (!node) return null;
  if (node.id === id) return node;
  // Check if children exists and is an array before iterating
  if (
    node.children &&
    Array.isArray(node.children) &&
    node.children.length > 0
  ) {
    for (const child of node.children) {
      const found = findNodeById(child, id);
      if (found) return found;
    }
  }
  return null;
}
