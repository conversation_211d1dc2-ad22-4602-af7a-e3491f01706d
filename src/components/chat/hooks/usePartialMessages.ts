'use client';
import { useContext, useEffect } from 'react';
import { ChatContext } from '@/providers/ChatProvider';
import { ChatSession } from '@/providers/ChatProvider';
import { MessageNode } from '@/lib/supabase/types';

/**
 * Hook to initialize streaming state from any partial messages in the provider
 * when returning to an ongoing conversation
 */
export function usePartialMessages({
  chatSessions,
  getFlattenedMessages,
}: {
  chatSessions: ChatSession[];
  getFlattenedMessages: (root: MessageNode | null) => MessageNode[];
}) {
  const { partialMessages, setChatSessions } = useContext(ChatContext);

  // When mounting this component, check if there are any partial messages
  // for the models in this conversation and apply them
  useEffect(() => {
    if (!partialMessages || partialMessages.size === 0) {
      return; // No partial messages to restore
    }

    // For each active chat session, check if its last assistant message
    // has a partial message stored in the provider
    let needsUpdate = false;
    const updatedSessions = [...chatSessions];

    chatSessions.forEach((session, index) => {
      const messages = getFlattenedMessages(session.parentMessageNode);

      // If there are messages and the last one is from the assistant
      if (messages.length > 0) {
        const lastMessage = messages[messages.length - 1];
        if (lastMessage && lastMessage.role === 'assistant') {
          const partialMessage = partialMessages.get(lastMessage.id);

          // If we have a partial message with more content than what's in the tree,
          // update the session's message tree
          if (
            partialMessage &&
            partialMessage.content &&
            partialMessage.content.length > lastMessage.content.length
          ) {
            needsUpdate = true;
            // Update the message in the tree with content from partial message
            const updatedNode = {
              ...lastMessage,
              content: partialMessage.content,
              annotations: partialMessage.annotations?.length
                ? partialMessage.annotations
                : lastMessage.annotations,
            };

            // Not mutating directly - will use setChatSessions later if needed
            updatedSessions[index] = {
              ...session,
              parentMessageNode: updateMessageNode(
                session.parentMessageNode,
                lastMessage.id,
                updatedNode
              ),
            };
          }
        }
      }
    });

    // Only update if necessary to avoid unnecessary re-renders
    if (needsUpdate) {
      setChatSessions(updatedSessions);
    }
  }, [chatSessions, partialMessages, getFlattenedMessages, setChatSessions]);

  return { hasPartialMessages: partialMessages.size > 0 };
}

// Helper function to update a message node in the tree
function updateMessageNode(
  root: MessageNode | null,
  id: string,
  updatedNode: Partial<MessageNode>
): MessageNode | null {
  if (!root) return null;

  if (root.id === id) {
    return { ...root, ...updatedNode };
  }

  if (root.children && root.children.length > 0) {
    const updatedChildren = root.children.map((child) => {
      return updateMessageNode(child, id, updatedNode) || child;
    });

    return { ...root, children: updatedChildren };
  }

  return root;
}
