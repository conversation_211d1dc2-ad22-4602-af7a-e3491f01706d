import React from 'react';
import { Model } from '@/lib/supabase/types';
import { Button } from '@/components/ui/button';
import {
  Settings,
  Search,
  Clock,
  Briefcase,
  ChevronDown,
  Check,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';

// Module-level state that persists during navigation but resets on page refresh
let globalDirtySettings = {
  webSearch: false,
  imageGeneration: false,
  workspace: false,
  temporary: false,
};

interface Workspace {
  id: string;
  name: string;
  icon?: string | null;
  description?: string | null;
}

interface ChatInputSettingsProps {
  // Existing settings
  canSearch: boolean;
  useWebSearch: boolean;
  setUseWebSearch: (value: boolean) => void;

  // Image generation
  canGenerateImages: boolean;
  useImageGeneration: boolean;
  setUseImageGeneration: (value: boolean) => void;

  // Temporary chat
  isTemporary?: boolean;
  setIsTemporary?: (value: boolean) => void;
  showTemporaryToggle?: boolean;

  // Workspace selection
  selectedWorkspaceId?: string | null;
  setSelectedWorkspaceId?: (workspaceId: string | null) => void;
  workspaces?: Workspace[];
  isLoadingWorkspaces?: boolean;
  canSelectWorkspace?: boolean;

  // New capability checking functions
  checkAllModelsSupport?: (capability: string) => boolean;
  modelSupportsCapability?: (
    model: Model | null | undefined,
    capability: string
  ) => boolean;
}

export function ChatInputSettings({
  canSearch,
  useWebSearch,
  setUseWebSearch,
  canGenerateImages,
  useImageGeneration,
  setUseImageGeneration,
  isTemporary = false,
  setIsTemporary,
  showTemporaryToggle = false,
  selectedWorkspaceId,
  setSelectedWorkspaceId,
  workspaces = [],
  isLoadingWorkspaces = false,
  canSelectWorkspace = false,
  checkAllModelsSupport,
}: ChatInputSettingsProps) {
  const [isExpanded, setIsExpanded] = React.useState(false);
  const [workspaceDropdownOpen, setWorkspaceDropdownOpen] =
    React.useState(false);

  // Track which settings have been interacted with (dirty state)
  const [dirtySettings, setDirtySettings] = React.useState<{
    webSearch: boolean;
    imageGeneration: boolean;
    workspace: boolean;
    temporary: boolean;
  }>(globalDirtySettings);

  // Update both local state and global state
  const updateDirtyState = (
    setting: keyof typeof dirtySettings,
    isDirty: boolean
  ) => {
    const newDirtySettings = { ...dirtySettings, [setting]: isDirty };
    setDirtySettings(newDirtySettings);
    globalDirtySettings = newDirtySettings; // Update global state for persistence across navigation
  };

  // Find selected workspace
  const selectedWorkspace = workspaces.find(
    (w) => w.id === selectedWorkspaceId
  );

  // Enhanced web search handler that marks as dirty
  const handleWebSearchToggle = () => {
    if (!dirtySettings.webSearch) {
      updateDirtyState('webSearch', true);
    }
    setUseWebSearch(!useWebSearch);
  };

  // Enhanced image generation handler that marks as dirty
  const handleImageGenerationToggle = () => {
    if (!dirtySettings.imageGeneration) {
      updateDirtyState('imageGeneration', true);
    }
    setUseImageGeneration(!useImageGeneration);
  };

  // Enhanced workspace handler that marks as dirty
  const handleWorkspaceSelect = (workspaceId: string | null) => {
    if (!dirtySettings.workspace) {
      updateDirtyState('workspace', true);
    }
    setSelectedWorkspaceId?.(workspaceId);
  };

  // Enhanced temporary handler that marks as dirty
  const handleTemporaryToggle = (checked: boolean) => {
    if (!dirtySettings.temporary) {
      updateDirtyState('temporary', true);
    }
    setIsTemporary?.(checked);
  };

  // Check for additional capabilities
  const canUseStructuredOutput = checkAllModelsSupport
    ? checkAllModelsSupport('structured_output')
    : false;

  // Determine which settings to show in compact mode - only if dirty or active
  const hasActiveSettings =
    useWebSearch || useImageGeneration || isTemporary || selectedWorkspaceId;
  const shouldShowWebSearch =
    canSearch && (dirtySettings.webSearch || useWebSearch);
  const shouldShowImageGeneration =
    canGenerateImages && (dirtySettings.imageGeneration || useImageGeneration);
  const shouldShowWorkspace =
    canSelectWorkspace && (dirtySettings.workspace || selectedWorkspaceId);
  const shouldShowTemporary =
    showTemporaryToggle && (dirtySettings.temporary || isTemporary);

  const showAnyCompactSettings =
    shouldShowWebSearch ||
    shouldShowImageGeneration ||
    shouldShowWorkspace ||
    shouldShowTemporary;

  const popoverContentStyle = {
    backgroundColor: 'var(--color-input-bg)',
    color: 'var(--color-input-fg)',
    backdropFilter: 'none',
    WebkitBackdropFilter: 'none',
    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
    border: '1px solid var(--color-border)',
  };

  return (
    <div className='flex items-center gap-1'>
      {/* Compact mode: Show settings that have been interacted with */}
      {showAnyCompactSettings && !isExpanded && (
        <div className='flex items-center gap-1'>
          {/* Web Search Toggle - only show if dirty or active */}
          {shouldShowWebSearch && (
            <Button
              variant={useWebSearch ? 'default' : 'ghost'}
              size='sm'
              className={cn(
                'h-7 px-2 text-xs rounded-full transition-all',
                useWebSearch
                  ? 'bg-blue-600 hover:bg-blue-700 text-white'
                  : 'text-muted-foreground hover:text-foreground hover:bg-muted'
              )}
              onClick={handleWebSearchToggle}
            >
              <Search className='h-3 w-3 mr-1' />
              Web
            </Button>
          )}

          {/* Image Generation Toggle - only show if dirty or active */}
          {shouldShowImageGeneration && (
            <Button
              variant={useImageGeneration ? 'default' : 'ghost'}
              size='sm'
              className={cn(
                'h-7 px-2 text-xs rounded-full transition-all',
                useImageGeneration
                  ? 'bg-purple-600 hover:bg-purple-700 text-white'
                  : 'text-muted-foreground hover:text-foreground hover:bg-muted'
              )}
              onClick={handleImageGenerationToggle}
            >
              🎨
              <span className='ml-1'>Image</span>
            </Button>
          )}

          {/* Workspace selector - only show if dirty or active */}
          {shouldShowWorkspace &&
            (selectedWorkspace ? (
              <Popover
                open={workspaceDropdownOpen}
                onOpenChange={setWorkspaceDropdownOpen}
              >
                <PopoverTrigger asChild>
                  <Button
                    variant='ghost'
                    size='sm'
                    className='h-7 px-2 text-xs rounded-full transition-all bg-muted text-muted-foreground hover:text-foreground hover:bg-muted/80'
                  >
                    {selectedWorkspace.icon ? (
                      <span className='text-xs mr-1'>
                        {selectedWorkspace.icon}
                      </span>
                    ) : (
                      <Briefcase className='h-3 w-3 mr-1' />
                    )}
                    <span className='max-w-16 truncate'>
                      {selectedWorkspace.name}
                    </span>
                  </Button>
                </PopoverTrigger>
                <PopoverContent
                  className='w-72 p-0'
                  align='start'
                  side='top'
                  sideOffset={8}
                  style={popoverContentStyle}
                >
                  <Command>
                    <CommandInput
                      placeholder='Search workspaces...'
                      className='h-8'
                    />
                    <CommandEmpty>
                      {isLoadingWorkspaces
                        ? 'Loading workspaces...'
                        : 'No workspaces found.'}
                    </CommandEmpty>
                    <CommandList>
                      <CommandGroup>
                        {/* Clear selection option */}
                        <CommandItem
                          value='clear-selection'
                          onSelect={() => {
                            handleWorkspaceSelect(null);
                            setWorkspaceDropdownOpen(false);
                          }}
                        >
                          <Check
                            className={cn(
                              'mr-2 h-4 w-4',
                              !selectedWorkspaceId ? 'opacity-100' : 'opacity-0'
                            )}
                          />
                          <span className='text-muted-foreground'>
                            Clear selection
                          </span>
                        </CommandItem>

                        {/* Workspace options */}
                        {workspaces.map((workspace) => (
                          <CommandItem
                            key={workspace.id}
                            value={workspace.name}
                            onSelect={() => {
                              handleWorkspaceSelect(workspace.id);
                              setWorkspaceDropdownOpen(false);
                            }}
                          >
                            <Check
                              className={cn(
                                'mr-2 h-4 w-4',
                                selectedWorkspaceId === workspace.id
                                  ? 'opacity-100'
                                  : 'opacity-0'
                              )}
                            />
                            <div className='flex items-center gap-2 flex-1 min-w-0'>
                              {workspace.icon && (
                                <span className='text-sm flex-shrink-0'>
                                  {workspace.icon}
                                </span>
                              )}
                              <div className='min-w-0 flex-1'>
                                <div className='truncate font-medium'>
                                  {workspace.name}
                                </div>
                                {workspace.description && (
                                  <div className='text-xs text-muted-foreground truncate'>
                                    {workspace.description}
                                  </div>
                                )}
                              </div>
                            </div>
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
            ) : (
              <Popover
                open={workspaceDropdownOpen}
                onOpenChange={setWorkspaceDropdownOpen}
              >
                <PopoverTrigger asChild>
                  <Button
                    variant='ghost'
                    size='sm'
                    className='h-7 px-2 text-xs rounded-full transition-all text-muted-foreground hover:text-foreground hover:bg-muted'
                  >
                    <Briefcase className='h-3 w-3 mr-1' />
                    Workspace
                  </Button>
                </PopoverTrigger>
                <PopoverContent
                  className='w-72 p-0'
                  align='start'
                  side='top'
                  sideOffset={8}
                  style={popoverContentStyle}
                >
                  <Command>
                    <CommandInput
                      placeholder='Search workspaces...'
                      className='h-8'
                    />
                    <CommandEmpty>
                      {isLoadingWorkspaces
                        ? 'Loading workspaces...'
                        : 'No workspaces found.'}
                    </CommandEmpty>
                    <CommandList>
                      <CommandGroup>
                        {/* Workspace options */}
                        {workspaces.map((workspace) => (
                          <CommandItem
                            key={workspace.id}
                            value={workspace.name}
                            onSelect={() => {
                              handleWorkspaceSelect(workspace.id);
                              setWorkspaceDropdownOpen(false);
                            }}
                          >
                            <Check
                              className={cn(
                                'mr-2 h-4 w-4',
                                selectedWorkspaceId === workspace.id
                                  ? 'opacity-100'
                                  : 'opacity-0'
                              )}
                            />
                            <div className='flex items-center gap-2 flex-1 min-w-0'>
                              {workspace.icon && (
                                <span className='text-sm flex-shrink-0'>
                                  {workspace.icon}
                                </span>
                              )}
                              <div className='min-w-0 flex-1'>
                                <div className='truncate font-medium'>
                                  {workspace.name}
                                </div>
                                {workspace.description && (
                                  <div className='text-xs text-muted-foreground truncate'>
                                    {workspace.description}
                                  </div>
                                )}
                              </div>
                            </div>
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
            ))}

          {/* Temporary indicator - only show if dirty or active */}
          {shouldShowTemporary && isTemporary && (
            <div className='flex items-center gap-1 px-2 py-1 bg-amber-100 dark:bg-amber-900/20 rounded-full text-xs text-amber-700 dark:text-amber-400'>
              <Clock className='h-3 w-3' />
              Temp
            </div>
          )}
        </div>
      )}

      {/* Settings button - always show if there are additional settings */}
      {(showTemporaryToggle || canSelectWorkspace || hasActiveSettings) && (
        <Popover open={isExpanded} onOpenChange={setIsExpanded}>
          <PopoverTrigger asChild>
            <Button
              variant='ghost'
              size='sm'
              className={cn(
                'h-7 w-7 p-0 rounded-full text-muted-foreground hover:text-foreground transition-all',
                hasActiveSettings && 'text-blue-600 hover:text-blue-700'
              )}
            >
              <Settings className='h-3.5 w-3.5' />
            </Button>
          </PopoverTrigger>
          <PopoverContent
            side='top'
            align='start'
            className='w-80 p-3 space-y-3'
            sideOffset={8}
            style={popoverContentStyle}
          >
            <div className='text-sm font-medium'>Chat Settings</div>

            {/* Web Search Setting */}
            {canSearch && (
              <div className='flex items-center justify-between'>
                <div className='flex items-center gap-2'>
                  <Search className='h-4 w-4 text-muted-foreground' />
                  <div>
                    <div className='text-sm'>Web Search</div>
                    <div className='text-xs text-muted-foreground'>
                      Search the web for current information
                    </div>
                  </div>
                </div>
                <label className='relative inline-flex cursor-pointer'>
                  <input
                    type='checkbox'
                    checked={useWebSearch}
                    onChange={() => handleWebSearchToggle()}
                    className='sr-only peer'
                  />
                  <div className="relative w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                </label>
              </div>
            )}

            {/* Image Generation Setting */}
            {canGenerateImages && (
              <div className='flex items-center justify-between'>
                <div className='flex items-center gap-2'>
                  <span className='text-muted-foreground text-sm'>🎨</span>
                  <div>
                    <div className='text-sm'>Image Generation</div>
                    <div className='text-xs text-muted-foreground'>
                      Generate images with AI when requested
                    </div>
                  </div>
                </div>
                <label className='relative inline-flex cursor-pointer'>
                  <input
                    type='checkbox'
                    checked={useImageGeneration}
                    onChange={() => handleImageGenerationToggle()}
                    className='sr-only peer'
                  />
                  <div className="relative w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-purple-300 dark:peer-focus:ring-purple-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-purple-600"></div>
                </label>
              </div>
            )}

            {/* Temporary Chat Setting */}
            {showTemporaryToggle && setIsTemporary && (
              <div className='flex items-center justify-between'>
                <div className='flex items-center gap-2'>
                  <Clock className='h-4 w-4 text-muted-foreground' />
                  <div>
                    <div className='text-sm'>Temporary Chat</div>
                    <div className='text-xs text-muted-foreground'>
                      This chat won&apos;t be saved to history
                    </div>
                  </div>
                </div>
                <label className='relative inline-flex cursor-pointer'>
                  <input
                    type='checkbox'
                    checked={isTemporary}
                    onChange={(e) => handleTemporaryToggle(e.target.checked)}
                    className='sr-only peer'
                  />
                  <div className="relative w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                </label>
              </div>
            )}

            {/* Workspace Selection */}
            {canSelectWorkspace && setSelectedWorkspaceId && (
              <div className='space-y-2'>
                <div className='flex items-center gap-2'>
                  {/* <Briefcase className='h-4 w-4 text-muted-foreground' /> */}
                  <div>
                    <div className='text-sm'>Workspace</div>
                    <div className='text-xs text-muted-foreground'>
                      Add this chat to a workspace
                    </div>
                  </div>
                </div>
                <Popover
                  open={workspaceDropdownOpen}
                  onOpenChange={setWorkspaceDropdownOpen}
                >
                  <PopoverTrigger asChild>
                    <Button
                      variant='outline'
                      role='combobox'
                      aria-expanded={workspaceDropdownOpen}
                      className='w-full justify-between text-sm h-8'
                    >
                      {selectedWorkspace ? (
                        <div className='flex items-center gap-2'>
                          {selectedWorkspace.icon && (
                            <span className='text-sm'>
                              {selectedWorkspace.icon}
                            </span>
                          )}
                          <span className='truncate'>
                            {selectedWorkspace.name}
                          </span>
                        </div>
                      ) : (
                        <span className='text-muted-foreground'>
                          Select workspace...
                        </span>
                      )}
                      <ChevronDown className='ml-2 h-3 w-3 shrink-0 opacity-50' />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent
                    className='w-72 p-0'
                    align='start'
                    style={popoverContentStyle}
                  >
                    <Command>
                      <CommandInput
                        placeholder='Search workspaces...'
                        className='h-8'
                      />
                      <CommandEmpty>
                        {isLoadingWorkspaces
                          ? 'Loading workspaces...'
                          : 'No workspaces found.'}
                      </CommandEmpty>
                      <CommandList>
                        <CommandGroup>
                          {/* None option */}
                          <CommandItem
                            value='none'
                            onSelect={() => {
                              handleWorkspaceSelect(null);
                              setWorkspaceDropdownOpen(false);
                            }}
                          >
                            <Check
                              className={cn(
                                'mr-2 h-4 w-4',
                                !selectedWorkspaceId
                                  ? 'opacity-100'
                                  : 'opacity-0'
                              )}
                            />
                            <span className='text-muted-foreground'>
                              No workspace
                            </span>
                          </CommandItem>

                          {/* Workspace options */}
                          {workspaces.map((workspace) => (
                            <CommandItem
                              key={workspace.id}
                              value={workspace.name}
                              onSelect={() => {
                                handleWorkspaceSelect(workspace.id);
                                setWorkspaceDropdownOpen(false);
                              }}
                            >
                              <Check
                                className={cn(
                                  'mr-2 h-4 w-4',
                                  selectedWorkspaceId === workspace.id
                                    ? 'opacity-100'
                                    : 'opacity-0'
                                )}
                              />
                              <div className='flex items-center gap-2 flex-1 min-w-0'>
                                {workspace.icon && (
                                  <span className='text-sm flex-shrink-0'>
                                    {workspace.icon}
                                  </span>
                                )}
                                <div className='min-w-0 flex-1'>
                                  <div className='truncate font-medium'>
                                    {workspace.name}
                                  </div>
                                  {workspace.description && (
                                    <div className='text-xs text-muted-foreground truncate'>
                                      {workspace.description}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>
            )}

            {/* Model Capabilities Info */}
            {(canGenerateImages || canUseStructuredOutput) && (
              <div className='border-t pt-3 space-y-2'>
                <div className='text-sm font-medium text-muted-foreground'>
                  Available Capabilities
                </div>
                {canGenerateImages && (
                  <div className='flex items-center gap-2 text-xs text-muted-foreground'>
                    <div className='w-2 h-2 bg-green-500 rounded-full'></div>
                    <span>Image Generation</span>
                  </div>
                )}
                {canUseStructuredOutput && (
                  <div className='flex items-center gap-2 text-xs text-muted-foreground'>
                    <div className='w-2 h-2 bg-blue-500 rounded-full'></div>
                    <span>Structured Output</span>
                  </div>
                )}
              </div>
            )}
          </PopoverContent>
        </Popover>
      )}
    </div>
  );
}
