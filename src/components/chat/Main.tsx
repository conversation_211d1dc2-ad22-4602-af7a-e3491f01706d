'use client';
import { useState, useContext, useEffect } from 'react';
import { ConversationState } from '@/lib/supabase/types';
import { AppContext } from '@/providers/AppProvider';
import { useBuilderStore } from '@/stores/builderStore';
import AppBuilder from '../builder/AppBuilder';
import SplitPaneWrapper from './SplitPaneWrapper';
import { X } from 'lucide-react';
import './split-pane.css';
import ChatArea from './ChatArea';
import ChatHeader from './ChatHeader';
import { useWorkspaceInfo } from '@/hooks/useWorkspaceInfo';

export type MaybeConversationState = ConversationState | null;

interface MainProps {
  groupConversationId?: string;
  initialPrompt?: string;
  initialDefaultModel?: string;
}

export default function Main({
  groupConversationId: propGroupId,
  initialPrompt,
  initialDefaultModel,
}: MainProps) {
  const { viewportHeight, isMobile } = useContext(AppContext);
  const isBuilderOpen = useBuilderStore((state) => state.isBuilderOpen);
  const { workspace } = useWorkspaceInfo(propGroupId);

  const [isTemporary, setIsTemporary] = useState<boolean>(false);

  // cleanup the url after getting the initial prompt and default model
  useEffect(() => {
    if (initialPrompt || initialDefaultModel) {
      const url = new URL(window.location.href);
      url.searchParams.delete('prompt');
      url.searchParams.delete('defaultModel');
      window.history.replaceState({}, '', url.toString());
    }
  }, [initialPrompt, initialDefaultModel]);

  // Add state to remember the last split size
  const [splitSize, setSplitSize] = useState<number | string>('70%');

  const chatAreaProps = {
    groupConversationId: propGroupId,
    isTemporary,
    setIsTemporary,
    selectedWorkspaceInfo: workspace,
    initialPrompt,
    initialDefaultModel,
  };

  return (
    <div
      className='flex-1 flex flex-col bg-chat-bg text-foreground overflow-hidden relative'
      style={{ height: isMobile ? `${viewportHeight}px` : '100svh' }}
    >
      {/* Header */}
      <ChatHeader
        isTemporary={isTemporary}
        conversationId={propGroupId || ''}
        workspace={workspace}
      />

      {/* Main Content */}
      <div className='flex-1 overflow-hidden'>
        {isBuilderOpen && !isMobile ? (
          <SplitPaneWrapper
            defaultSize={splitSize}
            onChange={(size) => setSplitSize(size)}
            leftPane={<ChatArea {...chatAreaProps} chatState='chat' />}
            rightPane={
              <div className='h-full w-full'>
                <AppBuilder />
              </div>
            }
          />
        ) : (
          <>
            {/* Chat Area (without split pane on mobile or when builder is closed) */}
            <ChatArea {...chatAreaProps} chatState='chat' />

            {/* Show App Builder as an overlay on mobile */}
            {isBuilderOpen && isMobile && (
              <div className='fixed inset-0 z-50 bg-background flex flex-col'>
                <div className='flex items-center justify-between p-3 border-b'>
                  <h2 className='text-base font-medium'>App Builder</h2>
                  <button
                    className='rounded-md p-1.5 hover:bg-muted'
                    onClick={() => useBuilderStore.getState().toggleBuilder()}
                    aria-label='Close'
                  >
                    <X className='h-5 w-5' />
                  </button>
                </div>
                <div className='flex-1 overflow-hidden'>
                  <AppBuilder />
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
