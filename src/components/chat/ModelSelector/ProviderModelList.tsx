import React, { useMemo } from 'react';
import { Check, LockIcon } from 'lucide-react';
import { CommandGroup, CommandItem } from '@/components/ui/command';
import { Model as DbModel, ModelTier, Provider } from '@/lib/supabase/types';
import {
  groupModelsByTier,
  getTierDisplayName,
  getTierPlanRequirement,
} from '@/lib/modelHelpers';
import { ChatSession } from '@/providers/ChatProvider';

type ProviderModelListProps = {
  providers: Provider[];
  chatSession: ChatSession;
  checkModelVisibility: (model: DbModel) => boolean;
  canAccessModelTier: (tier: ModelTier) => boolean;
  onModelSelect: (model: DbModel) => void;
  getModelsForProvider: (providerId: string) => DbModel[];
  currentProviderFilter: string;
};

export const ProviderModelList: React.FC<ProviderModelListProps> = ({
  providers,
  chatSession,
  checkModelVisibility,
  canAccessModelTier,
  onModelSelect,
  getModelsForProvider,
  currentProviderFilter,
}) => {
  // Memoize providers rendering to avoid unnecessary recalculations
  const providerElements = useMemo(() => {
    const filteredProviders =
      currentProviderFilter === 'all'
        ? providers
        : providers.filter((p) => p.id === currentProviderFilter);

    return filteredProviders
      .filter((p) => p.models.some((m) => checkModelVisibility(m)))
      .map((p) => {
        const providerModels = getModelsForProvider(p.id);
        const filteredProviderModels = providerModels.filter((m) =>
          checkModelVisibility(m)
        );
        const groupedModelsByTier = groupModelsByTier(filteredProviderModels);
        const visibleTiers = Object.entries(groupedModelsByTier).filter(
          (entry) => entry[1].length > 0
        );

        if (visibleTiers.length === 0) return null;

        return (
          <CommandGroup
            key={p.id}
            heading={
              <div className='flex items-center justify-between w-full'>
                <span>{p.display_name}</span>
              </div>
            }
            className='[&_[cmdk-group-heading]]:font-semibold [&_[cmdk-group-heading]]:text-foreground'
          >
            {visibleTiers.map((entry) => {
              const [tier, tierModels] = entry;
              const tierName = tier as ModelTier;
              const isTierAccessible = canAccessModelTier(tierName);
              const planRequirement = getTierPlanRequirement(tierName);

              return (
                <React.Fragment key={`${p.id}-${tier}`}>
                  {visibleTiers.length > 1 && (
                    <div className='px-2 py-0.5 text-[11px] text-muted-foreground/80 ml-2'>
                      {getTierDisplayName(tierName)}
                      {!isTierAccessible && planRequirement && (
                        <span className='ml-2 text-xs text-amber-500'>
                          ({planRequirement})
                        </span>
                      )}
                    </div>
                  )}
                  {tierModels
                    .sort((a, b) => (a.priority || 0) - (b.priority || 0))
                    .map((modelInfo) => (
                      <CommandItem
                        key={modelInfo.id}
                        value={`${p.id} - ${modelInfo.id} - ${p.display_name} - ${modelInfo.display_name}`}
                        onSelect={() => onModelSelect(modelInfo)}
                        disabled={!isTierAccessible}
                        className='flex items-center justify-between w-full ml-2 cursor-pointer'
                      >
                        <div className='flex items-center'>
                          <Check
                            className={`mr-2 h-4 w-4 ${
                              chatSession.model?.id === modelInfo.id
                                ? 'opacity-100'
                                : 'opacity-0'
                            }`}
                          />
                          {modelInfo.display_name}
                          {visibleTiers.length === 1 &&
                            !isTierAccessible &&
                            planRequirement && (
                              <span className='ml-2 text-xs text-amber-500'>
                                ({planRequirement})
                              </span>
                            )}
                          {visibleTiers.length === 1 &&
                            isTierAccessible &&
                            getTierDisplayName(tierName) !== 'Free Models' && (
                              <span className='ml-2 text-xs text-muted-foreground'>
                                ({getTierDisplayName(tierName)})
                              </span>
                            )}
                        </div>
                        {!isTierAccessible && (
                          <LockIcon className='ml-2 h-3 w-3 text-amber-500 flex-shrink-0' />
                        )}
                      </CommandItem>
                    ))}
                </React.Fragment>
              );
            })}
          </CommandGroup>
        );
      });
  }, [
    providers,
    chatSession,
    checkModelVisibility,
    canAccessModelTier,
    getModelsForProvider,
    onModelSelect,
    currentProviderFilter,
  ]);

  return <>{providerElements}</>;
};
