'use client';

import { useState, useCallback, useMemo, useContext, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { ChevronDown, X, LockIcon } from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandList,
} from '@/components/ui/command';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectGroup,
  SelectLabel,
} from '@/components/ui/select';
import { Model as DbModel, Provider, ModelTier } from '@/lib/supabase/types';
import { ChatContext } from '@/providers/ChatProvider';
import { UserContext } from '@/providers/AuthProvider';
import { useSubscription } from '@/providers/SubscriptionProvider';
import { isModelVisible } from '@/lib/modelVisibility';
import { ProviderFilterDropdown } from './ProviderFilterDropdown';
import { ProviderModelList } from './ProviderModelList';
import { ModelSelectionService } from '@/services/modelSelection';

export interface ModelSelectorProps {
  /** Selected model ID */
  value?: string | null;
  /** Callback when model selection changes */
  onChange: (modelId: string | null) => void;
  /** Variant of the selector */
  variant?: 'popover' | 'select' | 'inline';
  /** Button variant for popover mode */
  buttonVariant?: 'default' | 'outline' | 'ghost';
  /** Button size */
  size?: 'sm' | 'default' | 'lg';
  /** Whether the selector is disabled */
  disabled?: boolean;
  /** Placeholder text */
  placeholder?: string;
  /** Whether to show provider selection (only for select variant) */
  showProvider?: boolean;
  /** Custom providers list (if not provided, uses context) */
  providers?: Provider[];
  /** Whether to show remove button (for comparison mode) */
  showRemove?: boolean;
  /** Callback for remove action */
  onRemove?: () => void;
  /** Custom styling for popover content */
  popoverModal?: boolean;
  /** Custom className for the trigger */
  className?: string;
  /** Whether to group models by tier */
  groupByTier?: boolean;
}

export function ModelSelector({
  value,
  onChange,
  variant = 'popover',
  buttonVariant = 'outline',
  size = 'default',
  disabled = false,
  placeholder = 'Select model...',
  showProvider = false,
  providers: customProviders,
  showRemove = false,
  onRemove,
  popoverModal = true,
  className = '',
  groupByTier = true,
}: ModelSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [providerFilter, setProviderFilter] = useState('all');
  const [selectedProviderId, setSelectedProviderId] = useState<string>('');
  const searchInputRef = useRef<HTMLInputElement>(null);

  const { providers: contextProviders } = useContext(ChatContext);
  const { me } = useContext(UserContext);
  const { canAccessModelTier } = useSubscription();

  const providers = customProviders || contextProviders;

  // Memoize the model visibility check function
  const checkModelVisibility = useMemo(() => {
    return (model: DbModel) => {
      return isModelVisible(
        model.id,
        model.is_visible_by_default ?? true,
        me?.preferences
      );
    };
  }, [me?.preferences]);

  // Get the selected model details
  const selectedModel = useMemo(() => {
    if (!value) return null;

    for (const provider of providers) {
      const model = provider.models.find((m) => m.id === value);
      if (model) return { model, provider };
    }
    return null;
  }, [value, providers]);

  // Helper to get available models for a provider
  const getModelsForProvider = useCallback(
    (providerId: string) => {
      const provider = providers.find((p) => p.id === providerId);
      return provider?.models || [];
    },
    [providers]
  );

  // Group models by tier
  const groupModelsByTier = useCallback((models: DbModel[]) => {
    const grouped: Record<ModelTier, DbModel[]> = {
      free: [],
      starter: [],
      premium: [],
    };

    models.forEach((model) => {
      const tier = model.tier as ModelTier | undefined;
      if (
        tier &&
        (tier === 'free' || tier === 'starter' || tier === 'premium')
      ) {
        grouped[tier].push(model);
      } else {
        grouped.free.push(model);
      }
    });

    return grouped;
  }, []);

  // Get tier display name
  const getTierDisplayName = (tier: ModelTier) => {
    switch (tier) {
      case 'free':
        return 'Free Models';
      case 'starter':
        return 'Starter Models';
      case 'premium':
        return 'Premium Models';
      default:
        return 'Models';
    }
  };

  // Get tier plan requirement
  const getTierPlanRequirement = (tier: ModelTier) => {
    switch (tier) {
      case 'free':
        return null;
      case 'starter':
        return 'Requires starter plan';
      case 'premium':
        return 'Requires premium plan';
      default:
        return null;
    }
  };

  const handleModelSelection = useCallback(
    (model: DbModel) => {
      onChange(model.id);
      // Track this as an explicit user selection
      ModelSelectionService.updateLastUsedModel(model.id);
      setIsOpen(false);
    },
    [onChange]
  );

  const handleProviderFilterChange = useCallback((filter: string) => {
    setProviderFilter(filter);
  }, []);

  const handleProviderChange = useCallback(
    (providerId: string) => {
      setSelectedProviderId(providerId);
      // Reset model selection when provider changes in select mode
      if (variant === 'select') {
        onChange(null);
      }
    },
    [onChange, variant]
  );

  const handleModelChange = useCallback(
    (modelId: string) => {
      onChange(modelId);
      // Track this as an explicit user selection
      if (modelId) {
        ModelSelectionService.updateLastUsedModel(modelId);
      }
    },
    [onChange]
  );

  // Create a dummy chat session for the ProviderModelList component
  const dummyChatSession = useMemo(() => {
    const modelToUse = selectedModel?.model || providers[0]?.models[0] || null;

    return {
      id: 'selector',
      model: modelToUse!,
      conversationId: '',
      conversationState: 'healthy' as const,
      parentMessageNode: null,
    };
  }, [selectedModel, providers]);

  // Common popover content styles
  const popoverContentStyle = {
    backgroundColor: 'var(--color-input-bg)',
    color: 'var(--color-input-fg)',
    backdropFilter: 'none',
    WebkitBackdropFilter: 'none',
    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
    border: '1px solid var(--color-border)',
  };

  if (variant === 'select') {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        {showProvider && (
          <Select
            value={selectedProviderId}
            onValueChange={handleProviderChange}
            disabled={disabled}
          >
            <SelectTrigger className='w-36 bg-input border-border'>
              <SelectValue placeholder='Select provider' />
            </SelectTrigger>
            <SelectContent>
              {providers.map((provider) => (
                <SelectItem key={provider.id} value={provider.id}>
                  {provider.display_name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}

        <Select
          value={value || ''}
          onValueChange={handleModelChange}
          disabled={disabled}
        >
          <SelectTrigger className='flex-1 bg-input border-border'>
            <SelectValue placeholder={placeholder} />
          </SelectTrigger>
          <SelectContent>
            {(showProvider && selectedProviderId
              ? [selectedProviderId]
              : providers.map((p) => p.id)
            ).map((providerId) => {
              const models = getModelsForProvider(providerId);
              const filteredModels = models.filter((model) =>
                checkModelVisibility(model)
              );

              if (!groupByTier) {
                return filteredModels
                  .sort((a, b) => (a.priority || 0) - (b.priority || 0))
                  .map((model) => {
                    const isAccessible = canAccessModelTier(
                      model.tier as ModelTier
                    );
                    return (
                      <SelectItem
                        key={model.id}
                        value={model.id}
                        disabled={!isAccessible}
                      >
                        <div className='flex items-center'>
                          {model.display_name}
                          {!isAccessible && (
                            <LockIcon className='ml-2 h-3 w-3 text-amber-500' />
                          )}
                        </div>
                      </SelectItem>
                    );
                  });
              }

              const groupedModels = groupModelsByTier(filteredModels);
              return Object.entries(groupedModels).map(([tier, models]) => {
                if (models.length === 0) return null;

                const tierName = tier as ModelTier;
                const isAccessible = canAccessModelTier(tierName);
                const planRequirement = getTierPlanRequirement(tierName);

                return (
                  <SelectGroup key={`${providerId}-${tier}`}>
                    <SelectLabel>
                      {getTierDisplayName(tierName)}
                      {!isAccessible && planRequirement && (
                        <span className='ml-2 text-xs text-amber-500'>
                          ({planRequirement})
                        </span>
                      )}
                    </SelectLabel>
                    {models
                      .sort((a, b) => (a.priority || 0) - (b.priority || 0))
                      .map((model) => (
                        <SelectItem
                          key={model.id}
                          value={model.id}
                          disabled={!isAccessible}
                        >
                          <div className='flex items-center'>
                            {model.display_name}
                            {!isAccessible && (
                              <LockIcon className='ml-2 h-3 w-3 text-amber-500' />
                            )}
                          </div>
                        </SelectItem>
                      ))}
                  </SelectGroup>
                );
              });
            })}
          </SelectContent>
        </Select>

        {showRemove && onRemove && (
          <Button
            variant='ghost'
            size='icon'
            onClick={onRemove}
            className='h-8 w-8 hover:bg-gray-100 hover:text-red-500 transition-colors'
            disabled={disabled}
          >
            <X size={16} />
          </Button>
        )}
      </div>
    );
  }

  if (variant === 'inline') {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <span className='text-sm font-medium'>
          {selectedModel?.model.display_name || placeholder}
        </span>
        {showRemove && onRemove && (
          <Button
            variant='ghost'
            size='icon'
            onClick={onRemove}
            className='h-6 w-6 hover:bg-gray-100 hover:text-red-500 transition-colors'
            disabled={disabled}
          >
            <X size={14} />
          </Button>
        )}
      </div>
    );
  }

  // Default popover variant
  return (
    <div className={`flex items-center ${className}`}>
      <Popover open={isOpen} onOpenChange={setIsOpen} modal={popoverModal}>
        <PopoverTrigger asChild>
          <Button
            variant={buttonVariant}
            role='combobox'
            aria-expanded={isOpen}
            size={size}
            className='justify-between'
            disabled={disabled}
            style={
              buttonVariant === 'outline'
                ? {
                    backgroundColor: 'var(--color-input-bg)',
                    color: 'var(--color-input-fg)',
                    border: '1px solid var(--color-input-border)',
                  }
                : undefined
            }
          >
            {selectedModel ? selectedModel.model.display_name : placeholder}
            <ChevronDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className='w-[300px] p-0'
          style={popoverContentStyle}
          align='start'
          onOpenAutoFocus={(e) => {
            e.preventDefault();
            searchInputRef.current?.focus();
          }}
        >
          <ProviderFilterDropdown
            providers={providers}
            currentFilter={providerFilter}
            onFilterChange={handleProviderFilterChange}
            checkModelVisibility={checkModelVisibility}
          />
          <Command>
            <CommandInput ref={searchInputRef} placeholder='Search model...' />
            <CommandList>
              <CommandEmpty>No model found.</CommandEmpty>
              <ProviderModelList
                providers={providers}
                chatSession={dummyChatSession}
                checkModelVisibility={checkModelVisibility}
                canAccessModelTier={canAccessModelTier}
                onModelSelect={handleModelSelection}
                getModelsForProvider={getModelsForProvider}
                currentProviderFilter={providerFilter}
              />
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {showRemove && onRemove && (
        <Button
          variant='ghost'
          size='icon'
          onClick={onRemove}
          className='h-8 w-8 hover:bg-gray-100 hover:text-red-500 transition-colors ml-2'
          disabled={disabled}
        >
          <X size={16} />
        </Button>
      )}
    </div>
  );
}
