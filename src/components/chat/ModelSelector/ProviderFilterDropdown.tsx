import React from 'react';
import { Button } from '@/components/ui/button';
import { ChevronDown, Check } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Model as DbModel } from '@/lib/supabase/types';

// Type definition matching application's actual types
type Provider = {
  id: string;
  display_name: string | null;
  models: DbModel[];
  [key: string]: unknown;
};

type ProviderFilterDropdownProps = {
  providers: Provider[];
  currentFilter: string;
  onFilterChange: (providerId: string) => void;
  checkModelVisibility: (model: DbModel) => boolean;
};

export const ProviderFilterDropdown: React.FC<ProviderFilterDropdownProps> = ({
  providers,
  currentFilter,
  onFilterChange,
  checkModelVisibility,
}) => {
  const visibleProviders = providers.filter((p) =>
    p.models.some((m) => checkModelVisibility(m))
  );

  return (
    <div className='p-2 border-b border-border flex items-center justify-between'>
      <p className='text-xs text-muted-foreground mr-2'>Filter by provider:</p>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant='outline'
            size='sm'
            className='h-7 px-2 text-xs w-[150px] justify-between'
          >
            {currentFilter === 'all'
              ? 'All Providers'
              : providers.find((p) => p.id === currentFilter)?.display_name ||
                'Select Provider'}
            <ChevronDown className='ml-1 h-3 w-3 opacity-70' />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          className='w-[200px]'
          style={{
            backgroundColor: 'var(--color-input-bg)',
            color: 'var(--color-input-fg)',
          }}
        >
          <DropdownMenuItem onSelect={() => onFilterChange('all')}>
            <Check
              className={`mr-2 h-4 w-4 ${
                currentFilter === 'all' ? 'opacity-100' : 'opacity-0'
              }`}
            />
            All Providers
          </DropdownMenuItem>
          {visibleProviders.map((p) => (
            <DropdownMenuItem key={p.id} onSelect={() => onFilterChange(p.id)}>
              <Check
                className={`mr-2 h-4 w-4 ${
                  currentFilter === p.id ? 'opacity-100' : 'opacity-0'
                }`}
              />
              {p.display_name}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};
