import React, { useState, useEffect, useRef } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { Button } from '../ui/button';

interface CollapsibleMessageProps {
  content: React.ReactNode;
  isLong: boolean;
  messageId: string;
  initiallyCollapsed?: boolean;
  jumpToMessage: (messageId: string, behavior: ScrollBehavior) => void;
}

// Store collapsed state for messages
const collapsedMessages = new Map<string, boolean>();

export function CollapsibleMessage({
  content,
  isLong,
  messageId,
  initiallyCollapsed = false,
  jumpToMessage,
}: CollapsibleMessageProps) {
  // Get initial state from memory or props
  const [isCollapsed, setIsCollapsed] = useState(
    collapsedMessages.has(messageId)
      ? collapsedMessages.get(messageId)
      : isLong && initiallyCollapsed
  );
  const contentRef = useRef<HTMLDivElement>(null);
  const [showToggle, setShowToggle] = useState(isLong);

  // Measure content height to determine if toggle is needed
  useEffect(() => {
    if (contentRef.current) {
      const height = contentRef.current.scrollHeight;
      setShowToggle(height > 300); // Show toggle if content is tall
    }
  }, [content]);

  // Store collapsed state when it changes
  useEffect(() => {
    if (showToggle) {
      collapsedMessages.set(messageId, isCollapsed ?? false);
    }
  }, [isCollapsed, messageId, showToggle]);

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
    if (!isCollapsed) {
      jumpToMessage(messageId, 'instant');
    }
  };

  return (
    <div className='w-full'>
      <div
        ref={contentRef}
        className={`overflow-hidden transition-all duration-200 ${
          isCollapsed ? 'max-h-[300px] !overflow-y-auto' : 'max-h-none'
        }`}
      >
        {content}
      </div>

      {showToggle && (
        <Button
          variant='ghost'
          size='sm'
          onClick={toggleCollapse}
          className='mt-1 text-xs flex items-center gap-1 text-muted-foreground hover:text-foreground'
        >
          {isCollapsed ? (
            <>
              <ChevronDown className='h-3 w-3' />
              Show more
            </>
          ) : (
            <>
              <ChevronUp className='h-3 w-3' />
              Show less
            </>
          )}
        </Button>
      )}
    </div>
  );
}
