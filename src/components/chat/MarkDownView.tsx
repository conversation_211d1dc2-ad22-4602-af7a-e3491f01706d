import { Copy } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import { <PERSON>rism as SyntaxHighlighter } from 'react-syntax-highlighter';
import remarkGfm from 'remark-gfm';
import { CustomTooltip } from '../ui/tooltip';
import { toast } from 'sonner';
import { useMemo, memo, useState } from 'react';
import { PrismTheme, useCodeTheme } from '@/providers/CodeThemeProvider';
import { MessageNode, UrlCitationAnnotation } from '@/lib/supabase/types';

interface MarkDownViewProps {
  message: MessageNode;
  annotations?: UrlCitationAnnotation[];
}

// Helper function to apply annotations to text
const applyAnnotations = (
  text: string,
  annotations?: UrlCitationAnnotation[]
): React.ReactNode[] => {
  if (!annotations || annotations.length === 0) {
    return [text]; // Return plain text if no annotations
  }

  // Sort annotations by start index to process them in order
  const sortedAnnotations = [...annotations].sort(
    (a, b) => a.start_index - b.start_index
  );

  const parts: React.ReactNode[] = [];
  let lastIndex = 0;

  sortedAnnotations.forEach((anno, i) => {
    // Ensure indices are within bounds and valid
    if (
      anno.start_index < lastIndex ||
      anno.end_index > text.length ||
      anno.start_index >= anno.end_index
    ) {
      return; // Skip invalid or overlapping annotations
    }

    // Add text part before the annotation
    if (anno.start_index > lastIndex) {
      parts.push(text.substring(lastIndex, anno.start_index));
    }

    // Add the annotated part as a link
    parts.push(
      <a
        key={`anno-${i}`}
        href={anno.url}
        target='_blank'
        rel='noopener noreferrer'
        title={anno.title || anno.url}
        className='annotation-link bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 px-1 py-0.5 rounded text-xs font-medium align-super mx-0.5'
      >
        [{i + 1}]
      </a>
    );

    lastIndex = anno.end_index;
  });

  // Add any remaining text after the last annotation
  if (lastIndex < text.length) {
    parts.push(text.substring(lastIndex));
  }

  return parts;
};

// Sanitize URLs
const urlTransform = (url: string): string => {
  // Basic URL sanitization
  try {
    const parsed = new URL(url);
    // Allow only http and https protocols
    if (parsed.protocol !== 'http:' && parsed.protocol !== 'https:') {
      return '#';
    }
    return url;
  } catch {
    // If not a valid URL, it might be a relative link or anchor
    if (url.startsWith('/') || url.startsWith('#')) {
      return url;
    }
    return '#';
  }
};

// Create a separate component for code blocks to properly manage state
const CodeBlock = ({
  children,
  className,
  currentTheme,
  ...props
}: React.HTMLAttributes<HTMLElement> & {
  currentTheme: PrismTheme;
}) => {
  const match = /language-(\w+)/.exec(className || '');
  const content = String(children).replace(/\n$/, '');
  const [isHovered, setIsHovered] = useState(false);

  return match ? (
    <div
      className='relative rounded-md overflow-hidden border border-gray-200 dark:border-gray-700'
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <SyntaxHighlighter
        language={match[1]}
        style={currentTheme}
        className='!mt-0'
        wrapLines={true}
        showLineNumbers={true}
      >
        {content}
      </SyntaxHighlighter>
      <CopyButton text={content} isHovered={isHovered} />
    </div>
  ) : (
    <code
      className='bg-gray-100 dark:bg-gray-800 rounded px-1.5 py-0.5 font-mono text-sm'
      {...props}
    >
      {content}
    </code>
  );
};

function MarkDownView({ message, annotations }: MarkDownViewProps) {
  const { currentTheme } = useCodeTheme();

  const processedContent = useMemo(() => {
    return applyAnnotations(message.content, annotations);
  }, [message.content, annotations]);

  // Define component overrides for ReactMarkdown
  const components = useMemo(
    () => ({
      // Code blocks - now using the separate component
      code: (props: React.HTMLAttributes<HTMLElement>) => (
        <CodeBlock {...props} currentTheme={currentTheme} />
      ),
      // Preformatted text
      pre: ({ children, ...props }: React.HTMLAttributes<HTMLPreElement>) => (
        <pre
          className='whitespace-pre-wrap my-3 mx-0 p-0 bg-transparent'
          {...props}
        >
          {children}
        </pre>
      ),
      // Paragraphs with annotation handling
      p: ({
        children,
        ...props
      }: React.HTMLAttributes<HTMLParagraphElement>) => {
        // Check if children is just the raw message content string
        if (typeof children === 'string' && children === message.content) {
          return (
            <p
              className='whitespace-pre-line mb-4 last:mb-0 leading-relaxed'
              {...props}
            >
              {processedContent}
            </p>
          );
        }
        // Fallback for paragraphs that don't match or have complex children
        return (
          <p
            className='whitespace-pre-line mb-4 last:mb-0 leading-relaxed'
            {...props}
          >
            {children}
          </p>
        );
      },
      // Headers
      h1: ({
        children,
        ...props
      }: React.HTMLAttributes<HTMLHeadingElement>) => (
        <h1
          className='text-2xl font-bold mb-4 mt-6 pb-1 border-b border-gray-200 dark:border-gray-700'
          {...props}
        >
          {children}
        </h1>
      ),
      h2: ({
        children,
        ...props
      }: React.HTMLAttributes<HTMLHeadingElement>) => (
        <h2
          className='text-xl font-bold mb-3 mt-5 pb-1 border-b border-gray-200 dark:border-gray-700'
          {...props}
        >
          {children}
        </h2>
      ),
      h3: ({
        children,
        ...props
      }: React.HTMLAttributes<HTMLHeadingElement>) => (
        <h3 className='text-lg font-bold mb-2 mt-4' {...props}>
          {children}
        </h3>
      ),
      h4: ({
        children,
        ...props
      }: React.HTMLAttributes<HTMLHeadingElement>) => (
        <h4 className='text-base font-bold mb-2 mt-3' {...props}>
          {children}
        </h4>
      ),
      // Lists
      ul: ({ children, ...props }: React.HTMLAttributes<HTMLUListElement>) => (
        <ul className='list-disc ml-6 mb-4 space-y-1.5' {...props}>
          {children}
        </ul>
      ),
      ol: ({ children, ...props }: React.HTMLAttributes<HTMLOListElement>) => (
        <ol className='list-decimal ml-6 mb-4 space-y-1.5' {...props}>
          {children}
        </ol>
      ),
      // List items with support for task lists
      li: ({
        children,
        className,
        ...props
      }: React.LiHTMLAttributes<HTMLLIElement>) => {
        const isTaskItem = className?.includes('task-list-item');
        return (
          <li className={`${isTaskItem ? 'flex items-start' : ''}`} {...props}>
            {children}
          </li>
        );
      },
      // Checkbox for task lists
      input: ({
        type,
        checked,
        ...props
      }: React.InputHTMLAttributes<HTMLInputElement>) => {
        if (type === 'checkbox') {
          return (
            <input
              type='checkbox'
              readOnly
              checked={checked}
              className='mt-1 mr-2 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500'
              {...props}
            />
          );
        }
        return <input type={type} {...props} />;
      },
      // Blockquotes
      blockquote: ({
        children,
        ...props
      }: React.BlockquoteHTMLAttributes<HTMLQuoteElement>) => (
        <blockquote
          className='border-l-4 border-gray-300 dark:border-gray-600 pl-4 py-1 mb-4 italic text-gray-700 dark:text-gray-300'
          {...props}
        >
          {children}
        </blockquote>
      ),
      // Links
      a: ({
        children,
        href = '#',
        className,
        ...props
      }: React.AnchorHTMLAttributes<HTMLAnchorElement>) => {
        // Check if it's our annotation link (added by our component renderer)
        if (className?.includes('annotation-link')) {
          return (
            <a href={href} className={className} {...props}>
              {children}
            </a>
          );
        }
        // Style normal markdown links with URL sanitization
        const safeHref = urlTransform(href);
        return (
          <a
            href={safeHref}
            target='_blank'
            rel='noopener noreferrer'
            className='text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline underline-offset-2'
            {...props}
          >
            {children}
          </a>
        );
      },
      // Images
      img: ({
        src = '',
        alt = '',
        ...props
      }: React.ImgHTMLAttributes<HTMLImageElement>) => {
        const safeSrc = typeof src === 'string' ? urlTransform(src) : '#';
        return (
          <img
            src={safeSrc}
            alt={alt}
            className='max-w-full h-auto rounded-md my-2'
            loading='lazy'
            {...props}
          />
        );
      },
      // Horizontal rule
      hr: () => (
        <hr className='border-t border-gray-300 dark:border-gray-600 my-6' />
      ),
      // Strong/Bold
      strong: ({ children, ...props }: React.HTMLAttributes<HTMLElement>) => (
        <strong className='font-bold' {...props}>
          {children}
        </strong>
      ),
      // Emphasis/Italic
      em: ({ children, ...props }: React.HTMLAttributes<HTMLElement>) => (
        <em className='italic' {...props}>
          {children}
        </em>
      ),
      // Table components
      table: ({
        children,
        ...props
      }: React.TableHTMLAttributes<HTMLTableElement>) => (
        <div className='overflow-x-auto my-4'>
          <table
            className='min-w-full divide-y divide-gray-200 dark:divide-gray-700 border border-gray-200 dark:border-gray-700 rounded-md'
            {...props}
          >
            {children}
          </table>
        </div>
      ),
      thead: ({
        children,
        ...props
      }: React.HTMLAttributes<HTMLTableSectionElement>) => (
        <thead className='bg-gray-50 dark:bg-gray-800' {...props}>
          {children}
        </thead>
      ),
      tbody: ({
        children,
        ...props
      }: React.HTMLAttributes<HTMLTableSectionElement>) => (
        <tbody
          className='divide-y divide-gray-200 dark:divide-gray-700'
          {...props}
        >
          {children}
        </tbody>
      ),
      tr: ({
        children,
        ...props
      }: React.HTMLAttributes<HTMLTableRowElement>) => (
        <tr {...props}>{children}</tr>
      ),
      th: ({
        children,
        ...props
      }: React.ThHTMLAttributes<HTMLTableCellElement>) => (
        <th
          className='px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider'
          {...props}
        >
          {children}
        </th>
      ),
      td: ({
        children,
        ...props
      }: React.TdHTMLAttributes<HTMLTableCellElement>) => (
        <td
          className='px-4 py-2 text-sm text-gray-900 dark:text-gray-100'
          {...props}
        >
          {children}
        </td>
      ),
    }),
    [currentTheme, processedContent, message.content]
  );

  // Render ReactMarkdown with the component overrides
  return (
    <ReactMarkdown remarkPlugins={[remarkGfm]} components={components}>
      {message.content}
    </ReactMarkdown>
  );
}

export default memo(MarkDownView);

const CopyButton = ({
  text,
  isHovered,
}: {
  text: string;
  isHovered: boolean;
}) => {
  const copyCode = () => {
    navigator.clipboard.writeText(text);
    toast.success('Copied code to clipboard');
  };

  const button = (
    <button
      onClick={copyCode}
      className={`absolute top-2 right-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 bg-white/80 dark:bg-black/50 rounded p-1 transition-opacity duration-200 ${
        isHovered ? 'opacity-100' : 'opacity-0'
      }`}
      aria-label='Copy code'
    >
      <Copy className='h-4 w-4' />
    </button>
  );

  // Only wrap with tooltip when hovered
  return isHovered ? (
    <CustomTooltip tooltip='Copy code to clipboard'>{button}</CustomTooltip>
  ) : (
    button
  );
};
