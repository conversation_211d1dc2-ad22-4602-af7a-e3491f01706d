import { MessageNode } from '@/lib/supabase/types';

export function addMessageToTree(
  node: MessageNode | null,
  parentId: string | undefined,
  newMessage: MessageNode
): MessageNode | null {
  // Base case 1: If the tree is empty, the new message becomes the root.
  if (!node) {
    // If parentId was specified but the tree is empty, this might be an error state,
    // but typically adding the message as the root is the expected behavior.
    return newMessage;
  }

  // Base case 2: If the current node is the target parent
  if (node.id === parentId) {
    // Return a *new* node object with the newMessage added to children
    const newChildren = [...(node.children || []), newMessage];
    return { ...node, children: newChildren };
  }

  // Recursive step: Search in children
  if (node.children && node.children.length > 0) {
    let hasChanged = false;
    const newChildren = node.children.map((child) => {
      const updatedChild = addMessageToTree(child, parentId, newMessage);
      if (updatedChild !== child) {
        // If the recursive call returned a different object, it means
        // the newMessage was added somewhere down this branch.
        hasChanged = true;
      }
      return updatedChild;
    });

    // If a child branch was modified, return a *new* parent node
    // incorporating the updated children list.
    if (hasChanged) {
      // Filter out potential nulls just in case (though unlikely in this specific function)
      return {
        ...node,
        children: newChildren.filter(Boolean) as MessageNode[],
      };
    }
  }

  // If the parentId wasn't found in this node or any children,
  // return the original node reference (no change occurred here).
  // If parentId was undefined, and we reached here, it means the root wasn't the target,
  // so we traverse downwards. If parentId is not found anywhere, the original tree is returned.
  return node;
}

export function updateMessageInTree(
  node: MessageNode | null,
  targetId: string,
  updates: Partial<MessageNode>
): MessageNode | null {
  if (!node) return null;

  if (node.id === targetId) {
    // Determine the final children array
    let finalChildren: MessageNode[] | undefined | null;
    if ('children' in updates) {
      // If updates provide children, merge existing and new children
      finalChildren = [...(node.children || []), ...(updates.children || [])];
    } else {
      // Otherwise, keep the existing children
      finalChildren = node.children;
    }
    // Apply updates, ensuring children are correctly set (or kept as is)
    // Also ensure that finalChildren is treated as Message[] or null
    return {
      ...node,
      ...updates,
      children: (finalChildren?.filter(Boolean) as MessageNode[]) || null,
    };
  }

  if (node.children && node.children.length > 0) {
    let hasChanged = false;
    const newChildren = node.children.map((child) => {
      const updatedChild = updateMessageInTree(child, targetId, updates);
      if (updatedChild !== child) {
        hasChanged = true;
      }
      return updatedChild;
    });

    if (hasChanged) {
      return {
        ...node,
        children: newChildren.filter(Boolean) as MessageNode[],
      };
    }
  }

  return node;
}

export function removeMessageFromTree(
  root: MessageNode | null,
  targetId: string
): MessageNode | null {
  if (!root) return null;
  if (root.id === targetId) return null;

  return {
    ...root,
    children:
      (root.children
        ?.map((child) => removeMessageFromTree(child, targetId))
        .filter(Boolean) as MessageNode[]) || null,
  };
}
