'use client';
import { useContext } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  PlusCircle,
  Star,
  Clock,
  LayoutTemplate,
  AlignJustify,
  ExternalLink,
} from 'lucide-react';
import { ChatContext } from '@/providers/ChatProvider';
import { SidebarContext } from '@/providers/SidebarProvider';
import { useRouter } from 'next/navigation';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { AppContext } from '@/providers/AppProvider';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

import { useAnalytics } from '@/hooks/useAnalytics';
import { useBuilderStore } from '@/stores/builderStore';
import ModelSelector from './ChatModelSelector/ModelSelector';
import { isMobile } from 'react-device-detect';
import { WorkspaceInfo } from '@/hooks/useWorkspaceInfo';

const MAX_TITLE_LENGTH = 30;

const truncateTitle = (title: string) => {
  return title.length > MAX_TITLE_LENGTH
    ? title.slice(0, MAX_TITLE_LENGTH) + '...'
    : title;
};

export default function ChatHeader({
  isTemporary,
  conversationId,
  workspace,
}: {
  isTemporary: boolean;
  conversationId: string;
  workspace?: WorkspaceInfo | null;
}) {
  const { initializeNewChat, selectedConversation } = useContext(ChatContext);
  const { isTestMode, setIsTestMode } = useContext(AppContext);
  const { isBuilderOpen, toggleBuilder } = useBuilderStore();

  const router = useRouter();
  const analytics = useAnalytics();

  const { toggleSidebarState } = useContext(SidebarContext);

  const handleNewConversation = () => {
    initializeNewChat();
    router.push('/chat');
  };

  const handleWorkspaceClick = () => {
    if (workspace) {
      router.push(`/workspaces/${workspace.id}`);
    }
  };

  const currentTitle = truncateTitle(selectedConversation?.title || 'New Chat');

  const toggleFavorite = async () => {
    if (!conversationId || !selectedConversation) return;

    try {
      const response = await fetch(
        `/api/groupConversations/${conversationId}`,
        {
          method: 'PATCH',
        }
      );

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to toggle favorite status');
      }

      // Track favoriting from the header
      analytics.trackConversationFavorited(
        conversationId,
        !selectedConversation.is_favorite
      );

      // The UI will update on its own through context
    } catch (error) {
      console.error('Error toggling favorite:', error);
    }
  };

  return (
    <div className='flex items-center justify-between px-4 py-3 gap-2 border-b border-border'>
      <div className='flex items-center gap-2 flex-shrink-0'>
        <button
          className='text-btn-secondary-fg rounded-md p-2 z-10 hover:bg-btn-secondary-hover flex-shrink-0'
          onClick={toggleSidebarState}
          aria-label='Show sidebar'
        >
          <AlignJustify className='h-5 w-5' />
        </button>

        <div className='flex items-center gap-3'>
          <h2
            className='text-base font-medium text-foreground truncate hidden sm:block'
            title={selectedConversation?.title || 'Sabi Chat'}
          >
            {currentTitle}
          </h2>

          {/* Workspace Indicator */}
          {conversationId && workspace && !isMobile && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    onClick={handleWorkspaceClick}
                    className='flex items-center gap-2 px-3 py-1.5 rounded-full border border-primary/30 bg-primary/5 text-primary hover:bg-primary/10 transition-colors text-xs font-medium shadow-sm'
                  >
                    <div className='h-5 w-5 rounded-full bg-primary/20 flex items-center justify-center text-xs font-semibold flex-shrink-0'>
                      {workspace.icon || workspace.name.charAt(0).toUpperCase()}
                    </div>
                    <span className='hidden sm:inline truncate max-w-[120px]'>
                      {workspace.name}
                    </span>
                    <ExternalLink className='h-3 w-3 flex-shrink-0 ml-0.5' />
                  </button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Open {workspace.name} workspace</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>

        {selectedConversation && (
          <button
            className='text-foreground/70 hover:text-foreground transition-colors'
            onClick={toggleFavorite}
            aria-label={
              selectedConversation.is_favorite
                ? 'Remove from favorites'
                : 'Add to favorites'
            }
          >
            <Star
              className={`h-5 w-5 ${
                selectedConversation.is_favorite
                  ? 'fill-yellow-400 text-yellow-400'
                  : 'fill-none'
              }`}
            />
          </button>
        )}
      </div>

      <div className='flex-1 flex justify-center min-w-0'>
        <ModelSelector />
      </div>

      <div className='flex items-center gap-2 flex-shrink-0'>
        {process.env.NODE_ENV === 'development' && (
          <div className='flex items-center gap-2'>
            <Switch checked={isTestMode} onCheckedChange={setIsTestMode} />
            <Label className='hidden sm:inline'>Test Mode</Label>
          </div>
        )}
        {process.env.NODE_ENV === 'development' && (
          <div className='flex items-center gap-2'>
            <button
              className={`text-btn-secondary-fg rounded-md p-2 hover:bg-btn-secondary-hover ${
                isBuilderOpen ? 'bg-primary/10 text-primary' : ''
              }`}
              onClick={toggleBuilder}
              aria-label='Toggle App Builder'
              aria-expanded={isBuilderOpen}
            >
              <LayoutTemplate className='h-5 w-5' />
            </button>
          </div>
        )}
        {isTemporary && (
          <div className='flex items-center gap-1 text-xs px-2 py-0.5 bg-amber-100 text-amber-700 dark:bg-amber-900 dark:text-amber-300 rounded-full'>
            <Clock className='h-3 w-3' />
            <span>Temporary</span>
          </div>
        )}
        <Button
          variant='default'
          className='bg-primary hover:bg-primary/90 hover:bg-btn-primary-hover'
          onClick={handleNewConversation}
          aria-label='New Chat'
        >
          <PlusCircle className='h-4 w-4 mr-2' />
          New Chat
        </Button>
      </div>
    </div>
  );
}
