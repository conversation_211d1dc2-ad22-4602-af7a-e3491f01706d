/* Split Pane Styles */
.Resizer {
  background: var(--border);
  z-index: 10;
  box-sizing: border-box;
  background-clip: padding-box;
  opacity: 0.8;
  transition: all 0.2s ease;
  position: relative;
}

.Resizer.horizontal {
  height: 6px;
  margin: -3px 0;
  border-top: 3px solid transparent;
  border-bottom: 3px solid transparent;
  cursor: row-resize;
  width: 100%;
}

.Resizer.vertical {
  width: 6px;
  margin: 0 -3px;
  border-left: 3px solid transparent;
  border-right: 3px solid transparent;
  cursor: col-resize;
}

.Resizer:hover {
  background-color: var(--primary);
  opacity: 1;
}

.Resizer.vertical:hover::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 2px;
  height: 24px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 2px;
}

.Resizer.horizontal:hover::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 24px;
  height: 2px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 2px;
}

.Resizer.disabled {
  pointer-events: none;
}

.Pane {
  overflow: auto;
}

.Pane.Pane2 {
  box-shadow: -4px 0 16px -8px rgba(0, 0, 0, 0.15);
}