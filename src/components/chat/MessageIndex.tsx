import { MessageNode } from '@/lib/supabase/types';
import { <PERSON><PERSON>, Bo<PERSON> } from 'lucide-react';
import { ScrollArea } from '../ui/scroll-area';
import { Button } from '../ui/button';

interface MessageIndexProps {
  messages: MessageNode[];
  onJumpToMessage: (messageId: string) => void;
  currentVisibleMessageId?: string;
}

export function MessageIndex({
  messages,
  onJumpToMessage,
  currentVisibleMessageId,
}: MessageIndexProps) {
  // Filter out system messages and create message pairs
  const filteredMessages = messages.filter(
    (m) => m.role === 'user' || m.role === 'assistant'
  );

  // Generate summaries for each message
  const getSummary = (message: MessageNode) => {
    if (!message.content) return 'Empty message';

    // Get first 30 characters or first line
    const firstLine = message.content.split('\n')[0];
    return firstLine.length > 30
      ? firstLine.substring(0, 30) + '...'
      : firstLine;
  };

  return (
    <div className='w-full h-full border-l border-border bg-background flex flex-col'>
      <div className='p-3 border-b border-border'>
        <h3 className='font-medium'>Message Index</h3>
      </div>

      <ScrollArea className='flex-1'>
        <div className='p-2 space-y-1'>
          {filteredMessages.map((message) => (
            <Button
              key={message.id}
              variant={
                currentVisibleMessageId === message.id ? 'secondary' : 'ghost'
              }
              size='sm'
              className='w-full justify-start text-left h-auto py-2'
              onClick={() => onJumpToMessage(message.id)}
            >
              <div className='flex items-start gap-2'>
                <div className='mt-0.5'>
                  {message.role === 'user' ? (
                    <User className='h-3 w-3' />
                  ) : (
                    <Bot className='h-3 w-3' />
                  )}
                </div>
                <div className='flex-1 text-xs'>
                  <div className='font-medium'>
                    {message.role === 'user' ? 'You' : 'Assistant'}
                  </div>
                  <div className='text-muted-foreground'>
                    {getSummary(message)}
                  </div>
                </div>
              </div>
            </Button>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
}
