'use client';

import { useContext, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { UserContext } from '@/providers/AuthProvider';
import { toast } from 'sonner';

export default function ShareImportHandler() {
  const { isAuthenticated } = useContext(UserContext);
  const router = useRouter();
  const [isChecked, setIsChecked] = useState(false);

  useEffect(() => {
    // Only run once when authenticated
    if (isAuthenticated && !isChecked) {
      setIsChecked(true);
      checkForPendingShareImport();
    }
  }, [isAuthenticated, isChecked]);

  const checkForPendingShareImport = async () => {
    const pendingShareId = localStorage.getItem('pendingShareImport');

    // If we have a pending share import
    if (pendingShareId) {
      try {
        // Clear it immediately to prevent repeated processing
        localStorage.removeItem('pendingShareImport');

        // Call API to import the shared conversation
        const response = await fetch('/api/conversations/import-shared', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ shareId: pendingShareId }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to import conversation');
        }

        const data = await response.json();

        // Navigate to the chat page with the new conversation ID
        router.push(`/chat/${data.conversationId}`);
      } catch (error) {
        console.error('Error importing shared conversation:', error);
        toast.error('Failed to import shared conversation');
      }
    }
  };

  // make it show a loading spinner
  return null;
}