import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Copy } from 'lucide-react';
import { useState, useEffect, useContext } from 'react';
import { toast } from 'sonner';
import { ChatContext } from '@/providers/ChatProvider';
import { useAnalytics } from '@/hooks/useAnalytics';

interface ShareDialogProps {
  isOpen: boolean;
  onClose: () => void;
  messageId: string;
  conversationId: string;
}

// src/components/share-dialog.tsx - Update the component
export function ShareDialog({
  isOpen,
  onClose,
  messageId,
  conversationId,
}: ShareDialogProps) {
  const { selectedConversation } = useContext(ChatContext);
  const analytics = useAnalytics();
  const [isCreating, setIsCreating] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [shareUrl, setShareUrl] = useState('');
  const [shareTitle, setShareTitle] = useState(
    selectedConversation?.title || ''
  );
  const [error, setError] = useState('');
  const [existingShareId, setExistingShareId] = useState('');
  const [needsUpdate, setNeedsUpdate] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  // Initialize title from selected conversation
  useEffect(() => {
    if (selectedConversation && !shareTitle) {
      setShareTitle(selectedConversation.title || '');
    }
  }, [selectedConversation, shareTitle]);

  // Check for existing share when dialog opens
  useEffect(() => {
    if (isOpen && conversationId) {
      // Reset state and check for existing share
      setIsCreating(false);
      setIsLoading(true);
      setShareUrl('');
      setError('');
      setExistingShareId('');
      setNeedsUpdate(false);

      const checkExistingShare = async () => {
        try {
          const response = await fetch(
            `/api/shares?conversation_id=${conversationId}`
          );

          if (response.ok) {
            const data = await response.json();

            if (data.share_url) {
              // Existing share found - set it directly
              setShareUrl(`${window.location.origin}${data.share_url}`);
              setShareTitle(data.title || '');
              setExistingShareId(data.share_id);

              // Check if shared conversation needs an update
              // If the last shared message ID is different from the current message ID
              if (data.last_original_message_id !== messageId) {
                setNeedsUpdate(true);
              }
            }
          }
        } catch (err) {
          console.error('Error checking for existing share:', err);
          // Silently fail - user can still create a new share
        } finally {
          setIsLoading(false);
        }
      };

      checkExistingShare();
    }
  }, [isOpen, conversationId, messageId]);

  // Create a new share
  const createShare = async () => {
    try {
      setIsCreating(true);
      setError('');

      const response = await fetch('/api/shares', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          conversation_id: conversationId,
          message_id: messageId,
          title: shareTitle,
        }),
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to create share');
      }

      const { share_url, share_id } = await response.json();
      setShareUrl(`${window.location.origin}${share_url}`);
      setExistingShareId(share_id);
      setNeedsUpdate(false);

      // Track share link creation
      analytics.trackShareLinkCreated(conversationId, share_id);
    } catch (err) {
      console.error('Error creating share:', err);
      setError(err instanceof Error ? err.message : 'Failed to create share');
    } finally {
      setIsCreating(false);
    }
  };

  // Update existing share
  const updateShare = async () => {
    if (!existingShareId) return;

    try {
      setIsUpdating(true);
      setError('');

      const response = await fetch(`/api/shares/${existingShareId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message_id: messageId,
        }),
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to update share');
      }

      setNeedsUpdate(false);
      toast.success('Share updated successfully');
    } catch (err) {
      console.error('Error updating share:', err);
      setError(err instanceof Error ? err.message : 'Failed to update share');
    } finally {
      setIsUpdating(false);
    }
  };

  // Copy to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(shareUrl);
    toast.success('Share link copied to clipboard');
  };

  // Open share link
  const openShareLink = () => {
    window.open(shareUrl, '_blank');
  };

  // Delete share
  const deleteShare = async () => {
    try {
      setIsCreating(true);
      const shareId = existingShareId || shareUrl.split('/').pop();

      if (!shareId) {
        throw new Error('Invalid share URL');
      }

      const response = await fetch(`/api/shares/${shareId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to delete share');
      }

      toast.success('Share deleted successfully');
      onClose();
    } catch (err) {
      console.error('Error deleting share:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete share');
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <DialogTitle>Share conversation</DialogTitle>
          <DialogDescription>
            {shareUrl
              ? 'Anyone with the link can view the shared conversation.'
              : 'Generate a link to share this conversation path.'}
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className='py-12 flex justify-center items-center'>
            <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary'></div>
          </div>
        ) : !shareUrl ? (
          <div className='py-4 space-y-4'>
            <div className='space-y-2'>
              <Label htmlFor='share-title'>Title</Label>
              <Input
                id='share-title'
                placeholder='E.g., My Awesome Conversation'
                value={shareTitle}
                onChange={(e) => setShareTitle(e.target.value)}
                disabled={isCreating}
              />
            </div>
            {error && (
              <p className='text-sm text-red-600 dark:text-red-500'>{error}</p>
            )}
          </div>
        ) : (
          <div className='py-4 space-y-4'>
            <div className='space-y-2'>
              <Label htmlFor='share-link'>Share Link</Label>
              <div className='flex items-center space-x-2'>
                <div className='relative flex-1'>
                  <Input
                    id='share-link'
                    readOnly
                    value={shareUrl}
                    className='pr-10'
                  />
                  <Button
                    type='button'
                    size='icon'
                    variant='ghost'
                    onClick={copyToClipboard}
                    className='absolute right-1 top-1/2 h-7 w-7 -translate-y-1/2 text-muted-foreground hover:text-primary'
                    aria-label='Copy share link'
                  >
                    <Copy className='h-4 w-4' />
                  </Button>
                </div>
                <Button
                  type='button'
                  size='sm'
                  variant='outline'
                  onClick={openShareLink}
                  disabled={isCreating || isUpdating}
                >
                  Open Link
                </Button>
              </div>
            </div>

            {needsUpdate && (
              <div className='rounded-md bg-amber-50 dark:bg-amber-950/50 p-3 text-sm'>
                <p className='text-amber-800 dark:text-amber-200'>
                  This share doesn&apos;t include the latest messages.
                </p>
              </div>
            )}

            {error && (
              <p className='text-sm text-red-600 dark:text-red-500'>{error}</p>
            )}
          </div>
        )}

        {!isLoading && (
          <DialogFooter className='gap-2'>
            {shareUrl ? (
              <>
                <Button
                  type='button'
                  variant='destructive'
                  onClick={deleteShare}
                  disabled={isCreating || isUpdating}
                  className='mr-auto disabled:opacity-75 disabled:cursor-not-allowed'
                >
                  {isCreating ? (
                    <>
                      <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2'></div>
                      Deleting...
                    </>
                  ) : (
                    'Delete Share'
                  )}
                </Button>
                {needsUpdate && (
                  <Button
                    type='button'
                    variant='secondary'
                    onClick={updateShare}
                    disabled={isUpdating || isCreating}
                    className='disabled:opacity-75 disabled:cursor-not-allowed'
                  >
                    {isUpdating ? (
                      <>
                        <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2'></div>
                        Updating...
                      </>
                    ) : (
                      'Update Share'
                    )}
                  </Button>
                )}
                <Button
                  type='button'
                  variant='outline'
                  onClick={onClose}
                  disabled={isCreating || isUpdating}
                  className='disabled:opacity-75 disabled:cursor-not-allowed'
                >
                  Close
                </Button>
              </>
            ) : (
              <>
                <div className='flex-grow'></div>
                <Button
                  type='button'
                  variant='outline'
                  onClick={onClose}
                  disabled={isCreating}
                  className='disabled:opacity-75 disabled:cursor-not-allowed'
                >
                  Cancel
                </Button>
                <Button
                  type='button'
                  onClick={createShare}
                  disabled={isCreating || !shareTitle.trim()}
                  className='disabled:opacity-75 disabled:cursor-not-allowed'
                >
                  {isCreating ? (
                    <>
                      <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2'></div>
                      Creating...
                    </>
                  ) : (
                    'Create Link'
                  )}
                </Button>
              </>
            )}
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
}
