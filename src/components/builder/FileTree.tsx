'use client';
import { useBuilderStore } from '@/stores/builderStore';
import { File } from '@/stores/builderStore';
import { ChevronDown, ChevronRight, FileIcon, FolderIcon, FileTextIcon, ImageIcon, FileJson, FileCode } from 'lucide-react';
import { useState } from 'react';

type FileItemProps = {
  file: File;
  depth: number;
};

export default function FileTree() {
  const { tree } = useBuilderStore();

  // If tree is empty, show a message
  if (!tree.length) {
    return (
      <div className="p-4 text-center text-muted-foreground text-sm">
        No files available
      </div>
    );
  }

  return (
    <div className="p-2">
      {tree.map((file) => (
        <FileItem
          key={file.path}
          file={file}
          depth={0}
        />
      ))}
    </div>
  );
}

// Get the appropriate icon based on file extension
function getFileIcon(path: string) {
  const ext = path.split('.').pop()?.toLowerCase();

  if (!ext) return <FileIcon className="h-4 w-4 shrink-0" />;

  switch(ext) {
    case 'jsx':
    case 'tsx':
    case 'js':
    case 'ts':
      return <FileCode className="h-4 w-4 shrink-0 text-blue-500" />;
    case 'html':
      return <FileCode className="h-4 w-4 shrink-0 text-orange-500" />;
    case 'css':
      return <FileCode className="h-4 w-4 shrink-0 text-purple-500" />;
    case 'json':
      return <FileJson className="h-4 w-4 shrink-0 text-yellow-500" />;
    case 'md':
      return <FileTextIcon className="h-4 w-4 shrink-0 text-green-500" />;
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
    case 'svg':
      return <ImageIcon className="h-4 w-4 shrink-0 text-pink-500" />;
    default:
      return <FileIcon className="h-4 w-4 shrink-0" />;
  }
}

function FileItem({ file, depth }: FileItemProps) {
  const [isExpanded, setIsExpanded] = useState(true);
  const { selectFile, selectedFilePath, unsavedChanges } = useBuilderStore();

  const isSelected = selectedFilePath === file.path;
  const hasUnsavedChanges = unsavedChanges[file.path];

  const handleClick = () => {
    if (file.type === 'folder') {
      setIsExpanded(!isExpanded);
    } else {
      selectFile(file.path);
    }
  };

  return (
    <div>
      <button
        onClick={handleClick}
        className={`flex items-center w-full text-left px-2 py-1.5 rounded-md text-sm ${
          isSelected
            ? 'bg-primary/15 text-primary font-medium ring-1 ring-primary/20'
            : 'hover:bg-muted/70'
        } transition-all duration-150`}
        style={{ paddingLeft: `${depth * 12 + 8}px` }}
        aria-selected={isSelected}
        role={file.type === 'file' ? 'option' : 'treeitem'}
        aria-expanded={file.type === 'folder' ? isExpanded : undefined}
      >
        {/* Folder toggle or file icon */}
        {file.type === 'folder' ? (
          <span className="mr-1.5 text-muted-foreground">
            {isExpanded ? (
              <ChevronDown className="h-4 w-4 shrink-0" />
            ) : (
              <ChevronRight className="h-4 w-4 shrink-0" />
            )}
          </span>
        ) : (
          <span className="mr-1.5">
            {getFileIcon(file.path)}
          </span>
        )}

        {/* Folder icon for folders */}
        {file.type === 'folder' && (
          <FolderIcon className={`h-4 w-4 mr-1.5 shrink-0 ${isExpanded ? 'text-yellow-500' : 'text-muted-foreground'}`} />
        )}

        {/* File/folder name */}
        <span className="truncate">{file.name}</span>

        {/* Unsaved changes indicator */}
        {hasUnsavedChanges && (
          <span className="ml-1.5 text-primary text-xs">●</span>
        )}
      </button>

      {/* Render children for folders when expanded */}
      {file.type === 'folder' && isExpanded && file.children && file.children.length > 0 && (
        <div>
          {file.children.map((childFile: File) => (
            <FileItem
              key={childFile.path}
              file={childFile}
              depth={depth + 1}
            />
          ))}
        </div>
      )}
    </div>
  );
}
