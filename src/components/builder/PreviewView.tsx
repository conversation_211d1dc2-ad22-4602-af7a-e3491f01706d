'use client';
import { useBuilderStore } from '@/stores/builderStore';
import { useMemo } from 'react';
import { EyeOff, FileIcon, Globe, AlertTriangle } from 'lucide-react';

const MAX_PREVIEW_SIZE = 50 * 1024; // 50KB max size for preview

export default function PreviewView() {
  const { selectedFilePath, filesByPath } = useBuilderStore();

  const previewContent = useMemo(() => {
    if (!selectedFilePath) {
      return (
        <div className='flex flex-col items-center justify-center h-full text-muted-foreground gap-4'>
          <div className='w-16 h-16 rounded-full bg-muted flex items-center justify-center'>
            <Globe className='h-8 w-8 opacity-50' />
          </div>
          <div className='text-center'>
            <p>Select a file to preview</p>
            <p className='text-xs text-muted-foreground mt-2'>
              HTML files can be previewed in this panel
            </p>
          </div>
        </div>
      );
    }

    const file = filesByPath[selectedFilePath];
    if (!file) {
      return (
        <div className='flex flex-col items-center justify-center h-full text-muted-foreground gap-4'>
          <div className='w-16 h-16 rounded-full bg-muted flex items-center justify-center'>
            <FileIcon className='h-8 w-8 opacity-50' />
          </div>
          <div className='text-center'>
            <p>File not found</p>
            <p className='text-xs text-muted-foreground mt-2'>
              The selected file could not be loaded
            </p>
          </div>
        </div>
      );
    }

    const isHtml = selectedFilePath.toLowerCase().endsWith('.html');
    const contentSize = file.content.length;

    if (isHtml && contentSize <= MAX_PREVIEW_SIZE) {
      return (
        <div className='h-full w-full overflow-hidden bg-white rounded-md'>
          <div className='h-9 w-full bg-muted/30 border-b border-border flex items-center px-3'>
            <div className='w-3 h-3 rounded-full bg-red-500 opacity-70 mr-1.5'></div>
            <div className='w-3 h-3 rounded-full bg-yellow-500 opacity-70 mr-1.5'></div>
            <div className='w-3 h-3 rounded-full bg-green-500 opacity-70 mr-4'></div>
            <div className='text-xs text-muted-foreground truncate flex-1 text-center'>
              {selectedFilePath}
            </div>
          </div>
          <iframe
            srcDoc={file.content}
            className='w-full h-[calc(100%-36px)] border-0'
            title={`Preview of ${selectedFilePath}`}
            sandbox='allow-scripts'
          />
        </div>
      );
    }

    return (
      <div className='flex flex-col items-center justify-center h-full text-muted-foreground gap-4'>
        <div className='w-16 h-16 rounded-full bg-muted flex items-center justify-center'>
          {isHtml && contentSize > MAX_PREVIEW_SIZE ? (
            <AlertTriangle className='h-8 w-8 text-amber-500' />
          ) : (
            <EyeOff className='h-8 w-8 opacity-50' />
          )}
        </div>
        <div className='text-center'>
          {isHtml && contentSize > MAX_PREVIEW_SIZE ? (
            <>
              <p className='text-amber-500'>File too large to preview</p>
              <p className='text-xs text-muted-foreground mt-2'>
                Max size: {MAX_PREVIEW_SIZE / 1024}KB
              </p>
            </>
          ) : (
            <>
              <p>Preview not available for this file type</p>
              <p className='text-xs text-muted-foreground mt-2'>
                Only HTML files can be previewed
              </p>
            </>
          )}
        </div>
      </div>
    );
  }, [selectedFilePath, filesByPath]);

  return <div className='h-full p-4 bg-card'>{previewContent}</div>;
}
