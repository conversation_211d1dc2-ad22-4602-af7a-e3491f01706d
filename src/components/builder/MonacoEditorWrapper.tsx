'use client';
import { useBuilderStore } from '@/stores/builderStore';
import { useEffect, useRef } from 'react';
import dynamic from 'next/dynamic';
import { Spinner } from '@/components/ui/spinner';
import type { editor } from 'monaco-editor';
import { FileCode } from 'lucide-react';

// Define type for the Monaco Editor instance
type MonacoEditorType = editor.IStandaloneCodeEditor;

// Lazy-load Monaco Editor to reduce initial bundle size
const MonacoEditor = dynamic(() => import('@monaco-editor/react'), {
  loading: () => (
    <div className='h-full w-full flex flex-col items-center justify-center gap-3'>
      <Spinner className='h-8 w-8' />
      <span className='text-sm text-muted-foreground'>Loading editor...</span>
    </div>
  ),
  ssr: false,
});

// Map file extensions to language IDs
const extToLang: Record<string, string> = {
  '.js': 'javascript',
  '.jsx': 'javascript',
  '.ts': 'typescript',
  '.tsx': 'typescript',
  '.html': 'html',
  '.css': 'css',
  '.json': 'json',
  '.md': 'markdown',
  '.py': 'python',
  '.rb': 'ruby',
  '.php': 'php',
  '.java': 'java',
  '.go': 'go',
  '.rs': 'rust',
  '.c': 'c',
  '.cpp': 'cpp',
  '.cs': 'csharp',
};

export default function MonacoEditorWrapper() {
  const { selectedFilePath, filesByPath, setEditorViewState, editorViewState } =
    useBuilderStore();

  const editorRef = useRef<MonacoEditorType | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Determine language based on file extension
  const language = selectedFilePath
    ? getLanguageFromPath(selectedFilePath)
    : 'plaintext';

  // Get content from store
  const content =
    selectedFilePath && filesByPath[selectedFilePath]
      ? filesByPath[selectedFilePath].content
      : '';

  // Save editor view state when component unmounts or file changes
  useEffect(() => {
    return () => {
      if (editorRef.current && selectedFilePath) {
        const viewState = editorRef.current.saveViewState();
        setEditorViewState(selectedFilePath, viewState);
      }
    };
  }, [selectedFilePath, setEditorViewState]);

  // Setup resize observer to make editor responsive
  useEffect(() => {
    if (!containerRef.current || !editorRef.current) return;

    const resizeObserver = new ResizeObserver(() => {
      if (editorRef.current) {
        editorRef.current.layout();
      }
    });

    resizeObserver.observe(containerRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  // Handle editor mount
  const handleEditorDidMount = (editor: MonacoEditorType) => {
    editorRef.current = editor;

    // Restore view state if available
    if (selectedFilePath && editorViewState[selectedFilePath]) {
      editor.restoreViewState(editorViewState[selectedFilePath]);
      editor.focus();
    }
  };

  // Display the file name/extension in the header
  const getFileNameDisplay = () => {
    if (!selectedFilePath) return null;

    const fileName = selectedFilePath.split('/').pop() || '';

    return (
      <div className='h-9 bg-muted/30 border-b border-border flex items-center px-3 text-sm gap-2'>
        <FileCode className='h-4 w-4 text-primary' />
        <span className='font-medium'>{fileName}</span>
        <span className='text-xs px-1.5 py-0.5 rounded bg-muted/50 text-muted-foreground ml-auto'>
          {language}
        </span>
      </div>
    );
  };

  return (
    <div ref={containerRef} className='h-full w-full flex flex-col'>
      {/* File header */}
      {getFileNameDisplay()}

      {/* Editor */}
      <div className='flex-1'>
        <MonacoEditor
          height='100%'
          language={language}
          value={content}
          options={{
            minimap: { enabled: false },
            scrollBeyondLastLine: false,
            fontSize: 14,
            fontFamily: 'var(--font-geist-mono)',
            wordWrap: 'on',
            tabSize: 2,
            readOnly: true, // Read-only for Phase 1
            lineNumbers: 'on',
            renderLineHighlight: 'all',
            cursorBlinking: 'smooth',
            smoothScrolling: true,
            contextmenu: true,
            automaticLayout: true,
          }}
          onMount={handleEditorDidMount}
          theme='vs-dark'
        />
      </div>
    </div>
  );
}

// Helper to determine language from file path
function getLanguageFromPath(path: string): string {
  const extension = path.substring(path.lastIndexOf('.'));
  return extToLang[extension] || 'plaintext';
}
