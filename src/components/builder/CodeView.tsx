'use client';
import { useBuilderStore } from '@/stores/builderStore';
import { useState } from 'react';
import FileTree from './FileTree';
import MonacoEditorWrapper from './MonacoEditorWrapper';

export default function CodeView() {
  const [splitRatio] = useState(0.25); // 25% for file tree by default
  const { selectedFilePath } = useBuilderStore();

  // Track if we're currently resizing
  // const [isResizing, setIsResizing] = useState(false);

  // // Handle mouse events for resizing
  // useEffect(() => {
  //   if (!isResizing) return;

  //   const handleMouseMove = (e: MouseEvent) => {
  //     // Calculate new ratio based on mouse position
  //     const containerWidth =
  //       document.getElementById('code-view-container')?.clientWidth || 0;
  //     if (containerWidth === 0) return;

  //     const newRatio = Math.max(
  //       0.15,
  //       Math.min(0.5, e.clientX / containerWidth)
  //     );
  //     setSplitRatio(newRatio);
  //   };

  //   const handleMouseUp = () => {
  //     setIsResizing(false);
  //     document.body.classList.remove('select-none');
  //   };

  //   document.addEventListener('mousemove', handleMouseMove);
  //   document.addEventListener('mouseup', handleMouseUp);
  //   // Add class to prevent text selection while resizing
  //   document.body.classList.add('select-none');

  //   return () => {
  //     document.removeEventListener('mousemove', handleMouseMove);
  //     document.removeEventListener('mouseup', handleMouseUp);
  //     document.body.classList.remove('select-none');
  //   };
  // }, [isResizing]);

  return (
    <div id='code-view-container' className='flex h-full'>
      {/* File tree section */}
      <div
        className='border-r border-border bg-muted/20 overflow-auto'
        style={{ width: `${splitRatio * 100}%` }}
      >
        <FileTree />
      </div>

      {/* Resize handle */}
      {/* <div
        className='relative w-[6px] mx-[-3px] z-10 cursor-col-resize group hover:bg-primary transition-colors'
        onMouseDown={() => setIsResizing(true)}
      >
        <div className='absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity'>
          <div className='absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-1 h-8 bg-primary-foreground/70 rounded-full'></div>
        </div>
      </div> */}

      {/* Editor section */}
      <div
        className='flex-1 h-full overflow-hidden bg-card'
        style={{ width: `${(1 - splitRatio) * 100}%` }}
      >
        {selectedFilePath ? (
          <MonacoEditorWrapper />
        ) : (
          <div className='flex flex-col items-center justify-center h-full text-muted-foreground gap-4'>
            <div className='w-16 h-16 rounded-full bg-muted flex items-center justify-center'>
              <svg
                xmlns='http://www.w3.org/2000/svg'
                width='24'
                height='24'
                viewBox='0 0 24 24'
                fill='none'
                stroke='currentColor'
                strokeWidth='2'
                strokeLinecap='round'
                strokeLinejoin='round'
                className='opacity-50'
              >
                <path d='M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z'></path>
                <polyline points='14 2 14 8 20 8'></polyline>
              </svg>
            </div>
            <div className='text-center'>
              <p>Select a file from the sidebar to edit</p>
              <p className='text-xs text-muted-foreground mt-2'>
                Use the file explorer to navigate your project
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
