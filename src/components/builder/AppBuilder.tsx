'use client';
import { useBuilderStore } from '@/stores/builderStore';
import { useMemo } from 'react';
import CodeView from './CodeView';
import PreviewView from './PreviewView';
import { Code, Eye } from 'lucide-react';

export default function AppBuilder() {
  const { builderMode, setBuilderMode, selectedFilePath } = useBuilderStore();

  // Create breadcrumb from the selected file path
  const breadcrumb = useMemo(() => {
    if (!selectedFilePath) return null;

    const segments = selectedFilePath.split('/');
    const fileName = segments.pop() || '';

    return (
      <div className='flex items-center text-sm text-muted-foreground overflow-auto whitespace-nowrap p-3 border-b bg-muted/30'>
        {segments.length > 0 && (
          <>
            <span className='opacity-60'>{segments.join('/')}/</span>
            <span className='font-medium text-foreground ml-1'>{fileName}</span>
          </>
        )}
        {segments.length === 0 && (
          <span className='font-medium text-foreground'>{fileName}</span>
        )}
      </div>
    );
  }, [selectedFilePath]);

  return (
    <div className='flex flex-col h-full bg-background/95 backdrop-blur-sm'>
      {/* Header area */}
      <div className='border-b border-border p-3 flex items-center justify-between bg-muted/30 shadow-sm'>
        <h2 className='text-base font-medium flex items-center gap-2'>
          <span className='text-primary'>
            <Code className='h-5 w-5' />
          </span>
          App Builder
        </h2>

        {/* Mode toggle */}
        <div className='flex bg-background rounded-md p-1 shadow-sm'>
          <button
            className={`px-4 py-1.5 text-xs rounded-md transition-colors flex items-center gap-1.5 ${
              builderMode === 'code'
                ? 'bg-primary text-primary-foreground shadow-sm font-medium'
                : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
            }`}
            onClick={() => setBuilderMode('code')}
            aria-pressed={builderMode === 'code'}
          >
            <Code className='h-3.5 w-3.5' />
          </button>
          <button
            className={`px-4 py-1.5 text-xs rounded-md transition-colors flex items-center gap-1.5 ${
              builderMode === 'preview'
                ? 'bg-primary text-primary-foreground shadow-sm font-medium'
                : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
            }`}
            onClick={() => setBuilderMode('preview')}
            aria-pressed={builderMode === 'preview'}
          >
            <Eye className='h-3.5 w-3.5' />
          </button>
        </div>
      </div>

      {/* Breadcrumb */}
      {breadcrumb}

      {/* Content area */}
      <div className='flex-1 overflow-hidden relative'>
        {builderMode === 'code' ? <CodeView /> : <PreviewView />}
      </div>
    </div>
  );
}
