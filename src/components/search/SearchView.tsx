'use client';

import { useContext, useEffect, useState, ChangeEvent } from 'react';
import { SearchInput } from '@/components/search/SearchInput';
import { SearchResults } from '@/components/search/SearchResults';
import { GroupConversation } from '@/lib/supabase/types';
import { ChatContext } from '@/providers/ChatProvider';
import { useRouter } from 'next/navigation';
import { useDebounce } from '@/hooks/useDebounce';
import { X, Search, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';
import { useAnalytics } from '@/hooks/useAnalytics';
import { AnalyticsEvent } from '@/lib/analytics/service';

export function SearchView() {
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [searchResults, setSearchResults] = useState<GroupConversation[]>([]);
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [recentConversations, setRecentConversations] = useState<
    GroupConversation[]
  >([]);
  const debouncedQuery = useDebounce(searchQuery, 300);
  const analytics = useAnalytics();

  const { performSearch, fetchRecentConversations } = useContext(ChatContext);
  const router = useRouter();

  // Load recent conversations on initial render
  useEffect(() => {
    const loadRecentConversations = async () => {
      const recent = await fetchRecentConversations();
      setRecentConversations(recent);
    };

    loadRecentConversations();
  }, [fetchRecentConversations]);

  // Perform search when debounced query changes
  useEffect(() => {
    const search = async () => {
      if (!debouncedQuery) {
        setSearchResults([]);
        setIsSearching(false);
        return;
      }

      setIsSearching(true);
      try {
        const results = await performSearch(debouncedQuery);
        setSearchResults(results);

        // Track search usage, but don't send the actual query text
        // for privacy reasons
        analytics.trackEvent(AnalyticsEvent.SEARCH_USED, {
          resultCount: results.length,
          // Don't include the actual query
        });
      } catch (error) {
        console.error('Search error:', error);
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    };

    search();
  }, [debouncedQuery, performSearch, analytics]);

  const handleGoToConversation = (conversation: GroupConversation) => {
    router.push(`/chat/${conversation.id}`);
  };

  // Handle search input change
  const handleSearchChange = (e: ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  // Keyboard shortcut to focus search (⌘K)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        document.getElementById('search-input')?.focus();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  return (
    <motion.div
      className='flex flex-col h-full w-full bg-background'
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      <div className='flex-shrink-0 p-6 border-b border-border sticky top-0 bg-background/95 backdrop-blur-sm z-10 shadow-sm'>
        <div className='flex items-center justify-between mb-5'>
          <div className='flex items-center gap-2'>
            <Search className='h-5 w-5 text-primary' />
            <h1 className='text-2xl font-bold text-foreground'>
              Search Conversations
            </h1>
          </div>
          <Button
            variant='ghost'
            size='icon'
            onClick={() => router.back()}
            aria-label='Close search'
            className='rounded-full hover:bg-muted'
          >
            <X className='h-5 w-5' />
          </Button>
        </div>
        <SearchInput
          value={searchQuery}
          onChange={handleSearchChange}
          isSearching={isSearching}
          clearSearch={() => setSearchQuery('')}
        />

        {/* Search keyboard shortcut hint */}
        <div className='flex justify-end mt-2 text-xs text-muted-foreground'>
          <span className='flex items-center gap-1'>
            <kbd className='px-1.5 py-0.5 rounded border border-border bg-muted text-xs'>
              ⌘
            </kbd>
            <span>+</span>
            <kbd className='px-1.5 py-0.5 rounded border border-border bg-muted text-xs'>
              K
            </kbd>
            <span>to search</span>
          </span>
        </div>
      </div>

      <div className='flex-1 overflow-y-auto p-6'>
        {searchQuery ? (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2 }}
          >
            <SearchResults
              results={searchResults}
              query={searchQuery}
              isSearching={isSearching}
              onSelectConversation={handleGoToConversation}
            />
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2 }}
          >
            <div className='flex items-center gap-2 mb-4'>
              <Sparkles className='h-4 w-4 text-primary' />
              <h2 className='text-lg font-medium'>Recent Conversations</h2>
            </div>
            <SearchResults
              results={recentConversations}
              isSearching={false}
              onSelectConversation={handleGoToConversation}
            />
          </motion.div>
        )}
      </div>
    </motion.div>
  );
}
