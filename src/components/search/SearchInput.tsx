'use client';

import { ChangeEvent } from 'react';
import { Input } from '@/components/ui/input';
import { Search, X, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';

interface SearchInputProps {
  value: string;
  onChange: (e: ChangeEvent<HTMLInputElement>) => void;
  isSearching: boolean;
  clearSearch: () => void;
}

export function SearchInput({
  value,
  onChange,
  isSearching,
  clearSearch,
}: SearchInputProps) {
  return (
    <div className='relative'>
      <div className='absolute left-3.5 top-1/2 transform -translate-y-1/2 text-muted-foreground'>
        {isSearching ? (
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
          >
            <Loader2 className='h-4 w-4 text-primary' />
          </motion.div>
        ) : (
          <Search className='h-4 w-4' />
        )}
      </div>

      <Input
        id='search-input'
        type='text'
        placeholder='Search conversations by title or content...'
        value={value}
        onChange={onChange}
        className='pl-10 pr-16 py-6 h-auto text-base border-border focus-visible:ring-primary/20 focus-visible:ring-offset-0 shadow-sm bg-background hover:bg-background/80 transition-colors'
      />

      <div className='absolute right-3 top-1/2 transform -translate-y-1/2 flex gap-2 items-center'>
        {value && (
          <Button
            variant='ghost'
            size='icon'
            className='h-6 w-6 rounded-full hover:bg-muted'
            onClick={clearSearch}
            aria-label='Clear search'
          >
            <X className='h-3.5 w-3.5' />
          </Button>
        )}
      </div>
    </div>
  );
}
