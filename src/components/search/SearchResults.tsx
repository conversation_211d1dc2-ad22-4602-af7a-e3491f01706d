'use client';

import { GroupConversation } from '@/lib/supabase/types';
import {
  MessageSquare,
  Star,
  Loader2,
  Search<PERSON>,
  Clock,
  ArrowRight,
} from 'lucide-react';
import { format } from 'date-fns';
import { motion } from 'framer-motion';

interface SearchResultsProps {
  results: GroupConversation[];
  query?: string;
  isSearching: boolean;
  onSelectConversation: (conversation: GroupConversation) => void;
}

// Helper function to highlight matching text
function highlightText(text: string, query: string) {
  if (!query) return text;

  const regex = new RegExp(
    `(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`,
    'gi'
  );
  const parts = text.split(regex);

  return parts.map((part, i) =>
    regex.test(part) ? (
      <mark
        key={i}
        className='bg-yellow-200/50 dark:bg-yellow-400/30 rounded px-0.5 font-medium'
      >
        {part}
      </mark>
    ) : (
      part
    )
  );
}

export function SearchResults({
  results,
  query,
  isSearching,
  onSelectConversation,
}: SearchResultsProps) {
  if (isSearching) {
    return (
      <motion.div
        className='flex flex-col items-center justify-center py-16'
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        <motion.div
          animate={{
            rotate: 360,
            scale: [1, 1.1, 1],
          }}
          transition={{
            rotate: { duration: 1.5, repeat: Infinity, ease: 'linear' },
            scale: { duration: 2, repeat: Infinity },
          }}
        >
          <Loader2 className='h-10 w-10 text-primary/60 mb-4' />
        </motion.div>
        <p className='text-muted-foreground text-lg'>
          Searching conversations...
        </p>
      </motion.div>
    );
  }

  if (query && results.length === 0) {
    return (
      <motion.div
        className='flex flex-col items-center justify-center py-16 text-center'
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className='bg-muted/50 rounded-full p-5 mb-5'>
          <SearchX className='h-10 w-10 text-muted-foreground' />
        </div>
        <p className='text-muted-foreground text-lg mb-2'>
          No conversations found for &ldquo;{query}&rdquo;
        </p>
        <p className='text-muted-foreground text-sm max-w-md'>
          Try using different keywords or check your spelling
        </p>
      </motion.div>
    );
  }

  if (!query && results.length === 0) {
    return (
      <motion.div
        className='flex flex-col items-center justify-center py-16 text-center'
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className='bg-muted/50 rounded-full p-5 mb-5'>
          <Clock className='h-10 w-10 text-muted-foreground' />
        </div>
        <p className='text-muted-foreground text-lg mb-2'>
          No recent conversations found
        </p>
        <p className='text-muted-foreground text-sm max-w-md'>
          Start a new chat to begin your conversation history
        </p>
      </motion.div>
    );
  }

  return (
    <div className='space-y-3'>
      {results.map((conversation) => (
        <motion.button
          key={conversation.id}
          className='w-full text-left p-4 rounded-xl border border-border hover:bg-accent/30 transition-all hover:shadow-sm hover:border-border/80 group'
          onClick={() => onSelectConversation(conversation)}
          whileTap={{ scale: 0.99 }}
        >
          <div className='flex items-start justify-between'>
            <div className='flex items-center gap-3 mb-1'>
              <div className='rounded-full bg-accent/50 p-1.5 text-foreground'>
                {conversation.is_favorite ? (
                  <Star className='h-4 w-4 shrink-0 fill-yellow-400 text-yellow-400' />
                ) : (
                  <MessageSquare className='h-4 w-4 shrink-0' />
                )}
              </div>
              <h3 className='font-medium text-base'>
                {query
                  ? highlightText(
                      conversation.title || 'Untitled Conversation',
                      query
                    )
                  : conversation.title || 'Untitled Conversation'}
              </h3>
            </div>
            <div className='flex items-center gap-1'>
              <span className='text-xs text-muted-foreground'>
                {conversation.created_at &&
                  format(new Date(conversation.created_at), 'MMM d, yyyy')}
              </span>
              <ArrowRight className='h-3.5 w-3.5 text-primary/0 group-hover:text-primary/80 transition-all duration-300 ease-in-out transform translate-x-0 group-hover:translate-x-1' />
            </div>
          </div>
        </motion.button>
      ))}
    </div>
  );
}
