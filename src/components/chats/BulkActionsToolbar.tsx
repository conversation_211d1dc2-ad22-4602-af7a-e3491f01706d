'use client';

import { motion, AnimatePresence } from 'framer-motion';
import {
  Archive,
  Trash2,
  X,
  MoreHorizontal,
  Download,
  Share2,
  Loader2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

interface BulkActionsToolbarProps {
  selectedCount: number;
  onDeselectAll: () => void;
  onBulkFavorite: () => void;
  onBulkArchive: () => void;
  onBulkDelete: () => void;
  onBulkExport?: () => void;
  onBulkShare?: () => void;
  isVisible: boolean;
  isArchiving?: boolean;
  isDeleting?: boolean;
  isFavoriting?: boolean;
}

export function BulkActionsToolbar({
  selectedCount,
  onDeselectAll,
  // onBulkFavorite,
  onBulkArchive,
  onBulkDelete,
  onBulkExport,
  onBulkShare,
  isVisible,
  isArchiving = false,
  isDeleting = false,
  // isFavoriting = false,
}: BulkActionsToolbarProps) {
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -50 }}
          transition={{ duration: 0.2 }}
          className='sticky top-0 z-50 bg-background/95 backdrop-blur-sm border-b border-border'
        >
          <div className='container mx-auto px-4 py-3'>
            <div className='flex items-center justify-between'>
              {/* Selection info */}
              <div className='flex items-center gap-3'>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={onDeselectAll}
                  className='h-8 w-8 p-0'
                >
                  <X className='h-4 w-4' />
                  <span className='sr-only'>Deselect all</span>
                </Button>

                <div className='flex items-center gap-2'>
                  <Badge variant='secondary' className='text-sm'>
                    {selectedCount} selected
                  </Badge>
                  <span className='text-sm text-muted-foreground'>
                    {selectedCount === 1 ? 'conversation' : 'conversations'}
                  </span>
                </div>
              </div>

              {/* Actions */}
              <div className='flex items-center gap-2'>
                {/* Primary actions */}
                {/* <Button
                  variant='outline'
                  size='sm'
                  onClick={onBulkFavorite}
                  className='h-8'
                >
                  {isFavoriting ? (
                    <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                  ) : (
                    <Star className='h-4 w-4 mr-2' />
                  )}
                  {isFavoriting ? 'Favoriting...' : 'Favorite'}
                </Button> */}

                <Button
                  variant='outline'
                  size='sm'
                  onClick={onBulkArchive}
                  disabled={isArchiving || isDeleting}
                  className='h-8'
                >
                  {isArchiving ? (
                    <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                  ) : (
                    <Archive className='h-4 w-4 mr-2' />
                  )}
                  {isArchiving ? 'Archiving...' : 'Archive'}
                </Button>

                {/* Delete with confirmation */}
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button
                      variant='outline'
                      size='sm'
                      disabled={isArchiving || isDeleting}
                      className='h-8 text-destructive hover:text-destructive hover:bg-destructive/10'
                    >
                      {isDeleting ? (
                        <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                      ) : (
                        <Trash2 className='h-4 w-4 mr-2' />
                      )}
                      {isDeleting ? 'Deleting...' : 'Delete'}
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Delete Conversations</AlertDialogTitle>
                      <AlertDialogDescription>
                        Are you sure you want to delete {selectedCount}{' '}
                        conversation{selectedCount === 1 ? '' : 's'}? This
                        action cannot be undone and will permanently remove all
                        messages and data associated with
                        {selectedCount === 1
                          ? ' this conversation'
                          : ' these conversations'}
                        .
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel disabled={isDeleting}>
                        Cancel
                      </AlertDialogCancel>
                      <AlertDialogAction
                        onClick={onBulkDelete}
                        disabled={isDeleting}
                        className='bg-destructive text-destructive-foreground hover:bg-destructive/90'
                      >
                        {isDeleting ? (
                          <>
                            <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                            Deleting...
                          </>
                        ) : (
                          <>
                            Delete{' '}
                            {selectedCount === 1
                              ? 'Conversation'
                              : 'Conversations'}
                          </>
                        )}
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>

                {/* More actions dropdown */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant='outline' size='sm' className='h-8 w-8 p-0'>
                      <MoreHorizontal className='h-4 w-4' />
                      <span className='sr-only'>More actions</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align='end' className='w-48'>
                    {onBulkExport && (
                      <DropdownMenuItem onClick={onBulkExport}>
                        <Download className='h-4 w-4 mr-2' />
                        Export Selected
                      </DropdownMenuItem>
                    )}
                    {onBulkShare && (
                      <DropdownMenuItem onClick={onBulkShare}>
                        <Share2 className='h-4 w-4 mr-2' />
                        Share Selected
                      </DropdownMenuItem>
                    )}
                    {(onBulkExport || onBulkShare) && <DropdownMenuSeparator />}
                    <DropdownMenuItem onClick={onDeselectAll}>
                      <X className='h-4 w-4 mr-2' />
                      Deselect All
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>

          {/* Subtle gradient border */}
          <div className='absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-border to-transparent' />
        </motion.div>
      )}
    </AnimatePresence>
  );
}
