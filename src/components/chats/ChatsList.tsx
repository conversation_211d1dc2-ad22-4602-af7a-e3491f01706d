'use client';

import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { AnimatePresence } from 'framer-motion';
import {
  Search,
  Filter,
  Grid3X3,
  List,
  SortAsc,
  SortDesc,
  CheckSquare,
  Square,
  Loader2,
  MessageSquare,
} from 'lucide-react';
import { GroupConversation } from '@/lib/supabase/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useBulkSelection } from '@/hooks/useBulkSelection';
import { useInfiniteScroll } from '@/components/sidebar/hooks/useInfiniteScroll';
import { useDebounce } from '@/hooks/useDebounce';
import { ChatCard } from './ChatCard';
import { BulkActionsToolbar } from './BulkActionsToolbar';
import { RenameConversationDialog } from './RenameConversationDialog';
import { ChatContext } from '@/providers/ChatProvider';
import { useConversationActions } from '@/components/sidebar/hooks/useConversationActions';
import { cn } from '@/lib/utils';
import { useRouter } from 'next/navigation';

type ViewMode = 'grid' | 'list';
type SortField = 'created_at' | 'updated_at' | 'title';
type SortOrder = 'asc' | 'desc';
type FilterType =
  | 'all'
  | 'favorites'
  | 'workspace'
  | 'comparison'
  | 'temporary';

interface ChatsListProps {
  conversations: GroupConversation[];
  isLoading: boolean;
  hasMore: boolean;
  onLoadMore: () => void;
  isLoadingMore: boolean;
  sseConnections?: Map<string, { groupConversationId?: string }>;
}

export function ChatsList({
  conversations,
  isLoading,
  hasMore,
  onLoadMore,
  isLoadingMore,
  sseConnections,
}: ChatsListProps) {
  const router = useRouter();
  const { fetchUserConversations } = useContext(ChatContext);

  // Import conversation actions
  const {
    toggleFavorite,
    archiveConversation,
    deleteConversation,
    renameConversation,
    bulkArchiveConversations,
    bulkDeleteConversations,
  } = useConversationActions(fetchUserConversations);

  // State
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortField, setSortField] = useState<SortField>('created_at');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');
  const [filterType, setFilterType] = useState<FilterType>('all');
  const [showBulkSelection, setShowBulkSelection] = useState(false);
  const [renameDialogOpen, setRenameDialogOpen] = useState(false);
  const [conversationToRename, setConversationToRename] =
    useState<GroupConversation | null>(null);
  const [isArchiving, setIsArchiving] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isFavoriting, setIsFavoriting] = useState(false);

  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  // Infinite scroll
  const scrollTarget = React.useRef<HTMLDivElement>(null);
  useInfiniteScroll(scrollTarget, {
    hasMore,
    fetchMore: onLoadMore,
    isFetching: isLoadingMore,
  });

  // Bulk selection
  const bulkSelection = useBulkSelection({
    getId: (conversation) => conversation.id,
    items: conversations,
  });

  // Reset bulk selection when toggling it off
  useEffect(() => {
    if (!showBulkSelection) {
      bulkSelection.deselectAll();
    }
  }, [showBulkSelection]); // deselectAll is memoized, no need to include as dependency

  // Filtered and sorted conversations
  const filteredAndSortedConversations = useMemo(() => {
    let filtered = conversations;

    // Apply search filter
    if (debouncedSearchQuery) {
      const query = debouncedSearchQuery.toLowerCase();
      filtered = filtered.filter((conv) =>
        conv.title?.toLowerCase().includes(query)
      );
    }

    // Apply type filter
    switch (filterType) {
      case 'favorites':
        filtered = filtered.filter((conv) => conv.is_favorite);
        break;
      case 'workspace':
        filtered = filtered.filter((conv) => !!conv.workspace_id);
        break;
      case 'comparison':
        filtered = filtered.filter((conv) => conv.is_comparison);
        break;
      case 'temporary':
        filtered = filtered.filter((conv) => conv.is_temporary);
        break;
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: string | Date | undefined;
      let bValue: string | Date | undefined;

      switch (sortField) {
        case 'title':
          aValue = a.title || 'New Conversation';
          bValue = b.title || 'New Conversation';
          break;
        case 'updated_at':
          aValue = new Date(a.updated_at || a.created_at);
          bValue = new Date(b.updated_at || b.created_at);
          break;
        case 'created_at':
        default:
          aValue = new Date(a.created_at);
          bValue = new Date(b.created_at);
          break;
      }

      if (sortField === 'title') {
        const result = (aValue as string).localeCompare(bValue as string);
        return sortOrder === 'asc' ? result : -result;
      } else {
        const result = (aValue as Date).getTime() - (bValue as Date).getTime();
        return sortOrder === 'asc' ? result : -result;
      }
    });

    return filtered;
  }, [conversations, debouncedSearchQuery, filterType, sortField, sortOrder]);

  // Check if conversation is streaming
  const isConversationStreaming = useCallback(
    (conversationId: string) => {
      if (!sseConnections) return false;
      return Array.from(sseConnections.values()).some(
        (conn) => conn.groupConversationId === conversationId
      );
    },
    [sseConnections]
  );

  // Actions
  const handleConversationSelect = useCallback(
    (conversation: GroupConversation) => {
      if (showBulkSelection) {
        bulkSelection.toggleItem(conversation);
      } else {
        router.push(`/c/${conversation.id}`);
      }
    },
    [showBulkSelection, bulkSelection, router]
  );

  const handleNavigateToConversation = useCallback(
    (conversation: GroupConversation) => {
      router.push(`/c/${conversation.id}`);
    },
    [router]
  );

  const handleToggleFavorite = useCallback(
    (conversation: GroupConversation) => {
      toggleFavorite(conversation);
    },
    [toggleFavorite]
  );

  const handleArchive = useCallback(
    (conversation: GroupConversation) => {
      archiveConversation(conversation);
    },
    [archiveConversation]
  );

  const handleDelete = useCallback(
    (conversation: GroupConversation) => {
      deleteConversation(conversation);
    },
    [deleteConversation]
  );

  const handleRename = useCallback((conversation: GroupConversation) => {
    setConversationToRename(conversation);
    setRenameDialogOpen(true);
  }, []);

  const handleRenameDialogClose = useCallback(() => {
    setRenameDialogOpen(false);
    setConversationToRename(null);
  }, []);

  const handleRenameSubmit = useCallback(
    async (conversation: GroupConversation, newTitle: string) => {
      await renameConversation(conversation, newTitle);
    },
    [renameConversation]
  );

  // Bulk actions
  const handleBulkFavorite = useCallback(() => {
    if (bulkSelection.selectedItems.length > 0 && !isFavoriting) {
      setIsFavoriting(true);
      try {
        bulkSelection.selectedItems.forEach((conversation) => {
          if (!conversation.is_favorite) {
            toggleFavorite(conversation);
          }
        });
        bulkSelection.deselectAll();
      } finally {
        setIsFavoriting(false);
      }
    }
    bulkSelection.deselectAll();
  }, [bulkSelection.selectedItems, toggleFavorite, bulkSelection]);

  const handleBulkArchive = useCallback(async () => {
    if (bulkSelection.selectedItems.length > 0 && !isArchiving && !isDeleting) {
      setIsArchiving(true);
      try {
        await bulkArchiveConversations(bulkSelection.selectedItems);
        bulkSelection.deselectAll();
      } finally {
        setIsArchiving(false);
      }
    }
  }, [
    bulkSelection.selectedItems,
    bulkArchiveConversations,
    bulkSelection,
    isArchiving,
    isDeleting,
  ]);

  const handleBulkDelete = useCallback(async () => {
    if (bulkSelection.selectedItems.length > 0 && !isArchiving && !isDeleting) {
      setIsDeleting(true);
      try {
        await bulkDeleteConversations(bulkSelection.selectedItems);
        bulkSelection.deselectAll();
      } finally {
        setIsDeleting(false);
      }
    }
  }, [
    bulkSelection.selectedItems,
    bulkDeleteConversations,
    bulkSelection,
    isArchiving,
    isDeleting,
  ]);

  // Filter options for display
  const filterOptions = [
    { value: 'all', label: 'All Conversations', count: conversations.length },
    {
      value: 'favorites',
      label: 'Favorites',
      count: conversations.filter((c) => c.is_favorite).length,
    },
    {
      value: 'workspace',
      label: 'Workspace',
      count: conversations.filter((c) => !!c.workspace_id).length,
    },
    {
      value: 'comparison',
      label: 'Comparisons',
      count: conversations.filter((c) => c.is_comparison).length,
    },
    {
      value: 'temporary',
      label: 'Temporary',
      count: conversations.filter((c) => c.is_temporary).length,
    },
  ];

  if (isLoading) {
    return (
      <div className='flex items-center justify-center min-h-[400px]'>
        <div className='text-center'>
          <Loader2 className='h-8 w-8 animate-spin mx-auto mb-4 text-primary' />
          <p className='text-muted-foreground'>Loading conversations...</p>
        </div>
      </div>
    );
  }

  return (
    <div className='flex flex-col h-full'>
      {/* Bulk Actions Toolbar */}
      <BulkActionsToolbar
        isVisible={showBulkSelection && bulkSelection.hasSelection}
        selectedCount={bulkSelection.selectedCount}
        onDeselectAll={bulkSelection.deselectAll}
        onBulkFavorite={handleBulkFavorite}
        onBulkArchive={handleBulkArchive}
        onBulkDelete={handleBulkDelete}
        isArchiving={isArchiving}
        isDeleting={isDeleting}
        isFavoriting={isFavoriting}
      />

      {/* Fixed Header */}
      <div className='flex-shrink-0 space-y-6 bg-background/95 backdrop-blur-sm border-b border-border p-6'>
        {/* Header with controls */}
        <div className='flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between'>
          <div className='flex items-center gap-3'>
            <MessageSquare className='h-6 w-6 text-primary' />
            <div>
              <h1 className='text-2xl font-bold'>Conversations</h1>
              <p className='text-sm text-muted-foreground'>
                {filteredAndSortedConversations.length} of{' '}
                {conversations.length} conversations
              </p>
            </div>
          </div>

          <div className='flex items-center gap-2'>
            {/* Bulk selection toggle */}
            <Button
              variant={showBulkSelection ? 'default' : 'outline'}
              size='sm'
              onClick={() => setShowBulkSelection(!showBulkSelection)}
              className='h-8'
            >
              {showBulkSelection ? (
                <CheckSquare className='h-4 w-4' />
              ) : (
                <Square className='h-4 w-4' />
              )}
              <span className='ml-2 hidden sm:inline'>
                {showBulkSelection ? 'Exit Selection' : 'Select'}
              </span>
            </Button>

            {/* View mode toggle */}
            <div className='flex border rounded-md'>
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size='sm'
                onClick={() => setViewMode('grid')}
                className='h-8 rounded-r-none'
              >
                <Grid3X3 className='h-4 w-4' />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size='sm'
                onClick={() => setViewMode('list')}
                className='h-8 rounded-l-none'
              >
                <List className='h-4 w-4' />
              </Button>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className='flex flex-col sm:flex-row gap-4'>
          {/* Search */}
          <div className='relative flex-1 max-w-md'>
            <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground' />
            <Input
              placeholder='Search conversations...'
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className='pl-10'
            />
          </div>

          <div className='flex gap-2'>
            {/* Filter */}
            <Select
              value={filterType}
              onValueChange={(value: FilterType) => setFilterType(value)}
            >
              <SelectTrigger className='w-[180px] h-9'>
                <Filter className='h-4 w-4 mr-2' />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {filterOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className='flex items-center justify-between w-full'>
                      <span>{option.label}</span>
                      <Badge variant='secondary' className='ml-2 text-xs'>
                        {option.count}
                      </Badge>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Sort */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant='outline' size='sm' className='h-9'>
                  {sortOrder === 'asc' ? (
                    <SortAsc className='h-4 w-4' />
                  ) : (
                    <SortDesc className='h-4 w-4' />
                  )}
                  <span className='ml-2 hidden sm:inline'>Sort</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align='end' className='w-48'>
                <DropdownMenuItem
                  onClick={() => {
                    setSortField('created_at');
                    setSortOrder('desc');
                  }}
                >
                  Newest First
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => {
                    setSortField('created_at');
                    setSortOrder('asc');
                  }}
                >
                  Oldest First
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => {
                    setSortField('updated_at');
                    setSortOrder('desc');
                  }}
                >
                  Recently Updated
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => {
                    setSortField('title');
                    setSortOrder('asc');
                  }}
                >
                  Alphabetical
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Select All checkbox (when in bulk selection mode) */}
        {showBulkSelection && filteredAndSortedConversations.length > 0 && (
          <div className='flex items-center gap-3 px-1'>
            <Checkbox
              checked={bulkSelection.isAllSelected}
              onCheckedChange={bulkSelection.toggleAll}
              data-indeterminate={bulkSelection.isPartiallySelected}
            />
            <span className='text-sm text-muted-foreground'>
              Select all {filteredAndSortedConversations.length} conversations
              on this page
            </span>
          </div>
        )}
      </div>

      {/* Scrollable Content Area */}
      <div className='flex-1 overflow-y-auto'>
        <div className='p-6'>
          {/* Conversations Grid/List */}
          {filteredAndSortedConversations.length > 0 ? (
            <div
              className={cn(
                'gap-4',
                viewMode === 'grid'
                  ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
                  : 'flex flex-col space-y-2'
              )}
            >
              <AnimatePresence mode='popLayout'>
                {filteredAndSortedConversations.map((conversation) => (
                  <ChatCard
                    key={conversation.id}
                    conversation={conversation}
                    isSelected={bulkSelection.isSelected(conversation)}
                    onSelect={() => handleConversationSelect(conversation)}
                    onToggleSelection={() =>
                      bulkSelection.toggleItem(conversation)
                    }
                    onNavigate={() =>
                      handleNavigateToConversation(conversation)
                    }
                    onToggleFavorite={() => handleToggleFavorite(conversation)}
                    onArchive={() => handleArchive(conversation)}
                    onDelete={() => handleDelete(conversation)}
                    onRename={(e) => {
                      e.preventDefault();
                      handleRename(conversation);
                    }}
                    showSelection={showBulkSelection}
                    isStreaming={isConversationStreaming(conversation.id)}
                  />
                ))}
              </AnimatePresence>
            </div>
          ) : (
            <div className='text-center py-12'>
              <div className='bg-muted/50 rounded-full p-6 w-24 h-24 mx-auto mb-4 flex items-center justify-center'>
                <MessageSquare className='h-12 w-12 text-muted-foreground' />
              </div>
              <h3 className='text-lg font-medium mb-2'>
                No conversations found
              </h3>
              <p className='text-muted-foreground mb-4'>
                {debouncedSearchQuery || filterType !== 'all'
                  ? 'Try adjusting your search or filters'
                  : 'Start a new conversation to get started'}
              </p>
              {!debouncedSearchQuery && filterType === 'all' && (
                <Button onClick={() => router.push('/')}>
                  Start New Conversation
                </Button>
              )}
            </div>
          )}

          {/* Load More */}
          <div
            ref={scrollTarget}
            className='flex items-center justify-center py-8'
          >
            {isLoadingMore && (
              <div className='flex items-center gap-2 text-muted-foreground'>
                <Loader2 className='h-4 w-4 animate-spin' />
                <span>Loading more conversations...</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Rename Dialog */}
      <RenameConversationDialog
        conversation={conversationToRename}
        isOpen={renameDialogOpen}
        onClose={handleRenameDialogClose}
        onRename={handleRenameSubmit}
      />
    </div>
  );
}
