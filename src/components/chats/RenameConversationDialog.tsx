'use client';

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { GroupConversation } from '@/lib/supabase/types';

interface RenameConversationDialogProps {
  conversation: GroupConversation | null;
  isOpen: boolean;
  onClose: () => void;
  onRename: (conversation: GroupConversation, newTitle: string) => void;
}

export function RenameConversationDialog({
  conversation,
  isOpen,
  onClose,
  onRename,
}: RenameConversationDialogProps) {
  const [title, setTitle] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Update title when conversation changes
  useEffect(() => {
    if (conversation) {
      setTitle(conversation.title || 'New Conversation');
    }
  }, [conversation]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!conversation || !title.trim()) return;

    setIsSubmitting(true);
    try {
      await onRename(conversation, title.trim());
      onClose();
    } catch (error) {
      console.error('Failed to rename conversation:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  // Fix: Properly handle the onOpenChange boolean parameter
  const handleOpenChange = (open: boolean) => {
    if (!open && !isSubmitting) {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange} modal>
      <DialogContent className='sm:max-w-[425px]'>
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Rename Conversation</DialogTitle>
            <DialogDescription>
              Enter a new name for this conversation.
            </DialogDescription>
          </DialogHeader>

          <div className='grid gap-4 py-4'>
            <div className='grid grid-cols-4 items-center gap-4'>
              <Label htmlFor='conversation-title' className='text-right'>
                Name
              </Label>
              <Input
                id='conversation-title'
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className='col-span-3'
                placeholder='Enter conversation name...'
                disabled={isSubmitting}
                autoFocus
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              type='button'
              variant='outline'
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type='submit' disabled={isSubmitting || !title.trim()}>
              {isSubmitting ? 'Saving...' : 'Save changes'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
