'use client';

import { motion } from 'framer-motion';
import {
  MessageSquare,
  Star,
  Briefcase,
  Calendar,
  MoreVertical,
  Archive,
  Trash2,
  Edit3,
  ExternalLink,
} from 'lucide-react';
import { GroupConversation } from '@/lib/supabase/types';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';

interface ChatCardProps {
  conversation: GroupConversation;
  isSelected: boolean;
  onSelect: () => void;
  onToggleSelection: () => void;
  onNavigate: () => void;
  onToggleFavorite: () => void;
  onArchive: () => void;
  onDelete: () => void;
  onRename: (e: React.MouseEvent<HTMLDivElement>) => void;
  showSelection: boolean;
  isStreaming?: boolean;
}

export function ChatCard({
  conversation,
  isSelected,
  onSelect,
  onToggleSelection,
  onNavigate,
  onToggleFavorite,
  onArchive,
  onDelete,
  onRename,
  showSelection,
  isStreaming = false,
}: ChatCardProps) {
  const title = conversation.title || 'New Conversation';
  const isWorkspaceChat = !!conversation.workspace_id;
  const isFavorite = conversation.is_favorite;
  const createdDate = new Date(conversation.created_at);

  const formatDate = (date: Date) => {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'Today';
    if (diffDays === 2) return 'Yesterday';
    if (diffDays <= 7) return `${diffDays - 1} days ago`;

    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined,
    });
  };

  return (
    <TooltipProvider>
      <motion.div
        layout
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        whileHover={{ y: -2 }}
        transition={{ duration: 0.2 }}
        className='group'
      >
        <Card
          className={cn(
            'relative overflow-hidden border transition-all duration-200 hover:shadow-lg hover:shadow-primary/5',
            isSelected && 'ring-2 ring-primary/50 bg-primary/5',
            'cursor-pointer'
          )}
          onClick={onSelect}
        >
          <CardContent className='p-4'>
            {/* Header with selection and actions */}
            <div className='flex items-start justify-between mb-3'>
              <div className='flex items-center gap-3 min-w-0 flex-1'>
                {/* Selection checkbox */}
                {showSelection && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.15 }}
                  >
                    <Checkbox
                      checked={isSelected}
                      onCheckedChange={onToggleSelection}
                      onClick={(e) => e.stopPropagation()}
                      aria-label={`Select ${title}`}
                    />
                  </motion.div>
                )}

                {/* Icon */}
                <div className='flex-shrink-0'>
                  {isFavorite ? (
                    <Star className='h-5 w-5 fill-yellow-400 text-yellow-400' />
                  ) : isWorkspaceChat ? (
                    <div className='flex items-center justify-center h-5 w-5 rounded-full bg-primary/10 text-primary'>
                      <Briefcase className='h-3 w-3' />
                    </div>
                  ) : (
                    <MessageSquare className='h-5 w-5 text-muted-foreground' />
                  )}
                </div>

                {/* Title */}
                <div className='min-w-0 flex-1'>
                  <h3
                    className={cn(
                      'font-medium text-sm leading-tight truncate',
                      isSelected && 'text-primary'
                    )}
                    title={title}
                  >
                    {title}
                  </h3>
                </div>
              </div>

              {/* Actions dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant='ghost'
                    size='sm'
                    className='h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity'
                    onClick={(e) => e.stopPropagation()}
                  >
                    <MoreVertical className='h-4 w-4' />
                    <span className='sr-only'>Open menu</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align='end' className='w-48'>
                  <DropdownMenuItem onClick={(e) => {
                    e.stopPropagation();
                    onNavigate();
                  }}>
                    <ExternalLink className='h-4 w-4 mr-2' />
                    Open Chat
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={(e) => {
                    e.stopPropagation();
                    setTimeout(() => {
                      onRename(e);
                    }, 100);
                  }}>
                    <Edit3 className='h-4 w-4 mr-2' />
                    Rename
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={(e) => {
                    e.stopPropagation();
                    onToggleFavorite();
                  }}>
                    <Star className={cn('h-4 w-4 mr-2', isFavorite && 'fill-yellow-400 text-yellow-400')} />
                    {isFavorite ? 'Remove from favorites' : 'Add to favorites'}
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={(e) => {
                    e.stopPropagation();
                    onArchive();
                  }}>
                    <Archive className='h-4 w-4 mr-2' />
                    Archive
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={(e) => {
                      e.stopPropagation();
                      onDelete();
                    }}
                    className='text-destructive focus:text-destructive'
                  >
                    <Trash2 className='h-4 w-4 mr-2' />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* Metadata */}
            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-2 text-xs text-muted-foreground'>
                <Calendar className='h-3 w-3' />
                <span>{formatDate(createdDate)}</span>

                {/* Streaming indicator */}
                {isStreaming && (
                  <div className='flex items-center gap-1'>
                    <div className='h-1.5 w-1.5 rounded-full bg-blue-500 animate-pulse' />
                    <span className='text-blue-500'>Active</span>
                  </div>
                )}
              </div>

              {/* Badges */}
              <div className='flex items-center gap-1'>
                {isWorkspaceChat && (
                  <Tooltip>
                    <TooltipTrigger>
                      <Badge variant='secondary' className='text-xs'>
                        Workspace
                      </Badge>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>This chat is linked to a workspace</p>
                    </TooltipContent>
                  </Tooltip>
                )}

                {conversation.is_comparison && (
                  <Badge variant='outline' className='text-xs'>
                    Comparison
                  </Badge>
                )}

                {conversation.is_temporary && (
                  <Badge variant='outline' className='text-xs text-amber-600'>
                    Temporary
                  </Badge>
                )}
              </div>
            </div>
          </CardContent>

          {/* Hover overlay */}
          <div className='absolute inset-0 bg-gradient-to-r from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none' />
        </Card>
      </motion.div>
    </TooltipProvider>
  );
}
