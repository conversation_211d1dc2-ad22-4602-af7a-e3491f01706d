import { Component, ReactNode } from 'react';
import { AppError } from '@/lib/error';
import { logger } from '@/lib/logger';

interface Props {
  children: ReactNode;
  fallback: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log the error
    logger.error({
      error: error instanceof AppError ? error.toJSON() : error,
      componentStack: errorInfo.componentStack
    }, 'React component error');

    // Report to error monitoring services (handles both Sentry and Bugsnag)
    // This will only report in production environment
    if (!(error instanceof AppError)) {
      // For non-AppError instances, use handleError which will report to monitoring services
      try {
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const { handleError } = require('@/lib/error');
        handleError(error);
      } catch (importError) {
        console.error('Failed to import handleError:', importError);
      }
    }
    // AppError instances are already reported in their constructor
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback;
    }
    return this.props.children;
  }
}