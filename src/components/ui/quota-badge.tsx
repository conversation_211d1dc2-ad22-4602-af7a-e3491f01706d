import { useState, useEffect } from 'react';
import { Badge } from './badge';
import { CustomTooltip } from './tooltip';
import { ImageIcon } from 'lucide-react';

interface QuotaData {
  images: {
    used: number;
    remaining: number;
    total: number;
    resetDate: string;
  };
}

export function QuotaBadge() {
  const [quota, setQuota] = useState<QuotaData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchQuota = async () => {
      try {
        const response = await fetch('/api/me/quota');
        if (response.ok) {
          const data = await response.json();
          setQuota(data);
        }
      } catch (error) {
        console.error('Failed to fetch quota:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchQuota();
  }, []);

  if (loading || !quota) {
    return null;
  }

  const { images } = quota;
  const percentage = images.total > 0 ? (images.used / images.total) * 100 : 0;

  // Determine badge variant based on usage
  let variant: 'default' | 'secondary' | 'destructive' | 'outline' = 'default';
  if (percentage >= 90) {
    variant = 'destructive';
  } else if (percentage >= 70) {
    variant = 'secondary';
  }

  const tooltipContent = `Images: ${images.used}/${images.total} used today\nResets daily at midnight UTC`;

  return (
    <CustomTooltip tooltip={tooltipContent}>
      <Badge variant={variant} className='flex items-center gap-1 text-xs'>
        <ImageIcon className='h-3 w-3' />
        {images.remaining}
      </Badge>
    </CustomTooltip>
  );
}
