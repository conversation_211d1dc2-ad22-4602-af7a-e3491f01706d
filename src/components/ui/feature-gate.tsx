'use client';

import React, { useState } from 'react';
import { SubscriptionPlan } from '@/lib/supabase/types';
import { UpgradePrompt } from './upgrade-prompt';

interface FeatureGateProps {
  children: React.ReactNode;
  hasAccess: boolean;
  feature: string;
  requiredPlan: SubscriptionPlan;
  fallback?: React.ReactNode;
  showUpgradePrompt?: boolean;
  upgradePromptTitle?: string;
  upgradePromptDescription?: string;
}

export function FeatureGate({
  children,
  hasAccess,
  feature,
  requiredPlan,
  fallback,
  showUpgradePrompt = true,
  upgradePromptTitle,
  upgradePromptDescription,
}: FeatureGateProps) {
  const [showUpgrade, setShowUpgrade] = useState(false);

  if (hasAccess) {
    return <>{children}</>;
  }

  if (fallback) {
    return <>{fallback}</>;
  }

  if (showUpgradePrompt) {
    return (
      <>
        <div onClick={() => setShowUpgrade(true)}>
          {children}
        </div>
        <UpgradePrompt
          isOpen={showUpgrade}
          onClose={() => setShowUpgrade(false)}
          feature={feature}
          requiredPlan={requiredPlan}
          title={upgradePromptTitle}
          description={upgradePromptDescription}
        />
      </>
    );
  }

  return null;
}
