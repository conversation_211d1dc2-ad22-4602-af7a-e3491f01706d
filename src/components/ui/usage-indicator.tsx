'use client';

import { useSubscription } from '@/providers/SubscriptionProvider';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { AlertTriangle, Zap, CreditCard } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';

interface UsageIndicatorProps {
  className?: string;
  showDetails?: boolean;
}

export function UsageIndicator({
  className = '',
  showDetails = false,
}: UsageIndicatorProps) {
  const { quotaStatus, getPlan } = useSubscription();
  const currentPlan = getPlan();

  if (currentPlan === 'free' || !quotaStatus) {
    return null;
  }

  const handleUpgrade = () => {
    window.location.href = '/settings';
  };

  const handlePurchaseTokens = () => {
    toast.info('Redirecting to token purchase...');
    window.location.href = '/settings?tab=addons';
  };

  return (
    <div className={`space-y-2 ${className}`}>
      {/* Token Usage */}
      {quotaStatus.tokens && (
        <div className='flex items-center gap-2'>
          <div className='flex-1'>
            {showDetails && (
              <div className='text-xs text-muted-foreground mb-1'>
                Tokens: {quotaStatus.tokens.used.toLocaleString()} /{' '}
                {quotaStatus.tokens.total.toLocaleString()}
              </div>
            )}
            <Progress
              value={
                quotaStatus.tokens.total > 0
                  ? (quotaStatus.tokens.used / quotaStatus.tokens.total) * 100
                  : 0
              }
              className='h-1.5'
            />
          </div>

          {quotaStatus.tokens.remaining <= 0 && (
            <Badge variant='destructive' className='text-xs'>
              <AlertTriangle className='w-3 h-3 mr-1' />
              Quota Exceeded
            </Badge>
          )}

          {quotaStatus.tokens.remaining > 0 &&
            quotaStatus.tokens.total > 0 &&
            quotaStatus.tokens.used / quotaStatus.tokens.total >= 0.9 && (
              <Badge
                variant='outline'
                className='text-xs border-amber-500 text-amber-600'
              >
                <AlertTriangle className='w-3 h-3 mr-1' />
                {quotaStatus.tokens.total > 0
                  ? Math.round(
                      (quotaStatus.tokens.remaining /
                        quotaStatus.tokens.total) *
                        100
                    )
                  : 0}
                % Left
              </Badge>
            )}
        </div>
      )}

      {/* Image Credits */}
      {quotaStatus.images && showDetails && (
        <div className='flex items-center gap-2'>
          <div className='flex-1'>
            <div className='text-xs text-muted-foreground mb-1'>
              Images: {quotaStatus.images.used} / {quotaStatus.images.total}
            </div>
            <Progress
              value={
                quotaStatus.images.total > 0
                  ? (quotaStatus.images.used / quotaStatus.images.total) * 100
                  : 0
              }
              className='h-1.5'
            />
          </div>
        </div>
      )}

      {/* Comparison Usage (Premium only) */}
      {quotaStatus.comparisons && currentPlan === 'premium' && showDetails && (
        <div className='flex items-center gap-2'>
          <div className='flex-1'>
            <div className='text-xs text-muted-foreground mb-1'>
              Comparisons Today: {quotaStatus.comparisons.used} /{' '}
              {quotaStatus.comparisons.total}
            </div>
            <Progress
              value={
                quotaStatus.comparisons.total > 0
                  ? (quotaStatus.comparisons.used /
                      quotaStatus.comparisons.total) *
                    100
                  : 0
              }
              className='h-1.5'
            />
          </div>
        </div>
      )}

      {/* Quick Actions */}
      {showDetails &&
        quotaStatus.tokens &&
        quotaStatus.tokens.remaining <= 10000 && (
          <div className='flex gap-2 mt-2'>
            {currentPlan === 'premium' && (
              <Button
                size='sm'
                variant='outline'
                onClick={handlePurchaseTokens}
                className='text-xs'
              >
                <Zap className='w-3 h-3 mr-1' />
                Buy Tokens
              </Button>
            )}

            {currentPlan === 'starter' && (
              <Button
                size='sm'
                variant='outline'
                onClick={handleUpgrade}
                className='text-xs'
              >
                <CreditCard className='w-3 h-3 mr-1' />
                Upgrade
              </Button>
            )}
          </div>
        )}
    </div>
  );
}

export default UsageIndicator;
