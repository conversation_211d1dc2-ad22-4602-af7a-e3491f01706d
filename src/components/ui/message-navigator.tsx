import { useState, useEffect } from 'react';
import { ChevronUp, ChevronDown } from 'lucide-react';
import { Button } from './button';
import { cn } from '@/lib/utils';

interface MessageNavigatorProps {
  scrollAreaId: string;
  className?: string;
}

export function MessageNavigator({
  scrollAreaId,
  className,
}: MessageNavigatorProps) {
  const [atFirst, setAtFirst] = useState(true);
  const [atLast, setAtLast] = useState(false);
  const [showNavigator, setShowNavigator] = useState(false);

  useEffect(() => {
    const scrollArea = document.getElementById(scrollAreaId);
    if (!scrollArea) return;

    // Get all user messages for navigation
    const getUserMessages = () => {
      return Array.from(
        scrollArea.querySelectorAll('[data-message-role="user"]')
      ) as HTMLElement[];
    };

    const checkPosition = () => {
      const userMessages = getUserMessages();
      if (userMessages.length <= 1) {
        setShowNavigator(false);
        return;
      }

      setShowNavigator(true);

      // Check if at first or last message
      const scrollTop = scrollArea.scrollTop;
      const scrollHeight = scrollArea.scrollHeight;
      const clientHeight = scrollArea.clientHeight;

      setAtFirst(scrollTop < 50);
      setAtLast(scrollHeight - scrollTop - clientHeight < 50);
    };

    // Initial check
    checkPosition();

    // Add event listener
    scrollArea.addEventListener('scroll', checkPosition);

    // Handle content changes
    const resizeObserver = new ResizeObserver(() => {
      checkPosition();
    });

    resizeObserver.observe(scrollArea);

    return () => {
      scrollArea.removeEventListener('scroll', checkPosition);
      resizeObserver.disconnect();
    };
  }, [scrollAreaId]);

  const navigateToMessage = (direction: 'up' | 'down') => {
    const scrollArea = document.getElementById(scrollAreaId);
    if (!scrollArea) return;

    const userMessages = Array.from(
      scrollArea.querySelectorAll('[data-message-role="user"]')
    ) as HTMLElement[];

    if (userMessages.length <= 1) return;

    // Find the current visible message
    const scrollMiddle = scrollArea.scrollTop + scrollArea.clientHeight / 2;
    let targetIndex = 0;

    for (let i = 0; i < userMessages.length; i++) {
      const msgTop = userMessages[i].offsetTop;
      if (msgTop > scrollMiddle) {
        targetIndex = i;
        break;
      }
      if (i === userMessages.length - 1) {
        targetIndex = i;
      }
    }

    // Adjust target based on direction
    if (direction === 'up') {
      targetIndex = Math.max(0, targetIndex - 1);
    } else {
      targetIndex = Math.min(userMessages.length - 1, targetIndex + 1);
    }

    // Scroll to target
    userMessages[targetIndex].scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    });
  };

  if (!showNavigator) return null;

  return (
    <div
      className={cn(
        'fixed right-4 bottom-24 flex flex-col space-y-1 z-10',
        className
      )}
    >
      <Button
        variant='outline'
        size='icon'
        className='h-8 w-8 rounded-full bg-background/80 backdrop-blur-sm'
        onClick={() => navigateToMessage('up')}
        disabled={atFirst}
      >
        <ChevronUp className='h-4 w-4' />
      </Button>
      <Button
        variant='outline'
        size='icon'
        className='h-8 w-8 rounded-full bg-background/80 backdrop-blur-sm'
        onClick={() => navigateToMessage('down')}
        disabled={atLast}
      >
        <ChevronDown className='h-4 w-4' />
      </Button>
    </div>
  );
}
