'use client';

import React from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Check, Crown, Zap, Users } from 'lucide-react';
import { SUBSCRIPTION_PLANS, SubscriptionPlan } from '@/lib/supabase/types';

interface UpgradePromptProps {
  isOpen: boolean;
  onClose: () => void;
  feature: string;
  requiredPlan: SubscriptionPlan;
  title?: string;
  description?: string;
}

export function UpgradePrompt({
  isOpen,
  onClose,
  feature,
  requiredPlan,
  title,
  description,
}: UpgradePromptProps) {
  const planDetails = SUBSCRIPTION_PLANS[requiredPlan];

  const getPlanIcon = (plan: SubscriptionPlan) => {
    switch (plan) {
      case 'starter':
        return <Zap className="h-5 w-5" />;
      case 'premium':
        return <Crown className="h-5 w-5" />;
      default:
        return <Users className="h-5 w-5" />;
    }
  };

  const getPlanColor = (plan: SubscriptionPlan) => {
    switch (plan) {
      case 'starter':
        return 'bg-blue-500';
      case 'premium':
        return 'bg-purple-500';
      default:
        return 'bg-gray-500';
    }
  };

  const defaultTitle = `${feature} requires ${planDetails.name}`;
  const defaultDescription = `Upgrade to ${planDetails.name} to unlock ${feature.toLowerCase()} and other premium features.`;

  const handleUpgrade = () => {
    // TODO: Implement navigation to pricing/upgrade page
    window.open('/pricing', '_blank');
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-3 mb-2">
            <div className={`p-2 rounded-full ${getPlanColor(requiredPlan)} text-white`}>
              {getPlanIcon(requiredPlan)}
            </div>
            <div>
              <DialogTitle className="text-left">
                {title || defaultTitle}
              </DialogTitle>
              <Badge variant="secondary" className="mt-1">
                {planDetails.name}
              </Badge>
            </div>
          </div>
          <DialogDescription className="text-left">
            {description || defaultDescription}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="bg-muted/50 rounded-lg p-4">
            <h4 className="font-medium mb-3 flex items-center gap-2">
              <span className={`w-2 h-2 rounded-full ${getPlanColor(requiredPlan)}`} />
              {planDetails.name} includes:
            </h4>
            <ul className="space-y-2">
              {planDetails.features.map((feature, index) => (
                <li key={index} className="flex items-center gap-2 text-sm">
                  <Check className="h-4 w-4 text-green-500 flex-shrink-0" />
                  <span>{feature}</span>
                </li>
              ))}
            </ul>
          </div>

          <div className="text-center">
            <div className="text-2xl font-bold">{planDetails.price}</div>
            <div className="text-sm text-muted-foreground">per month</div>
          </div>
        </div>

        <DialogFooter className="flex-col sm:flex-col gap-2">
          <Button onClick={handleUpgrade} className="w-full">
            Upgrade to {planDetails.name}
          </Button>
          <Button variant="outline" onClick={onClose} className="w-full">
            Maybe Later
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
