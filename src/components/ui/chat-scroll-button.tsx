'use client';

import { useEffect, useState } from 'react';
import { ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface ChatScrollButtonProps {
  scrollAreaId: string;
  className?: string;
}

export function ChatScrollButton({
  scrollAreaId,
  className,
}: ChatScrollButtonProps) {
  const [showButton, setShowButton] = useState(false);

  useEffect(() => {
    const scrollArea = document.getElementById(scrollAreaId);

    if (!scrollArea) return;

    const checkScroll = () => {
      // Show button if not at bottom and has overflow
      const isAtBottom =
        scrollArea.scrollHeight - scrollArea.scrollTop <=
        scrollArea.clientHeight + 30;
      const hasOverflow = scrollArea.scrollHeight > scrollArea.clientHeight;

      setShowButton(hasOverflow && !isAtBottom);
    };

    // Initial check
    checkScroll();

    // Add event listener
    scrollArea.addEventListener('scroll', checkScroll);

    // Handle content changes
    const resizeObserver = new ResizeObserver(() => {
      checkScroll();
    });

    resizeObserver.observe(scrollArea);

    return () => {
      scrollArea.removeEventListener('scroll', checkScroll);
      resizeObserver.disconnect();
    };
  }, [scrollAreaId]);

  const scrollToBottom = () => {
    const scrollArea = document.getElementById(scrollAreaId);
    if (scrollArea) {
      scrollArea.scrollTo({
        top: scrollArea.scrollHeight,
        behavior: 'smooth',
      });
    }
  };

  // move to center of the chat area
  return (
    <Button
      variant='outline'
      size='icon'
      className={cn(
        'absolute bottom-4 right-1/2 rounded-full shadow-md transition-opacity duration-200 z-10',
        showButton ? 'opacity-100' : 'opacity-0 pointer-events-none',
        className
      )}
      onClick={scrollToBottom}
      aria-label='Scroll to bottom'
    >
      <ChevronDown className='h-4 w-4' />
    </Button>
  );
}
