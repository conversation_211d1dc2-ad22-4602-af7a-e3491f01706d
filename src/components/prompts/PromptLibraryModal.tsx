'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Search, Plus, User, Globe, Building2 } from 'lucide-react';
import { Tables } from '@/types/database.types';
import { useDebounce } from '@/hooks/useDebounce';
import { PromptCard } from './PromptCard';
import { PromptPreview } from './PromptPreview';
import { VariableDialog } from './VariableDialog';
import { PromptEditorDrawer } from './PromptEditorDrawer';
import {
  parsePromptVariables,
  resolvePromptVariables,
  VariableResolutionContext,
} from '@/lib/promptVariables';
import { useFeatureAccess } from '@/hooks/useFeatureAccess';
import { UpgradePrompt } from '@/components/ui/upgrade-prompt';

type Prompt = Tables<'prompts'>;

interface PromptLibraryModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUsePrompt: (resolvedPrompt: string, defaultModel?: string) => void;
  context?: VariableResolutionContext;
}

interface PromptSearchFilters {
  query: string;
  tags: string[];
  visibility: 'all' | 'private' | 'workspace' | 'public';
}

export function PromptLibraryModal({
  isOpen,
  onClose,
  onUsePrompt,
  context = {},
}: PromptLibraryModalProps) {
  const { canUsePromptLibrary } = useFeatureAccess();
  const [prompts, setPrompts] = React.useState<Prompt[]>([]);
  const [loading, setLoading] = React.useState(false);
  const [selectedPrompt, setSelectedPrompt] = React.useState<Prompt | null>(
    null
  );
  const [showVariableDialog, setShowVariableDialog] = React.useState(false);
  const [showEditor, setShowEditor] = React.useState(false);
  const [editingPrompt, setEditingPrompt] = React.useState<Prompt | null>(null);
  const [filters, setFilters] = useState<PromptSearchFilters>({
    query: '',
    tags: [],
    visibility: 'all',
  });

  // If user can't access prompt library, show upgrade prompt instead
  if (isOpen && !canUsePromptLibrary) {
    return (
      <UpgradePrompt
        isOpen={true}
        onClose={onClose}
        feature='Prompt Library'
        requiredPlan='starter'
        title='Prompt Library requires Starter Plan'
        description='Upgrade to Starter or Premium plan to access the prompt library and save your favorite prompts.'
      />
    );
  }

  const debouncedQuery = useDebounce(filters.query, 300);

  // Fetch prompts based on current filters
  const fetchPrompts = useCallback(async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams();

      if (debouncedQuery) {
        params.append('q', debouncedQuery);
      }

      if (filters.visibility !== 'all') {
        params.append('visibility', filters.visibility);
      }

      filters.tags.forEach((tag) => {
        params.append('tags', tag);
      });

      const response = await fetch(`/api/prompts?${params}`);
      const data = await response.json();

      if (data.error) {
        console.error('Failed to fetch prompts:', data.error);
        return;
      }

      setPrompts(data.data || []);
    } catch (error) {
      console.error('Error fetching prompts:', error);
    } finally {
      setLoading(false);
    }
  }, [debouncedQuery, filters.visibility, JSON.stringify(filters.tags)]);

  useEffect(() => {
    if (isOpen) {
      fetchPrompts();
    }
  }, [isOpen, fetchPrompts]);

  const handleUsePrompt = useCallback(
    async (prompt: Prompt) => {
      const variables = parsePromptVariables(prompt.body_md);

      if (variables.length > 0) {
        // Set both states in a batch to prevent multiple re-renders
        setSelectedPrompt(prompt);
        setShowVariableDialog(true);
      } else {
        // No variables, use prompt directly
        // Track usage
        try {
          await fetch(`/api/prompts/${prompt.id}/use`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ sent: false }),
          });
        } catch (error) {
          console.error('Failed to track prompt usage:', error);
        }

        onUsePrompt(prompt.body_md, prompt.default_model || undefined);
        onClose();
      }
    },
    [onUsePrompt, onClose]
  );

  const handleVariableSubmit = async (userValues: Record<string, string>) => {
    if (!selectedPrompt) return;

    const { resolvedBody } = resolvePromptVariables(
      selectedPrompt.body_md,
      userValues,
      context
    );

    // Track usage
    try {
      await fetch(`/api/prompts/${selectedPrompt.id}/use`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sent: false }), // Will be marked as sent when message is actually sent
      });
    } catch (error) {
      console.error('Failed to track prompt usage:', error);
    }

    onUsePrompt(resolvedBody, selectedPrompt.default_model || undefined);
    setShowVariableDialog(false);
    setSelectedPrompt(null);
    onClose();
  };

  const handleCreateNew = useCallback(() => {
    setEditingPrompt(null);
    setShowEditor(true);
  }, []);

  const handleEditPrompt = useCallback((prompt: Prompt) => {
    setEditingPrompt(prompt);
    setShowEditor(true);
  }, []);

  const handlePromptSaved = useCallback(() => {
    setShowEditor(false);
    setEditingPrompt(null);
    fetchPrompts(); // Refresh the list
  }, [fetchPrompts]);

  const handleSelectPrompt = useCallback((prompt: Prompt) => {
    setSelectedPrompt(prompt);
  }, []);

  const availableTags = Array.from(
    new Set(
      prompts
        .flatMap((p) => p.tags)
        .filter((tag): tag is string => tag !== null)
    )
  ).sort();

  const handleTagToggle = (tag: string) => {
    setFilters((prev) => ({
      ...prev,
      tags: prev.tags.includes(tag)
        ? prev.tags.filter((t) => t !== tag)
        : [...prev.tags, tag],
    }));
  };

  const clearFilters = () => {
    setFilters({
      query: '',
      tags: [],
      visibility: 'all',
    });
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className='max-w-6xl h-[80vh]'>
          <DialogHeader>
            <DialogTitle>Prompt Library</DialogTitle>
          </DialogHeader>

          <div className='flex flex-1 gap-6 overflow-hidden relative'>
            {/* Left panel - Search and filters */}
            <div
              className={`${
                selectedPrompt ? 'w-1/3' : 'w-full max-w-2xl mx-auto'
              } flex flex-col space-y-4 transition-all duration-200`}
            >
              {/* Search bar */}
              <div className='relative'>
                <Search className='absolute left-3 top-3 h-4 w-4 text-muted-foreground' />
                <Input
                  placeholder='Search prompts...'
                  value={filters.query}
                  onChange={(e) =>
                    setFilters((prev) => ({ ...prev, query: e.target.value }))
                  }
                  className='pl-10'
                />
              </div>

              {/* Visibility tabs */}
              <Tabs
                value={filters.visibility}
                onValueChange={(value) =>
                  setFilters((prev) => ({
                    ...prev,
                    visibility: value as
                      | 'all'
                      | 'private'
                      | 'workspace'
                      | 'public',
                  }))
                }
              >
                <TabsList className='grid w-full grid-cols-4'>
                  <TabsTrigger value='all' className='text-xs'>
                    All
                  </TabsTrigger>
                  <TabsTrigger value='private' className='text-xs'>
                    <User className='w-3 h-3' />
                  </TabsTrigger>
                  <TabsTrigger value='workspace' className='text-xs'>
                    <Building2 className='w-3 h-3' />
                  </TabsTrigger>
                  <TabsTrigger value='public' className='text-xs'>
                    <Globe className='w-3 h-3' />
                  </TabsTrigger>
                </TabsList>
              </Tabs>

              {/* Tag filters */}
              {availableTags.length > 0 && (
                <div className='space-y-2'>
                  <div className='flex items-center justify-between'>
                    <span className='text-sm font-medium'>Tags</span>
                    {filters.tags.length > 0 && (
                      <Button
                        variant='ghost'
                        size='sm'
                        onClick={clearFilters}
                        className='h-6 px-2 text-xs'
                      >
                        Clear
                      </Button>
                    )}
                  </div>
                  <div className='flex flex-wrap gap-1 max-h-24 overflow-y-auto'>
                    {availableTags.map((tag) => (
                      <Badge
                        key={tag}
                        variant={
                          filters.tags.includes(tag) ? 'default' : 'outline'
                        }
                        className='cursor-pointer text-xs'
                        onClick={() => handleTagToggle(tag)}
                      >
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Results count */}
              <div className='text-sm text-muted-foreground'>
                {loading ? 'Loading...' : `${prompts.length} prompts`}
              </div>

              {/* Prompt list */}
              <ScrollArea className='flex-1'>
                <div
                  className={`${
                    selectedPrompt
                      ? 'space-y-2'
                      : 'grid grid-cols-1 md:grid-cols-2 gap-3'
                  } transition-all duration-200`}
                >
                  {prompts.map((prompt) => (
                    <PromptCard
                      key={prompt.id}
                      prompt={prompt}
                      onSelect={handleSelectPrompt}
                      onUse={handleUsePrompt}
                      onEdit={handleEditPrompt}
                      isSelected={selectedPrompt?.id === prompt.id}
                      compact={!selectedPrompt}
                    />
                  ))}

                  {!loading && prompts.length === 0 && (
                    <div className='col-span-full text-center py-12'>
                      <div className='mx-auto w-24 h-24 mb-4 rounded-full bg-muted flex items-center justify-center'>
                        <Search className='w-8 h-8 text-muted-foreground' />
                      </div>
                      <h3 className='font-medium text-lg mb-2'>
                        No prompts found
                      </h3>
                      <p className='text-muted-foreground mb-4'>
                        {filters.query || filters.tags.length > 0
                          ? 'Try adjusting your search criteria'
                          : 'Get started by creating your first prompt'}
                      </p>
                      <Button onClick={handleCreateNew}>
                        <Plus className='w-4 h-4 mr-2' />
                        Create First Prompt
                      </Button>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </div>

            {/* Right panel - Preview */}
            {selectedPrompt && (
              <div className='flex-1 border-l pl-6'>
                <div className='flex items-center gap-2 mb-4'>
                  <Button
                    variant='ghost'
                    size='sm'
                    onClick={() => setSelectedPrompt(null)}
                    className='p-1'
                  >
                    ← Back to Library
                  </Button>
                </div>
                <PromptPreview
                  prompt={selectedPrompt}
                  context={context}
                  onUse={() => handleUsePrompt(selectedPrompt)}
                  onEdit={() => handleEditPrompt(selectedPrompt)}
                />
              </div>
            )}

            {/* Floating Action Button for Create New Prompt */}
            <Button
              onClick={handleCreateNew}
              className='absolute bottom-6 right-6 w-14 h-14 rounded-full shadow-lg hover:scale-105 transition-transform'
              size='icon'
            >
              <Plus className='w-6 h-6' />
              <span className='sr-only'>New Prompt</span>
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Variable input dialog */}
      {showVariableDialog && selectedPrompt && (
        <VariableDialog
          isOpen={showVariableDialog}
          onClose={() => {
            setShowVariableDialog(false);
            setSelectedPrompt(null);
          }}
          prompt={selectedPrompt}
          context={context}
          onSubmit={handleVariableSubmit}
        />
      )}

      {/* Prompt editor drawer */}
      {showEditor && (
        <PromptEditorDrawer
          isOpen={showEditor}
          onClose={() => {
            setShowEditor(false);
            setEditingPrompt(null);
          }}
          prompt={editingPrompt}
          onSave={handlePromptSaved}
        />
      )}
    </>
  );
}
