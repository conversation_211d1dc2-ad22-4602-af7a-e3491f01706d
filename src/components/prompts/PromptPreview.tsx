'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import {
  Star,
  User,
  Globe,
  Building2,
  Edit,
  Copy,
  Eye,
  Code,
  Clock,
  Tag,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Tables } from '@/types/database.types';
import {
  parsePromptVariables,
  generateTemplatePreview,
  VariableResolutionContext,
} from '@/lib/promptVariables';

type Prompt = Tables<'prompts'>;

interface PromptPreviewProps {
  prompt: Prompt;
  context?: VariableResolutionContext;
  onUse: () => void;
  onEdit: () => void;
}

export function PromptPreview({
  prompt,
  context = {},
  onUse,
  onEdit,
}: PromptPreviewProps) {
  const [activeTab, setActiveTab] = useState<'preview' | 'raw'>('preview');

  const variables = parsePromptVariables(prompt.body_md);
  const preview = generateTemplatePreview(prompt.body_md, {}, context);
  const averageRating =
    prompt.rating_count > 0 ? prompt.rating_sum / prompt.rating_count : 0;

  const getVisibilityIcon = () => {
    switch (prompt.visibility) {
      case 'private':
        return <User className='w-4 h-4' />;
      case 'workspace':
        return <Building2 className='w-4 h-4' />;
      case 'public':
        return <Globe className='w-4 h-4' />;
      default:
        return null;
    }
  };

  const getVisibilityColor = () => {
    switch (prompt.visibility) {
      case 'private':
        return 'text-blue-600 bg-blue-50';
      case 'workspace':
        return 'text-green-600 bg-green-50';
      case 'public':
        return 'text-purple-600 bg-purple-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(prompt.body_md);
    } catch (error) {
      console.error('Failed to copy prompt:', error);
    }
  };

  const handleRating = async (rating: number) => {
    try {
      const response = await fetch(`/api/prompts/${prompt.id}/rate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ rating }),
      });

      if (!response.ok) {
        throw new Error('Failed to rate prompt');
      }

      // TODO: Update the prompt rating in the parent component
    } catch (error) {
      console.error('Error rating prompt:', error);
    }
  };

  return (
    <div className='h-full flex flex-col'>
      {/* Header */}
      <div className='space-y-4 pb-4 border-b'>
        <div className='flex items-start justify-between'>
          <div className='space-y-2 flex-1'>
            <h2 className='text-xl font-semibold leading-tight'>
              {prompt.title}
            </h2>

            <div className='flex items-center gap-3'>
              <div
                className={cn(
                  'flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium',
                  getVisibilityColor()
                )}
              >
                {getVisibilityIcon()}
                <span className='capitalize'>{prompt.visibility}</span>
              </div>

              <div className='flex items-center gap-1 text-sm text-muted-foreground'>
                <Clock className='w-3 h-3' />
                <span>v{prompt.version}</span>
              </div>

              <div className='text-sm text-muted-foreground'>
                {new Date(prompt.created_at).toLocaleDateString()}
              </div>
            </div>
          </div>

          <div className='flex gap-2'>
            <Button variant='outline' size='sm' onClick={handleCopy}>
              <Copy className='w-4 h-4 mr-2' />
              Copy
            </Button>
            <Button variant='outline' size='sm' onClick={onEdit}>
              <Edit className='w-4 h-4 mr-2' />
              Edit
            </Button>
            <Button onClick={onUse} size='sm' className='bg-primary'>
              Use Prompt
            </Button>
          </div>
        </div>

        {/* Rating */}
        {averageRating > 0 && (
          <div className='flex items-center gap-2'>
            <div className='flex items-center gap-1'>
              {[1, 2, 3, 4, 5].map((star) => (
                <Star
                  key={star}
                  className={cn(
                    'w-4 h-4 cursor-pointer transition-colors',
                    star <= Math.round(averageRating)
                      ? 'fill-yellow-400 text-yellow-400'
                      : 'text-gray-300 hover:text-yellow-400'
                  )}
                  onClick={() => handleRating(star)}
                />
              ))}
            </div>
            <span className='text-sm text-muted-foreground'>
              {averageRating.toFixed(1)} ({prompt.rating_count} rating
              {prompt.rating_count !== 1 ? 's' : ''})
            </span>
          </div>
        )}

        {/* Tags */}
        {prompt.tags && prompt.tags.length > 0 && (
          <div className='flex items-center gap-2'>
            <Tag className='w-4 h-4 text-muted-foreground' />
            <div className='flex flex-wrap gap-1'>
              {prompt.tags.map((tag) => (
                <Badge key={tag} variant='secondary' className='text-xs'>
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Content */}
      <div className='flex-1 flex flex-col'>
        <Tabs value={activeTab} onValueChange={(v) => setActiveTab(v as 'preview' | 'raw')}>
          <TabsList className='w-full mt-4'>
            <TabsTrigger value='preview' className='flex-1'>
              <Eye className='w-4 h-4 mr-2' />
              Preview
            </TabsTrigger>
            <TabsTrigger value='raw' className='flex-1'>
              <Code className='w-4 h-4 mr-2' />
              Raw Template
            </TabsTrigger>
          </TabsList>

          <TabsContent value='preview' className='flex-1 mt-4'>
            <ScrollArea className='h-full'>
              <div className='prose prose-sm max-w-none'>
                <div className='whitespace-pre-wrap text-sm leading-relaxed'>
                  {preview}
                </div>
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value='raw' className='flex-1 mt-4'>
            <ScrollArea className='h-full'>
              <div className='font-mono text-sm bg-muted p-4 rounded-lg'>
                <pre className='whitespace-pre-wrap'>{prompt.body_md}</pre>
              </div>
            </ScrollArea>
          </TabsContent>
        </Tabs>

        {/* Variables info */}
        {variables.length > 0 && (
          <>
            <Separator className='my-4' />
            <div className='space-y-2'>
              <h3 className='font-medium text-sm'>
                Variables ({variables.length})
              </h3>
              <div className='grid grid-cols-2 gap-2'>
                {variables.map((variable) => (
                  <div key={variable.name} className='text-sm'>
                    <code className='bg-muted px-1 py-0.5 rounded text-xs'>
                      {'{{'}
                      {variable.name}
                      {'}}'}
                    </code>
                    {variable.defaultValue && (
                      <span className='text-muted-foreground ml-2'>
                        Default: {variable.defaultValue}
                      </span>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
