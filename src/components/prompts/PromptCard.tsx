'use client';

import { useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuPortal,
} from '@/components/ui/dropdown-menu';
import {
  Star,
  User,
  Globe,
  Building2,
  MoreVertical,
  Edit,
  Trash2,
  Copy
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Tables } from '@/types/database.types';
import { parsePromptVariables } from '@/lib/promptVariables';

type Prompt = Tables<'prompts'>;

interface PromptCardProps {
  prompt: Prompt;
  onSelect: (prompt: Prompt) => void;
  onUse: (prompt: Prompt) => void;
  onEdit: (prompt: Prompt) => void;
  isSelected?: boolean;
  compact?: boolean;
}

export function PromptCard({
  prompt,
  onSelect,
  onUse,
  onEdit,
  isSelected = false,
  compact = false,
}: PromptCardProps) {
  const variables = parsePromptVariables(prompt.body_md);
  const averageRating =
    prompt.rating_count > 0 ? prompt.rating_sum / prompt.rating_count : 0;

  const getVisibilityIcon = () => {
    switch (prompt.visibility) {
      case 'private':
        return <User className='w-3 h-3' />;
      case 'workspace':
        return <Building2 className='w-3 h-3' />;
      case 'public':
        return <Globe className='w-3 h-3' />;
      default:
        return null;
    }
  };

  const getVisibilityColor = () => {
    switch (prompt.visibility) {
      case 'private':
        return 'text-blue-600';
      case 'workspace':
        return 'text-green-600';
      case 'public':
        return 'text-purple-600';
      default:
        return 'text-gray-600';
    }
  };

  const handleCopy = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(prompt.body_md);
    } catch (error) {
      console.error('Failed to copy prompt:', error);
    }
  }, [prompt.body_md]);

  return (
    <Card
      className={cn(
        'cursor-pointer transition-all hover:shadow-md',
        isSelected && 'ring-2 ring-primary',
        compact && 'hover:bg-muted'
      )}
      onClick={() => (compact ? onUse : onSelect)(prompt)}
    >
      <CardHeader className='pb-2'>
        <div className='flex items-start justify-between'>
          <div className='space-y-1 flex-1'>
            <h3 className='font-medium text-sm leading-tight'>
              {prompt.title}
            </h3>
            <div className='flex items-center gap-2'>
              <div
                className={cn('flex items-center gap-1', getVisibilityColor())}
              >
                {getVisibilityIcon()}
                <span className='text-xs capitalize'>{prompt.visibility}</span>
              </div>

              {averageRating > 0 && (
                <div className='flex items-center gap-1'>
                  <Star className='w-3 h-3 fill-yellow-400 text-yellow-400' />
                  <span className='text-xs text-muted-foreground'>
                    {averageRating.toFixed(1)} ({prompt.rating_count})
                  </span>
                </div>
              )}
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant='ghost'
                size='sm'
                className='h-6 w-6 p-0'
                onClick={(e) => e.stopPropagation()}
              >
                <MoreVertical className='w-3 h-3' />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuPortal>
              <DropdownMenuContent align='end'>
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    onUse(prompt);
                  }}
                >
                  Use Prompt
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    onEdit(prompt);
                  }}
                >
                  <Edit className='w-4 h-4 mr-2' />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCopy();
                  }}
                >
                  <Copy className='w-4 h-4 mr-2' />
                  Copy
                </DropdownMenuItem>
                <DropdownMenuItem
                  className='text-destructive'
                  onClick={(e) => {
                    e.stopPropagation(); /* TODO: Delete handler */
                  }}
                >
                  <Trash2 className='w-4 h-4 mr-2' />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenuPortal>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className='pt-0'>
        {/* Preview of prompt body */}
        <p className='text-xs text-muted-foreground line-clamp-2 mb-3'>
          {prompt.body_md.slice(0, 120)}
          {prompt.body_md.length > 120 && '...'}
        </p>

        {/* Tags */}
        {prompt.tags && prompt.tags.length > 0 && (
          <div className='flex flex-wrap gap-1 mb-3'>
            {prompt.tags.slice(0, 3).map((tag) => (
              <Badge
                key={tag}
                variant='secondary'
                className='text-xs px-1 py-0'
              >
                {tag}
              </Badge>
            ))}
            {prompt.tags.length > 3 && (
              <Badge variant='outline' className='text-xs px-1 py-0'>
                +{prompt.tags.length - 3} more
              </Badge>
            )}
          </div>
        )}

        {/* Variables indicator */}
        {variables.length > 0 && (
          <div className='text-xs text-muted-foreground mb-3'>
            {variables.length} variable{variables.length !== 1 ? 's' : ''}:{' '}
            {variables
              .slice(0, 2)
              .map((v) => v.name)
              .join(', ')}
            {variables.length > 2 && ` +${variables.length - 2} more`}
          </div>
        )}

        {/* Action buttons */}
        <div className='flex gap-2'>
          <Button
            size='sm'
            onClick={(e) => {
              e.stopPropagation();
              onUse(prompt);
            }}
            className='flex-1 h-7 text-xs'
          >
            Use Prompt
          </Button>
        </div>

        {/* Metadata */}
        <div className='text-xs text-muted-foreground mt-2 pt-2 border-t'>
          <div className='flex justify-between'>
            <span>v{prompt.version}</span>
            <span>{new Date(prompt.created_at).toLocaleDateString()}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
