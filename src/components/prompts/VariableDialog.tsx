'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  Di<PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Info, Wand2, Loader2 } from 'lucide-react';
import { Tables } from '@/types/database.types';
import {
  parsePromptVariables,
  resolvePromptVariables,
  generateTemplatePreview,
  VariableResolutionContext
} from '@/lib/promptVariables';
import { cn } from '@/lib/utils';

type Prompt = Tables<'prompts'>;

interface VariableDialogProps {
  isOpen: boolean;
  onClose: () => void;
  prompt: Prompt;
  context?: VariableResolutionContext;
  onSubmit: (values: Record<string, string>) => void | Promise<void>;
}

export function VariableDialog({
  isOpen,
  onClose,
  prompt,
  context = {},
  onSubmit,
}: VariableDialogProps) {
  const [values, setValues] = useState<Record<string, string>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const firstInputRef = React.useRef<HTMLTextAreaElement>(null);
  const hasUserEditedRef = React.useRef<Record<string, boolean>>({});

  const variables = useMemo(
    () => parsePromptVariables(prompt.body_md),
    [prompt.body_md]
  );
  const { missingVariables } = resolvePromptVariables(
    prompt.body_md,
    values,
    context
  );

  // Helper to get context value; used in initializing values
  const getContextValue = useCallback(
    (
      variableName: string,
      ctx: VariableResolutionContext
    ): string | undefined => {
      switch (variableName.toLowerCase()) {
        case 'conversation_title':
        case 'title':
          return ctx.conversationTitle;
        case 'last_message':
        case 'lastmessage':
          return ctx.lastMessage;
        case 'workspace':
          return ctx.workspaceContext?.name as string | undefined;
        default:
          return ctx.workspaceContext?.[variableName] as string | undefined;
      }
    },
    []
  );

  // Track if values have been initialized to prevent overwriting user input
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // Only initialize values once when dialog opens or when not yet initialized
    if (!isInitialized || !isOpen) {
      const initialValues: Record<string, string> = {};

      variables.forEach((variable) => {
        // Only set initial value if user hasn't edited this field
        if (!hasUserEditedRef.current[variable.name]) {
          // Try context first, then default value
          const ctxValue = context && getContextValue(variable.name, context);
          if (ctxValue) {
            initialValues[variable.name] = ctxValue;
          } else if (variable.defaultValue) {
            initialValues[variable.name] = variable.defaultValue;
          } else {
            initialValues[variable.name] = '';
          }
        }
      });

      // Only update values that haven't been edited by user
      setValues(prev => ({
        ...prev,
        ...initialValues
      }));

      if (isOpen) {
        setIsInitialized(true);
      }
    }
  }, [variables, context, getContextValue, isOpen, isInitialized]);

  // Reset initialization state and user edit tracking when dialog closes
  useEffect(() => {
    if (!isOpen) {
      setIsInitialized(false);
      hasUserEditedRef.current = {};
    }
  }, [isOpen]);

  // Auto-focus first input when dialog opens
  useEffect(() => {
    if (isOpen) {
      const timer = setTimeout(() => {
        firstInputRef.current?.focus();
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  const handleSubmit = useCallback(async () => {
    const newErrors: Record<string, string> = {};

    // Validate required variables
    variables.forEach((variable) => {
      if (!values[variable.name]?.trim() && !variable.defaultValue) {
        newErrors[variable.name] = 'This field is required';
      }
    });

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit(values);
    } finally {
      setIsSubmitting(false);
    }
  }, [variables, values, onSubmit]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        handleSubmit();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isOpen, handleSubmit]);

  const handleValueChange = useCallback(
    (variableName: string, value: string) => {
      // Mark this field as user-edited
      hasUserEditedRef.current[variableName] = true;

      setValues((prev) => ({
        ...prev,
        [variableName]: value,
      }));

      // Clear error when user starts typing
      setErrors((prev) => {
        if (prev[variableName]) {
          const newErrors = { ...prev };
          delete newErrors[variableName];
          return newErrors;
        }
        return prev;
      });
    },
    []
  );

  const isContextProvided = (variableName: string): boolean => {
    return !!getContextValue(variableName, context);
  };



  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-w-3xl max-h-[90vh] flex flex-col'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Wand2 className='w-5 h-5' />
            Configure Variables
          </DialogTitle>
        </DialogHeader>

        <div className='flex-1 flex gap-6 overflow-hidden'>
          {/* Left side - Variable inputs */}
          <div className='w-1/2 space-y-4'>
            <div className='flex items-center gap-2 text-sm text-muted-foreground'>
              <Info className='w-4 h-4' />
              Fill in the variables to customize your prompt
            </div>

            <ScrollArea className='h-[400px]'>
              <div className='space-y-4 pr-4'>
                {variables.map((variable, index) => (
                  <div key={variable.name} className='space-y-2'>
                    <div className='flex items-center justify-between'>
                      <Label htmlFor={variable.name} className='font-medium'>
                        {variable.name}
                        {!variable.defaultValue &&
                          !isContextProvided(variable.name) && (
                            <span className='text-destructive ml-1'>*</span>
                          )}
                      </Label>

                      <div className='flex gap-1'>
                        {isContextProvided(variable.name) && (
                          <Badge variant='secondary' className='text-xs'>
                            Auto-filled
                          </Badge>
                        )}
                        {variable.defaultValue && (
                          <Badge variant='outline' className='text-xs'>
                            Has default
                          </Badge>
                        )}
                      </div>
                    </div>

                    <Textarea
                      value={values[variable.name] || ''}
                      onChange={(e) => handleValueChange(variable.name, e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey && (e.metaKey || e.ctrlKey)) {
                          e.preventDefault();
                          handleSubmit();
                        }
                      }}
                      className={cn(
                        'min-h-[36px] max-h-[200px] resize-none',
                        '[&::-webkit-scrollbar]:hidden',
                        errors[variable.name] ? 'border-destructive' : ''
                      )}
                      style={{
                        height: 'auto',
                        paddingTop: '8px',
                        paddingBottom: '8px',
                        scrollbarWidth: 'none',
                        msOverflowStyle: 'none',
                      }}
                      disabled={isContextProvided(variable.name)}
                      ref={index === 0 && !isContextProvided(variable.name) ? firstInputRef : undefined}
                      onInput={(e) => {
                        const target = e.target as HTMLTextAreaElement;
                        target.style.height = 'auto';
                        target.style.height = `${Math.min(target.scrollHeight, 200)}px`;
                      }}
                      rows={1}
                    />

                    {errors[variable.name] && (
                      <p className='text-sm text-destructive'>
                        {errors[variable.name]}
                      </p>
                    )}

                    {variable.defaultValue && (
                      <p className='text-xs text-muted-foreground'>
                        Default: {variable.defaultValue}
                      </p>
                    )}

                    {index < variables.length - 1 && <Separator />}
                  </div>
                ))}

                {variables.length === 0 && (
                  <div className='text-center py-8 text-muted-foreground'>
                    No variables found in this prompt
                  </div>
                )}
              </div>
            </ScrollArea>
          </div>

          {/* Right side - Preview */}
          <div className='w-1/2 space-y-4'>
            <h3 className='font-medium'>Preview</h3>

            <ScrollArea className='h-[400px] [&>div>div]:!flex [&>div>div]:!flex-col'>
              <div className='bg-muted p-4 rounded-lg overflow-hidden'>
                <div className='text-sm leading-relaxed break-words overflow-wrap-anywhere whitespace-pre-wrap'>
                  {generateTemplatePreview(prompt.body_md, values, context)}
                </div>
              </div>
            </ScrollArea>

            {missingVariables.length > 0 && (
              <div className='space-y-2'>
                <p className='text-sm font-medium text-amber-600'>
                  Missing variables:
                </p>
                <div className='flex flex-wrap gap-1'>
                  {missingVariables.map((variable) => (
                    <Badge
                      key={variable.name}
                      variant='outline'
                      className='text-amber-600'
                    >
                      {variable.name}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant='outline' onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={missingVariables.length > 0 || isSubmitting}
          >
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Use Prompt
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
