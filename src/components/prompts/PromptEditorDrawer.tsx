'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
} from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ModelSelector } from '@/components/chat/ModelSelector/ModelSelector';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertD<PERSON>ogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { X, Plus, Eye, Code, AlertCircle, Check, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Tables } from '@/types/database.types';
import {
  validatePromptTemplate,
  generateTemplatePreview,
  parsePromptVariables,
} from '@/lib/promptVariables';
import { useWorkspaces } from '@/hooks/useWorkspaces';

type Prompt = Tables<'prompts'>;

interface PromptEditorDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  prompt?: Prompt | null;
  onSave: () => void;
}

interface FormData {
  title: string;
  body_md: string;
  tags: string[];
  default_model: string;
  visibility: 'private' | 'workspace' | 'public';
  workspace_id?: string;
}

export function PromptEditorDrawer({
  isOpen,
  onClose,
  prompt,
  onSave,
}: PromptEditorDrawerProps) {
  const [formData, setFormData] = useState<FormData>({
    title: '',
    body_md: '',
    tags: [],
    default_model: '',
    visibility: 'private',
  });

  const [newTag, setNewTag] = useState('');
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showDiscardDialog, setShowDiscardDialog] = useState(false);
  const [activeTab, setActiveTab] = useState<'edit' | 'preview'>('edit');

  const { workspaces } = useWorkspaces();

  // Initialize form with prompt data
  useEffect(() => {
    if (prompt) {
      setFormData({
        title: prompt.title,
        body_md: prompt.body_md,
        tags: prompt.tags || [],
        default_model: prompt.default_model || '',
        visibility: prompt.visibility as FormData['visibility'],
        workspace_id: prompt.workspace_id || undefined,
      });
    } else {
      setFormData({
        title: '',
        body_md: '',
        tags: [],
        default_model: '',
        visibility: 'private',
      });
    }
  }, [prompt]);

  const validation = validatePromptTemplate(formData.body_md);
  const preview = generateTemplatePreview(formData.body_md);
  const variables = parsePromptVariables(formData.body_md);

  const isFormDirty = () => {
    if (!prompt) {
      return formData.title || formData.body_md || formData.tags.length > 0;
    }

    return (
      formData.title !== prompt.title ||
      formData.body_md !== prompt.body_md ||
      JSON.stringify(formData.tags) !== JSON.stringify(prompt.tags || []) ||
      formData.default_model !== (prompt.default_model || '') ||
      formData.visibility !== prompt.visibility ||
      formData.workspace_id !== (prompt.workspace_id || undefined)
    );
  };

  const handleClose = () => {
    if (isFormDirty()) {
      setShowDiscardDialog(true);
    } else {
      // Add small delay to prevent focus scope conflicts
      setTimeout(() => {
        // Reset form state before closing
        setFormData({
          title: '',
          body_md: '',
          tags: [],
          default_model: '',
          visibility: 'private',
        });
        setErrors({});
        setActiveTab('edit');
        onClose();
      }, 50);
    }
  };

  const handleSave = async () => {
    setErrors({});

    // Validate form
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!formData.body_md.trim()) {
      newErrors.body_md = 'Prompt body is required';
    }

    if (!validation.isValid) {
      newErrors.body_md = validation.errors[0];
    }

    if (formData.visibility === 'workspace' && !formData.workspace_id) {
      newErrors.workspace_id = 'Workspace is required for workspace visibility';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setSaving(true);

    try {
      const method = prompt ? 'PATCH' : 'POST';
      const url = prompt ? `/api/prompts/${prompt.id}` : '/api/prompts';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to save prompt');
      }

      onSave();
    } catch (error) {
      console.error('Error saving prompt:', error);
      setErrors({
        general:
          error instanceof Error ? error.message : 'Failed to save prompt',
      });
    } finally {
      setSaving(false);
    }
  };

  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData((prev) => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()],
      }));
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData((prev) => ({
      ...prev,
      tags: prev.tags.filter((tag) => tag !== tagToRemove),
    }));
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault();
      handleSave();
    }
  };

  return (
    <>
      <Sheet
        key={isOpen ? 'open' : 'closed'}
        open={isOpen}
        modal={true}
        onOpenChange={(open) => {
          if (!open) {
            handleClose();
          }
        }}
      >
        <SheetContent
          side='right'
          className='w-full sm:max-w-3xl flex flex-col'
          onPointerDownOutside={(e) => {
            // Prevent closing on outside click if form is dirty
            if (isFormDirty()) {
              e.preventDefault();
              setShowDiscardDialog(true);
            }
          }}
          onEscapeKeyDown={(e) => {
            // Prevent closing on escape if form is dirty
            if (isFormDirty()) {
              e.preventDefault();
              setShowDiscardDialog(true);
            }
          }}
        >
          <SheetHeader>
            <SheetTitle>
              {prompt ? 'Edit Prompt' : 'Create New Prompt'}
            </SheetTitle>
          </SheetHeader>

          <div
            className='flex-1 flex flex-col overflow-hidden'
            onKeyDown={handleKeyDown}
          >
            <Tabs
              value={activeTab}
              onValueChange={(v) => setActiveTab(v as 'edit' | 'preview')}
            >
              <TabsList className='w-full mb-4'>
                <TabsTrigger value='edit' className='flex-1'>
                  <Code className='w-4 h-4 mr-2' />
                  Edit
                </TabsTrigger>
                <TabsTrigger value='preview' className='flex-1'>
                  <Eye className='w-4 h-4 mr-2' />
                  Preview
                </TabsTrigger>
              </TabsList>

              <TabsContent value='edit' className='flex-1 overflow-hidden'>
                <ScrollArea className='h-full'>
                  <div className='space-y-6 pr-4'>
                    {/* Title */}
                    <div className='space-y-2'>
                      <Label htmlFor='title'>Title *</Label>
                      <Input
                        id='title'
                        value={formData.title}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            title: e.target.value,
                          }))
                        }
                        className={errors.title ? 'border-destructive' : ''}
                        placeholder='Enter a descriptive title...'
                      />
                      {errors.title && (
                        <p className='text-sm text-destructive'>
                          {errors.title}
                        </p>
                      )}
                    </div>

                    {/* Visibility and Workspace */}
                    <div className='grid grid-cols-2 gap-4'>
                      <div className='space-y-2'>
                        <Label htmlFor='visibility'>Visibility *</Label>
                        <Select
                          value={formData.visibility}
                          onValueChange={(value) =>
                            setFormData((prev) => ({
                              ...prev,
                              visibility: value as FormData['visibility'],
                              workspace_id:
                                value !== 'workspace'
                                  ? undefined
                                  : prev.workspace_id,
                            }))
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value='private'>Private</SelectItem>
                            <SelectItem value='workspace'>Workspace</SelectItem>
                            <SelectItem value='public'>Public</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      {formData.visibility === 'workspace' && (
                        <div className='space-y-2'>
                          <Label htmlFor='workspace'>Workspace *</Label>
                          <Select
                            value={formData.workspace_id || ''}
                            onValueChange={(value) =>
                              setFormData((prev) => ({
                                ...prev,
                                workspace_id: value,
                              }))
                            }
                          >
                            <SelectTrigger
                              className={
                                errors.workspace_id ? 'border-destructive' : ''
                              }
                            >
                              <SelectValue placeholder='Select workspace...' />
                            </SelectTrigger>
                            <SelectContent>
                              {workspaces.map((workspace) => (
                                <SelectItem
                                  key={workspace.id}
                                  value={workspace.id}
                                >
                                  {workspace.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          {errors.workspace_id && (
                            <p className='text-sm text-destructive'>
                              {errors.workspace_id}
                            </p>
                          )}
                        </div>
                      )}
                    </div>

                    {/* Default Model */}
                    <div className='space-y-2'>
                      <Label htmlFor='default_model'>
                        Default Model (Optional)
                      </Label>
                      <ModelSelector
                        value={formData.default_model || null}
                        onChange={(modelId) =>
                          setFormData((prev) => ({
                            ...prev,
                            default_model: modelId || '',
                          }))
                        }
                        variant='popover'
                        buttonVariant='outline'
                        placeholder='Select a model for this prompt'
                        className='w-full'
                      />
                    </div>

                    {/* Tags */}
                    <div className='space-y-2'>
                      <Label>Tags</Label>
                      <div className='flex gap-2'>
                        <Input
                          value={newTag}
                          onChange={(e) => setNewTag(e.target.value)}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault();
                              handleAddTag();
                            }
                          }}
                          placeholder='Add a tag...'
                          className='flex-1'
                        />
                        <Button type='button' onClick={handleAddTag} size='sm'>
                          <Plus className='w-4 h-4' />
                        </Button>
                      </div>

                      {formData.tags.length > 0 && (
                        <div className='flex flex-wrap gap-1'>
                          {formData.tags.map((tag) => (
                            <Badge
                              key={tag}
                              variant='secondary'
                              className='text-xs'
                            >
                              {tag}
                              <button
                                type='button'
                                onClick={() => handleRemoveTag(tag)}
                                className='ml-1 hover:text-destructive'
                              >
                                <X className='w-3 h-3' />
                              </button>
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>

                    <Separator />

                    {/* Prompt Body */}
                    <div className='space-y-2'>
                      <Label htmlFor='body_md'>Prompt Template *</Label>
                      <Textarea
                        id='body_md'
                        value={formData.body_md}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            body_md: e.target.value,
                          }))
                        }
                        className={cn(
                          'min-h-[200px] font-mono text-sm',
                          errors.body_md ? 'border-destructive' : ''
                        )}
                        placeholder='Enter your prompt template here...

Use {{variable_name}} for variables
Use {{variable_name|default_value}} for variables with defaults'
                      />
                      {errors.body_md && (
                        <p className='text-sm text-destructive'>
                          {errors.body_md}
                        </p>
                      )}
                    </div>

                    {/* Validation feedback */}
                    {formData.body_md && (
                      <div className='space-y-2'>
                        {validation.isValid ? (
                          <div className='flex items-center gap-2 text-green-600'>
                            <Check className='w-4 h-4' />
                            <span className='text-sm'>Template is valid</span>
                          </div>
                        ) : (
                          <div className='flex items-center gap-2 text-destructive'>
                            <AlertCircle className='w-4 h-4' />
                            <span className='text-sm'>Template has errors</span>
                          </div>
                        )}

                        {variables.length > 0 && (
                          <div className='text-sm text-muted-foreground'>
                            Variables found:{' '}
                            {variables.map((v) => v.name).join(', ')}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </TabsContent>

              <TabsContent value='preview' className='flex-1 overflow-hidden'>
                <ScrollArea className='h-full'>
                  <div className='space-y-4'>
                    <div className='bg-muted p-4 rounded-lg'>
                      <div className='whitespace-pre-wrap text-sm leading-relaxed'>
                        {preview || 'Enter a prompt template to see preview...'}
                      </div>
                    </div>

                    {variables.length > 0 && (
                      <div className='space-y-2'>
                        <h3 className='font-medium text-sm'>
                          Variables ({variables.length})
                        </h3>
                        <div className='grid grid-cols-2 gap-2'>
                          {variables.map((variable) => (
                            <div key={variable.name} className='text-sm'>
                              <code className='bg-muted px-1 py-0.5 rounded text-xs'>
                                {'{{'}
                                {variable.name}
                                {'}}'}
                              </code>
                              {variable.defaultValue && (
                                <span className='text-muted-foreground ml-2'>
                                  Default: {variable.defaultValue}
                                </span>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </TabsContent>
            </Tabs>
          </div>

          <SheetFooter className='border-t pt-4'>
            {errors.general && (
              <p className='text-sm text-destructive mb-2'>{errors.general}</p>
            )}

            <div className='flex gap-2 w-full'>
              <Button
                variant='outline'
                onClick={handleClose}
                className='flex-1'
              >
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                disabled={saving || !validation.isValid}
                className='flex-1'
              >
                {saving && <Loader2 className='w-4 h-4 mr-2 animate-spin' />}
                {prompt ? 'Update' : 'Create'}
              </Button>
            </div>
          </SheetFooter>
        </SheetContent>
      </Sheet>

      <AlertDialog open={showDiscardDialog} onOpenChange={setShowDiscardDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Discard changes?</AlertDialogTitle>
            <AlertDialogDescription>
              You have unsaved changes. Are you sure you want to close without
              saving?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Continue Editing</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                setShowDiscardDialog(false);
                // Add small delay to prevent focus scope conflicts
                setTimeout(() => {
                  // Reset form state before closing
                  setFormData({
                    title: '',
                    body_md: '',
                    tags: [],
                    default_model: '',
                    visibility: 'private',
                  });
                  setErrors({});
                  setActiveTab('edit');
                  onClose();
                }, 50);
              }}
            >
              Discard Changes
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
