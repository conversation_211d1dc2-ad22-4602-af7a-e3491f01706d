'use client';
import { ReactNode, useEffect } from 'react';
import { initializeBuilderStore } from '@/stores/builderStore';

interface BuilderProviderProps {
  children: ReactNode;
}

export function BuilderProvider({ children }: BuilderProviderProps) {
  // Initialize the store with sample data once when the provider mounts
  useEffect(() => {
    initializeBuilderStore();
  }, []);

  return <>{children}</>;
}
