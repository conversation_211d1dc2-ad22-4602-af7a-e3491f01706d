'use client';

import { useEffect, Suspense } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import { AnalyticsService } from '@/lib/analytics/service';
import { useContext } from 'react';
import { UserContext } from './AuthProvider';

// Separate component for page tracking that uses useSearchParams
function PageViewTracker() {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Track page views (optional)
  useEffect(() => {
    // Don't track sensitive URLs or sensitive parameters
    // This is a simple implementation - enhance as needed
    const shouldTrack = !(
      pathname.includes('/search') || searchParams.has('query')
    );

    if (shouldTrack) {
      AnalyticsService.track('Page View', {
        path: pathname,
      });
    }
  }, [pathname, searchParams]);

  return null;
}

export default function AnalyticsProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const { user } = useContext(UserContext);

  // Initialize analytics once on client-side
  useEffect(() => {
    AnalyticsService.init();
  }, []);

  // Identify user when authenticated
  useEffect(() => {
    if (user) {
      AnalyticsService.identify(user.id, {
        email: user.email,
        // Use user properties that are available from Supabase Auth
        createdAt: user.created_at,
      });
    }
  }, [user]);

  // Reset analytics when user signs out
  useEffect(() => {
    if (!user) {
      AnalyticsService.reset();
    }
  }, [user]);

  return (
    <>
      <Suspense fallback={null}>
        <PageViewTracker />
      </Suspense>
      {children}
    </>
  );
}
