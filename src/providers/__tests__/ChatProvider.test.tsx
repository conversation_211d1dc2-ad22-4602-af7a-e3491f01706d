import React from 'react';
import { render, screen, act } from '@testing-library/react';
import { Chat<PERSON>rovider, ChatContext } from '../ChatProvider';
import '@testing-library/jest-dom';
import { UrlCitationAnnotation } from '@/lib/supabase/types';
import { UserContext } from '../AuthProvider';

// Store the original UserContext for reference in our mock
const originalUserContext = UserContext;

// Mock the UserContext provider
jest.mock('../AuthProvider', () => {
  const actualAuthProvider = jest.requireActual('../AuthProvider');
  return {
    ...actualAuthProvider,
    // Keep the original UserContext export
    UserContext: actualAuthProvider.UserContext,
  };
});

// Mock the React.useContext
const originalUseContext = React.useContext;
jest.spyOn(React, 'useContext').mockImplementation((context) => {
  // When accessing UserContext, return mock values
  if (context === originalUserContext) {
    return {
      me: { id: 'test-user-id' },
      user: { id: 'test-user-id' },
      isAuthenticated: true,
      signOut: jest.fn(),
      fetchMe: jest.fn(),
    };
  }

  // For any other context, use the original
  return originalUseContext(context);
});

// Mock the SubscriptionProvider
jest.mock('../SubscriptionProvider', () => ({
  useSubscription: () => ({
    accessibleModelIds: ['model-1', 'model-2'],
  }),
}));

// Mock logger
jest.mock('@/lib/logger', () => ({
  logger: {
    child: () => ({
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
    }),
  },
}));

// Mock UUID generation
jest.mock('uuid', () => ({
  v4: () => 'test-uuid',
}));

// Create a test component that uses the ChatContext
const TestComponent = () => {
  const {
    partialMessages,
    getPartialMessage,
    clearPartialMessage,
    subscribeToStream,
    unsubscribeFromStream,
  } = React.useContext(ChatContext);

  return (
    <div>
      <div data-testid='partial-message-count'>{partialMessages.size}</div>
      <button
        data-testid='subscribe-button'
        onClick={() => {
          subscribeToStream(
            'test-message-id',
            'test-conversation-id',
            'test-model',
            false,
            false,
            false,
            () => {},
            () => {},
            () => {},
            () => {},
            () => {},
          );
        }}
      >
        Subscribe
      </button>
      <button
        data-testid='unsubscribe-button'
        onClick={() => {
          unsubscribeFromStream('test-message-id');
        }}
      >
        Unsubscribe
      </button>
      <button
        data-testid='get-message-button'
        onClick={() => {
          const message = getPartialMessage('test-message-id');
          if (message) {
            document.getElementById('message-content')!.textContent =
              message.content;
          }
        }}
      >
        Get Message
      </button>
      <button
        data-testid='clear-message-button'
        onClick={() => {
          clearPartialMessage('test-message-id');
        }}
      >
        Clear Message
      </button>
      <div id='message-content' data-testid='message-content'></div>
    </div>
  );
};

describe('ChatProvider - Partial Messages', () => {
  // Define a type for our mocked event
  type MockSSEEvent = {
    data: string;
  };

  // Mock EventSource for SSE testing
  let mockEventSource: {
    addEventListener: jest.Mock;
    close: jest.Mock;
    onerror: null;
  };

  beforeEach(() => {
    // Mock the EventSource class
    mockEventSource = {
      addEventListener: jest.fn(),
      close: jest.fn(),
      onerror: null,
    };

    // @ts-expect-error - Mocking global EventSource
    global.EventSource = jest.fn(() => mockEventSource);

    // Clear any timeouts
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  test('should store and retrieve partial messages', async () => {
    render(
      <ChatProvider>
        <TestComponent />
      </ChatProvider>
    );

    // Check initial state
    expect(screen.getByTestId('partial-message-count')).toHaveTextContent('0');

    // Subscribe to a stream
    act(() => {
      screen.getByTestId('subscribe-button').click();
    });

    // Verify EventSource was created
    expect(global.EventSource).toHaveBeenCalled();

    // Simulate receiving delta events
    // Find the callback for 'delta' events
    const deltaCallback = mockEventSource.addEventListener.mock.calls.find(
      (call: [string, (event: MockSSEEvent) => void]) => call[0] === 'delta'
    )[1];

    // Trigger the callback with simulated data
    act(() => {
      deltaCallback({
        data: JSON.stringify({
          type: 'delta',
          content: 'Hello, ',
        }),
      });
    });

    // Simulate receiving more data
    act(() => {
      deltaCallback({
        data: JSON.stringify({
          type: 'delta',
          content: 'world!',
        }),
      });
    });

    // Check if partial message was stored
    act(() => {
      screen.getByTestId('get-message-button').click();
    });

    // Content should be combined
    expect(screen.getByTestId('message-content')).toHaveTextContent(
      'Hello, world!'
    );

    // Count should be 1
    expect(screen.getByTestId('partial-message-count')).toHaveTextContent('1');

    // Test annotation storage
    const annotationCallback = mockEventSource.addEventListener.mock.calls.find(
      (call: [string, (event: MockSSEEvent) => void]) =>
        call[0] === 'annotation'
    )[1];

    const testAnnotation: UrlCitationAnnotation = {
      type: 'url_citation',
      url: 'https://example.com',
      title: 'Example Site',
      start_index: 0,
      end_index: 5,
    };

    act(() => {
      annotationCallback({
        data: JSON.stringify({
          type: 'annotation',
          annotation: testAnnotation,
        }),
      });
    });

    // Verify message is cleared when done
    const doneCallback = mockEventSource.addEventListener.mock.calls.find(
      (call: [string, (event: MockSSEEvent) => void]) => call[0] === 'done'
    )[1];

    // Trigger the done callback
    act(() => {
      doneCallback();
    });

    // The message should still be in the store because of the setTimeout
    expect(screen.getByTestId('partial-message-count')).toHaveTextContent('1');

    // Advance the timer past the 3-second delay
    act(() => {
      jest.advanceTimersByTime(4000);
    });

    // Now the count should be 0
    expect(screen.getByTestId('partial-message-count')).toHaveTextContent('0');
  });

  test('should handle unsubscribing from streams', () => {
    render(
      <ChatProvider>
        <TestComponent />
      </ChatProvider>
    );

    // Subscribe to a stream
    act(() => {
      screen.getByTestId('subscribe-button').click();
    });

    // Simulate receiving data
    const deltaCallback = mockEventSource.addEventListener.mock.calls.find(
      (call: [string, (event: MockSSEEvent) => void]) => call[0] === 'delta'
    )[1];

    act(() => {
      deltaCallback({
        data: JSON.stringify({
          type: 'delta',
          content: 'Test content',
        }),
      });
    });

    // Unsubscribe from the stream
    act(() => {
      screen.getByTestId('unsubscribe-button').click();
    });

    // Verify EventSource was closed
    expect(mockEventSource.close).toHaveBeenCalled();

    // Manually clear the message since we didn't get a 'done' event
    act(() => {
      screen.getByTestId('clear-message-button').click();
    });

    // Count should be 0
    expect(screen.getByTestId('partial-message-count')).toHaveTextContent('0');
  });
});
