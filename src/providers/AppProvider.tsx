'use client';
import { createContext, useState, useEffect } from 'react';
import { useLocalStorage } from 'react-use';
import { isMobile as isMobileDevice } from 'react-device-detect';

type AppContextType = {
  isTestMode: boolean;
  setIsTestMode: (isTestMode: boolean) => void;
  isSidebarOpen: boolean;
  isMobile: boolean;
  viewportHeight: number;
  toggleSidebar: () => void;
};

export const AppContext = createContext<AppContextType>({
  isTestMode: false,
  setIsTestMode: () => {},
  isSidebarOpen: false,
  isMobile: false,
  viewportHeight: 0,
  toggleSidebar: () => {},
});

export function AppProvider({ children }: { children: React.ReactNode }) {
  const [isMounted, setIsMounted] = useState(false);
  const [isTestModeState, setIsTestModeState] = useState(
    process.env.NODE_ENV === 'development'
  );
  const [isTestModeStored, setIsTestModeStored] = useLocalStorage(
    'isTestMode',
    process.env.NODE_ENV === 'development'
  );
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [viewportHeight, setViewportHeight] = useState(0);

  useEffect(() => {
    setIsMounted(true);
    if (isTestModeStored !== undefined) {
      setIsTestModeState(isTestModeStored);
    }
  }, []);

  const setIsTestMode = (value: boolean) => {
    setIsTestModeState(value);
    setIsTestModeStored(value);
  };

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  useEffect(() => {
    const checkViewport = () => {
      const vh = window.innerHeight;
      setViewportHeight(vh);
    };

    checkViewport();

    window.addEventListener('resize', checkViewport);
    return () => {
      window.removeEventListener('resize', checkViewport);
    };
  }, []);

  const isMobile = isMounted ? isMobileDevice : false;

  if (!isMounted) {
    return (
      <AppContext.Provider
        value={{
          isTestMode: process.env.NODE_ENV === 'development',
          setIsTestMode: () => {},
          isSidebarOpen,
          isMobile,
          viewportHeight,
          toggleSidebar,
        }}
      >
        {children}
      </AppContext.Provider>
    );
  }

  return (
    <AppContext.Provider
      value={{
        isTestMode: isTestModeState,
        setIsTestMode,
        isSidebarOpen,
        isMobile,
        viewportHeight,
        toggleSidebar,
      }}
    >
      {children}
    </AppContext.Provider>
  );
}
