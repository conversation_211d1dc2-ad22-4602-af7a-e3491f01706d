'use client';
import { User } from '@supabase/supabase-js';
import { createContext, useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { authService } from '@/lib/supabase/auth';
import { SubscriptionProvider } from './SubscriptionProvider';
import { Me } from '@/lib/supabase/types';
import { useAnalytics } from '@/hooks/useAnalytics';

export const UserContext = createContext<{
  user: User | null;
  isAuthenticated: boolean;
  signOut: () => Promise<void>;
  me: Me | null;
  fetchMe: () => Promise<void>;
}>({
  user: null,
  isAuthenticated: false,
  signOut: async () => {},
  me: null,
  fetchMe: async () => {},
});

export default function AuthProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const router = useRouter();
  const [me, setMe] = useState<Me | null>(null);
  const analytics = useAnalytics();

  const fetchMe = async () => {
    const response = await fetch('/api/me');
    const data = await response.json();
    setMe(data.me);
  };

  useEffect(() => {
    fetchMe();
  }, []);

  useEffect(() => {
    const {
      data: { subscription },
    } = authService.onAuthStateChange((event, session) => {
      if (session) {
        setUser(session.user);
        setIsAuthenticated(true);
      } else {
        setUser(null);
        setIsAuthenticated(false);
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const signOut = async () => {
    if (user) {
      // Track sign-out event before actually signing out
      analytics.trackEvent('User Signed Out');
    }

    await authService.signOut();
    router.push('/auth/login');
  };

  return (
    <UserContext.Provider
      value={{ user, isAuthenticated, signOut, me, fetchMe }}
    >
      <SubscriptionProvider>{children}</SubscriptionProvider>
    </UserContext.Provider>
  );
}
