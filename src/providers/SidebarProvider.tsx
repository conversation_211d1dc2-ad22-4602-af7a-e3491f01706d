'use client';
import { createContext, useEffect, useState, useContext } from 'react';
import { AppContext } from './AppProvider';
import { AppProgressBar as ProgressBar } from 'next-nprogress-bar';

export type SidebarState = 'full' | 'minimized' | 'hidden';

type SidebarContextType = {
  sidebarState: SidebarState;
  setSidebarState: (state: SidebarState) => void;
  toggleSidebarState: () => void;
  saveCollapseSidebarState: (state: SidebarState) => void;
  collapseSidebarState: SidebarState;
  isPeeking: boolean;
  setIsPeeking: (isPeeking: boolean) => void;
};

export const SidebarContext = createContext<SidebarContextType>({
  sidebarState: 'full',
  setSidebarState: () => void 0,
  toggleSidebarState: () => void 0,
  saveCollapseSidebarState: () => void 0,
  collapseSidebarState: 'minimized',
  isPeeking: false,
  setIsPeeking: () => void 0,
});

export const SidebarProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const { isMobile } = useContext(AppContext);
  const [sidebarState, setSidebarState] = useState<SidebarState>('hidden');
  const [collapseSidebarState, setCollapseSidebarState] =
    useState<SidebarState>('minimized');
  const [isPeeking, setIsPeeking] = useState<boolean>(false);

  useEffect(() => {
    const collapseSidebarState = localStorage.getItem('collapseSidebarState');
    const sidebarState = localStorage.getItem('sidebarState');
    if (collapseSidebarState) {
      setCollapseSidebarState(collapseSidebarState as SidebarState);
    }
    if (sidebarState) {
      setSidebarState(sidebarState as SidebarState);
    }
  }, []);

  const toggleSidebarState = () => {
    if (isMobile) {
      if (sidebarState === 'hidden') {
        setIsPeeking(true);
      } else {
        setSidebarState('hidden');
        setIsPeeking(false);
      }
    } else {
      const newSidebarState =
        sidebarState === 'full' ? collapseSidebarState : 'full';
      setSidebarState(newSidebarState);
      localStorage.setItem('sidebarState', newSidebarState);
    }
  };

  const saveCollapseSidebarState = (state: SidebarState) => {
    setCollapseSidebarState(state);
    localStorage.setItem('collapseSidebarState', state);
  };

  return (
    <SidebarContext.Provider
      value={{
        sidebarState,
        setSidebarState,
        toggleSidebarState,
        saveCollapseSidebarState,
        collapseSidebarState,
        isPeeking,
        setIsPeeking,
      }}
    >
      {children}
      <ProgressBar
        height="4px"
        color="#fffd00"
        options={{ showSpinner: false }}
        shallowRouting
      />
    </SidebarContext.Provider>
  );
};
