'use client';
import React, { createContext, useContext, useState, useEffect } from 'react';
import { useLocalStorage } from 'react-use';
// Import a fallback theme statically
import atomDark from 'react-syntax-highlighter/dist/esm/styles/prism/atom-dark';
import codeThemes from '@/lib/code-themes.json';
import { logger } from '@/lib/logger';

const log = logger.child({
  module: 'CodeThemeProvider',
});

export type PrismTheme = { [key: string]: React.CSSProperties };

// Create a cache for loaded themes
const themeCache: { [key: string]: PrismTheme } = {
  'atom-dark': atomDark, // Pre-populate with default theme
};

const getTheme = async (themeId: string): Promise<PrismTheme> => {
  // Return from cache if available
  if (themeCache[themeId]) {
    return themeCache[themeId];
  }

  // Find theme in configuration
  const theme = codeThemes.themes.find((t) => t.id === themeId);
  if (!theme) {
    log.warn(`Theme ${themeId} not found, using default`);
    return atomDark;
  }

  try {
    const module = await import(
      `react-syntax-highlighter/dist/esm/styles/prism/${themeId}`
    );
    const themeStyle = module.default;

    if (!themeStyle) {
      log.warn(`Theme ${themeId} not found in module, using default`);
      return atomDark;
    }

    // Cache the result
    themeCache[themeId] = themeStyle;
    return themeStyle;
  } catch (error) {
    log.error(`Failed to load theme ${themeId}:`, error);
    return atomDark;
  }
};

interface CodeThemeContextType {
  currentTheme: PrismTheme;
  isLoading: boolean;
  codeTheme: string;
  setCodeTheme: (theme: string) => void;
}

const CodeThemeContext = createContext<CodeThemeContextType>({
  currentTheme: atomDark,
  isLoading: false,
  codeTheme: 'atom-dark',
  setCodeTheme: () => {},
});

export const useCodeTheme = () => useContext(CodeThemeContext);

export const CodeThemeProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [codeTheme, setCodeTheme] = useLocalStorage('codeTheme', 'atom-dark');
  const [currentTheme, setCurrentTheme] = useState<PrismTheme>(atomDark);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const loadTheme = async () => {
      setIsLoading(true);
      try {
        const themeToUse = codeTheme || 'atom-dark';
        const loadedTheme = await getTheme(themeToUse);
        setCurrentTheme(loadedTheme);
      } catch (error) {
        log.error('Error loading theme:', error);
        // Fallback to atom-dark if there's an error
        setCurrentTheme(atomDark);
      } finally {
        setIsLoading(false);
      }
    };

    loadTheme();
  }, [codeTheme]);

  return (
    <CodeThemeContext.Provider
      value={{
        currentTheme,
        isLoading,
        codeTheme: codeTheme ?? 'atom-dark',
        setCodeTheme,
      }}
    >
      {children}
    </CodeThemeContext.Provider>
  );
};
