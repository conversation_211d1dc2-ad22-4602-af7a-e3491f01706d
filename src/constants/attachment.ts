export const MAX_FILE_SIZE_MB = 10; // Increased max size slightly
export const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024;
// Updated allowed file types
export const ALLOWED_FILE_TYPES = [
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/webp',
  // 'text/plain',
  // 'text/markdown', // Common MD type
  'application/pdf',
  // Add more as needed, e.g., 'text/csv', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
];
