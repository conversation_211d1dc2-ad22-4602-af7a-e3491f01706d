import { useState, useCallback, useMemo } from 'react';

export interface UseBulkSelectionOptions<T> {
  /** Function to extract unique ID from each item */
  getId: (item: T) => string;
  /** Initial items to manage selection for */
  items: T[];
}

export function useBulkSelection<T>({
  getId,
  items,
}: UseBulkSelectionOptions<T>) {
  const [selectedIds, setSelectedIds] = useState<Set<string>>(new Set());

  // Derived state
  const selectedItems = useMemo(
    () => items.filter((item) => selectedIds.has(getId(item))),
    [items, selectedIds, getId]
  );

  const isSelected = useCallback(
    (item: T) => selectedIds.has(getId(item)),
    [selectedIds, getId]
  );

  const isAllSelected = useMemo(
    () =>
      items.length > 0 && items.every((item) => selectedIds.has(getId(item))),
    [items, selectedIds, getId]
  );

  const isPartiallySelected = useMemo(
    () => selectedIds.size > 0 && !isAllSelected,
    [selectedIds.size, isAllSelected]
  );

  // Actions
  const selectItem = useCallback(
    (item: T) => {
      setSelectedIds((prev) => new Set(prev).add(getId(item)));
    },
    [getId]
  );

  const deselectItem = useCallback(
    (item: T) => {
      setSelectedIds((prev) => {
        const newSet = new Set(prev);
        newSet.delete(getId(item));
        return newSet;
      });
    },
    [getId]
  );

  const toggleItem = useCallback(
    (item: T) => {
      const id = getId(item);
      setSelectedIds((prev) => {
        const newSet = new Set(prev);
        if (newSet.has(id)) {
          newSet.delete(id);
        } else {
          newSet.add(id);
        }
        return newSet;
      });
    },
    [getId]
  );

  const selectAll = useCallback(() => {
    setSelectedIds(new Set(items.map(getId)));
  }, [items, getId]);

  const deselectAll = useCallback(() => {
    setSelectedIds(new Set());
  }, []);

  const toggleAll = useCallback(() => {
    if (isAllSelected) {
      deselectAll();
    } else {
      selectAll();
    }
  }, [isAllSelected, selectAll, deselectAll]);

  const selectMultiple = useCallback(
    (itemsToSelect: T[]) => {
      setSelectedIds((prev) => {
        const newSet = new Set(prev);
        itemsToSelect.forEach((item) => newSet.add(getId(item)));
        return newSet;
      });
    },
    [getId]
  );

  const deselectMultiple = useCallback(
    (itemsToDeselect: T[]) => {
      setSelectedIds((prev) => {
        const newSet = new Set(prev);
        itemsToDeselect.forEach((item) => newSet.delete(getId(item)));
        return newSet;
      });
    },
    [getId]
  );

  return {
    // State
    selectedIds,
    selectedItems,
    selectedCount: selectedIds.size,

    // Checks
    isSelected,
    isAllSelected,
    isPartiallySelected,
    hasSelection: selectedIds.size > 0,

    // Actions
    selectItem,
    deselectItem,
    toggleItem,
    selectAll,
    deselectAll,
    toggleAll,
    selectMultiple,
    deselectMultiple,

    // Direct access to setter for advanced use cases
    setSelectedIds,
  };
}
