import { useState, useEffect } from 'react';

export interface WorkspaceInfo {
  id: string;
  name: string;
  icon?: string;
  description?: string;
}

export function useWorkspaceInfo(conversationId?: string) {
  const [workspace, setWorkspace] = useState<WorkspaceInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!conversationId) {
      setWorkspace(null);
      setError(null);
      return;
    }

    const fetchWorkspaceInfo = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch(
          `/api/groupConversations/${conversationId}/workspace`
        );
        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Failed to fetch workspace info');
        }

        setWorkspace(data.data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
        setWorkspace(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchWorkspaceInfo();
  }, [conversationId]);

  return { workspace, isLoading, error };
}
