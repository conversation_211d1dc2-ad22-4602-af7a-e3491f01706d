import { useMemo } from 'react';
import { AnalyticsService, AnalyticsEvent } from '@/lib/analytics/service';

export function useAnalytics() {
  return useMemo(() => ({
    /**
     * Track an event with optional properties
     */
    trackEvent: (event: AnalyticsEvent | string, properties?: Record<string, unknown>) => {
      AnalyticsService.track(event, properties);
    },

    /**
     * Helper methods for specific events
     */
    trackMessageSent: (
      conversationId: string,
      messageId: string,
      modelUsed: string,
      characterCount: number,
      tokenCount?: number
    ) => {
      AnalyticsService.track(AnalyticsEvent.MESSAGE_SENT, {
        conversationId,
        messageId,
        modelUsed,
        characterCount,
        ...(tokenCount ? { tokenCount } : {}),
      });
    },

    trackMessageEdited: (conversationId: string, messageId: string) => {
      AnalyticsService.track(AnalyticsEvent.MESSAGE_EDITED, {
        conversationId,
        messageId,
      });
    },

    trackMessageRetried: (lastUserMessageId: string, model: string) => {
      AnalyticsService.track(AnalyticsEvent.MESSAGE_RETRIED, {
        lastUserMessageId,
        model,
      });
    },

    trackConversationRenamed: (conversationId: string) => {
      AnalyticsService.track(AnalyticsEvent.CONVERSATION_RENAMED, {
        conversationId,
      });
    },

    trackConversationFavorited: (conversationId: string, favorited: boolean) => {
      AnalyticsService.track(AnalyticsEvent.CONVERSATION_FAVORITED, {
        conversationId,
        favorited,
      });
    },

    trackConversationArchived: (conversationId: string) => {
      AnalyticsService.track(AnalyticsEvent.CONVERSATION_ARCHIVED, {
        conversationId,
      });
    },

    trackConversationDeleted: (conversationId: string) => {
      AnalyticsService.track(AnalyticsEvent.CONVERSATION_DELETED, {
        conversationId,
      });
    },

    trackShareLinkCreated: (conversationId: string, shareLinkId: string) => {
      AnalyticsService.track(AnalyticsEvent.SHARE_LINK_CREATED, {
        conversationId,
        shareLinkId,
      });
    },

    trackModelChanged: (newModel: string, previousModel: string, conversationId?: string) => {
      AnalyticsService.track(AnalyticsEvent.MODEL_CHANGED, {
        newModel,
        previousModel,
        ...(conversationId ? { conversationId } : {}),
      });
    },

    trackSettingsChanged: (settingName: string, newValue: unknown) => {
      // Avoid tracking sensitive values
      let trackedValue = newValue;

      // If the setting involves API keys or sensitive info, don't track the actual value
      if (
        settingName.toLowerCase().includes('key') ||
        settingName.toLowerCase().includes('token') ||
        settingName.toLowerCase().includes('secret')
      ) {
        trackedValue = '[REDACTED]';
      }

      AnalyticsService.track(AnalyticsEvent.SETTINGS_CHANGED, {
        settingName,
        newValue: trackedValue,
      });
    },
  }), []);
}