import { useState, useEffect } from 'react';

interface Workspace {
  id: string;
  name: string;
  icon?: string | null;
  description?: string | null;
}

export function useWorkspaces() {
  const [workspaces, setWorkspaces] = useState<Workspace[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchWorkspaces();
  }, []);

  async function fetchWorkspaces() {
    // Skip fetch in test environment or when fetch is not available
    if (typeof fetch === 'undefined' || process.env.NODE_ENV === 'test') {
      setWorkspaces([]);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/workspaces');
      if (!response.ok) {
        throw new Error('Failed to fetch workspaces');
      }

      const data = await response.json();
      setWorkspaces(data.workspaces || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch workspaces');
      console.error('Error fetching workspaces:', err);
    } finally {
      setIsLoading(false);
    }
  }

  return {
    workspaces,
    isLoading,
    error,
    refetch: fetchWorkspaces,
  };
}