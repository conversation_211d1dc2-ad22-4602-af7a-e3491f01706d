import { useEffect, useState, useRef } from 'react';
import { debounce } from '@/utils/debounce';

export function useVisibilityChange(callback: () => void, debounceMs = 500) {
  const [isVisible, setIsVisible] = useState(() => {
    // Check if we're in the browser environment
    if (typeof window !== 'undefined' && typeof document !== 'undefined') {
      return !document.hidden;
    }
    return true; // Default to visible during SSR
  });

  // Store the callback in a ref to avoid recreating the debounced function
  const callbackRef = useRef(callback);
  callbackRef.current = callback;

  // Create debounced function that uses the ref
  const debouncedCallback = useRef(
    debounce(() => {
      callbackRef.current();
    }, debounceMs)
  );

  // Only recreate debounced function if debounceMs changes
  useEffect(() => {
    debouncedCallback.current = debounce(() => {
      callbackRef.current();
    }, debounceMs);
  }, [debounceMs]);

  useEffect(() => {
    // Only set up event listeners in the browser environment
    if (typeof window === 'undefined' || typeof document === 'undefined') {
      return;
    }

    const handleVisibilityChange = () => {
      const visible = !document.hidden;
      setIsVisible(visible);

      // Only trigger callback when page becomes visible
      if (visible) {
        debouncedCallback.current();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []); // Remove debouncedCallback from dependencies since we're using ref

  return isVisible;
}
