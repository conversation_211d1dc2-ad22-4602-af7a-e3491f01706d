import { renderHook, act } from '@testing-library/react';
import { useVisibilityChange } from '../useVisibilityChange';

// Mock the debounce utility
jest.mock('@/utils/debounce', () => ({
  debounce: jest.fn((fn) => fn), // Return the function directly for testing
}));

describe('useVisibilityChange', () => {
  let mockCallback: jest.Mock;
  let mockAddEventListener: jest.SpyInstance;
  let mockRemoveEventListener: jest.SpyInstance;

  beforeEach(() => {
    mockCallback = jest.fn();
    mockAddEventListener = jest.spyOn(document, 'addEventListener');
    mockRemoveEventListener = jest.spyOn(document, 'removeEventListener');

    // Mock document.hidden
    Object.defineProperty(document, 'hidden', {
      writable: true,
      value: false,
    });

    // Ensure we're in a browser-like environment
    Object.defineProperty(window, 'window', {
      value: window,
      writable: true,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
    mockAddEventListener.mockRestore();
    mockRemoveEventListener.mockRestore();
  });

  test('should set up visibility change listener', () => {
    renderHook(() => useVisibilityChange(mockCallback));

    expect(mockAddEventListener).toHaveBeenCalledWith(
      'visibilitychange',
      expect.any(Function)
    );
  });

  test('should clean up listener on unmount', () => {
    const { unmount } = renderHook(() => useVisibilityChange(mockCallback));

    unmount();

    expect(mockRemoveEventListener).toHaveBeenCalledWith(
      'visibilitychange',
      expect.any(Function)
    );
  });

  test('should return initial visibility state', () => {
    const { result } = renderHook(() => useVisibilityChange(mockCallback));

    expect(result.current).toBe(true); // !document.hidden = !false = true
  });

  test('should call callback when page becomes visible', () => {
    renderHook(() => useVisibilityChange(mockCallback));

    // Get the event listener that was added
    const visibilityChangeHandler = mockAddEventListener.mock.calls[0][1];

    // Simulate page becoming visible
    act(() => {
      Object.defineProperty(document, 'hidden', { value: false });
      visibilityChangeHandler();
    });

    expect(mockCallback).toHaveBeenCalled();
  });

  test('should not call callback when page becomes hidden', () => {
    renderHook(() => useVisibilityChange(mockCallback));

    // Get the event listener that was added
    const visibilityChangeHandler = mockAddEventListener.mock.calls[0][1];

    // Simulate page becoming hidden
    act(() => {
      Object.defineProperty(document, 'hidden', { value: true });
      visibilityChangeHandler();
    });

    expect(mockCallback).not.toHaveBeenCalled();
  });

  test.skip('should handle SSR environment gracefully', () => {
    // Test that the hook works when document.hidden is not available
    // This simulates the SSR scenario where document properties might not exist

    // Temporarily remove the hidden property to simulate SSR
    const originalHidden = Object.getOwnPropertyDescriptor(document, 'hidden');
    Object.defineProperty(document, 'hidden', {
      get: () => undefined,
      configurable: true,
    });

    const { result } = renderHook(() => useVisibilityChange(mockCallback));

    // Should default to visible when document.hidden is undefined (SSR-like behavior)
    expect(result.current).toBe(true);

    // Should still set up event listeners (since we're in a browser-like test environment)
    expect(mockAddEventListener).toHaveBeenCalledWith(
      'visibilitychange',
      expect.any(Function)
    );

    // Restore the original property
    if (originalHidden) {
      Object.defineProperty(document, 'hidden', originalHidden);
    }
  });
});
