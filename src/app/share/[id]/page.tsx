'use client';

import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import MarkDownView from '@/components/chat/MarkDownView';
import { Message } from '@/lib/supabase/types';
import { Skeleton } from '@/components/ui/skeleton';
import Link from 'next/link';
import { LinkIcon, MessageSquare, LogIn } from 'lucide-react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { authService } from '@/lib/supabase/auth';

// Define types for shared conversations
interface SharedMessage extends Omit<Message, 'conversation_id'> {
  shared_conversation_id: string;
  parent_shared_message_id: string | null;
}

interface SharedConversation {
  id: string;
  title: string;
  created_at: string;
  messages: SharedMessage[];
}

export default function SharedConversationPage() {
  const params = useParams();
  const shareId = params.id as string;
  const router = useRouter();

  const [conversation, setConversation] = useState<SharedConversation | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const [isContinuing, setIsContinuing] = useState(false);

  useEffect(() => {
    // Check authentication status
    const checkAuth = async () => {
      try {
        const { data } = await authService.getSession();
        setIsAuthenticated(!!data.session);
      } catch (err) {
        console.error('Error checking auth status:', err);
        setIsAuthenticated(false);
      }
    };

    checkAuth();
  }, []);

  useEffect(() => {
    const fetchSharedConversation = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/shares/${shareId}`);

        if (!response.ok) {
          throw new Error('Failed to fetch shared conversation');
        }

        const data = await response.json();
        setConversation(data);
      } catch (err) {
        console.error('Error fetching shared conversation:', err);
        setError(
          'This shared conversation does not exist or has been deleted.'
        );
      } finally {
        setLoading(false);
      }
    };

    fetchSharedConversation();
  }, [shareId]);

  const copyShareLink = () => {
    const url = window.location.href;
    navigator.clipboard.writeText(url);
    toast.success('Share link copied to clipboard');
  };

  const handleContinueConversation = async () => {
    let loadingToast: string | number | undefined;
    try {
      setIsContinuing(true);
      // Show immediate feedback with toast
      loadingToast = toast.loading('Importing conversation...');

      // Call the import-shared API endpoint to import the conversation
      const response = await fetch('/api/conversations/import-shared', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ shareId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to import conversation');
      }

      const data = await response.json();
      toast.success('Conversation imported successfully');
      // Navigate to the chat page with the new or existing conversation ID
      router.push(`/chat/${data.conversationId}`);
    } catch (error) {
      console.error('Error importing conversation:', error);
      toast.error('Failed to continue conversation. Please try again.');
    } finally {
      if (loadingToast) {
        toast.dismiss(loadingToast);
      }
      setIsContinuing(false);
    }
  };

  const handleSignIn = async () => {
    try {
      // Store the shareId in localStorage before initiating the sign-in flow
      localStorage.setItem('pendingShareImport', shareId);
      await authService.signInWithGoogle();
    } catch (error) {
      console.error('Error signing in:', error);
      toast.error('Failed to sign in. Please try again.');
    }
  };

  return (
    <div className='min-h-screen bg-background relative'>
      <header className='sticky top-0 z-20 border-b border-border bg-background/80 backdrop-blur-sm'>
        <div className='container flex items-center justify-between h-16 px-4 gap-4'>
          <Link
            href='/'
            className='font-semibold hover:text-primary transition-colors flex-shrink-0'
          >
            SabiChat
          </Link>
          <div className='flex-1 text-center truncate'>
            {loading ? (
              <Skeleton className='h-5 w-48 mx-auto bg-slate-200' />
            ) : conversation ? (
              <span className='text-sm font-medium text-muted-foreground'>
                {conversation.title}
              </span>
            ) : null}
          </div>
          <Button
            variant='outline'
            size='sm'
            onClick={copyShareLink}
            className='flex items-center gap-1 flex-shrink-0'
          >
            <LinkIcon className='h-4 w-4' />
            <span>Copy link</span>
          </Button>
        </div>
      </header>

      <main className='container max-w-4xl mx-auto px-4 py-8 pb-24'>
        {loading ? (
          <LoadingState />
        ) : error ? (
          <ErrorState error={error} />
        ) : conversation ? (
          <div className='space-y-6'>
            <Card className='border-none bg-card/50 shadow-sm'>
              <CardContent className='pt-6'>
                <div className='space-y-5'>
                  {conversation.messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex flex-col ${
                        message.role === 'user' ? 'items-end' : 'items-start'
                      }`}
                    >
                      {/* <span className='text-xs text-muted-foreground mb-1 px-1'>
                        {message.role === 'user' ? 'You' : 'Assistant'}
                      </span> */}
                      <div
                        className={`p-3 rounded-2xl shadow-sm ${
                          message.role === 'user'
                            ? 'bg-user-bubble-bg text-user-bubble-fg rounded-br-none max-w-[80%]'
                            : 'bg-assistant-bubble-bg text-assistant-bubble-fg rounded-bl-none max-w-[80%]'
                        }`}
                      >
                        {message.content && (
                          <div
                            className='prose dark:prose-invert max-w-none text-sm break-words'
                            style={{ overscrollBehaviorX: 'auto' }}
                          >
                            <MarkDownView
                              message={{
                                ...message,
                                conversation_id: conversation.id,
                                children: [],
                                modelData: null,
                                annotations: message.annotations || [],
                              }}
                              annotations={message.annotations || []}
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        ) : null}
      </main>

      {!loading && !error && conversation && (
        <div className='fixed bottom-0 left-0 right-0 z-10 bg-background border-t border-border py-3 px-4 shadow-lg'>
          <div className='container max-w-4xl mx-auto'>
            <div className='flex flex-row items-center justify-between gap-4'>
              <p className='text-sm text-muted-foreground hidden sm:block'>
                {isAuthenticated
                  ? 'Continue this conversation or start a new one.'
                  : 'Sign in to SabiChat to continue this conversation.'}
              </p>
              <div className='flex-1 sm:flex-none'>
                {isAuthenticated ? (
                  <Button
                    onClick={handleContinueConversation}
                    className='flex items-center gap-2 w-full sm:w-auto'
                    size='lg'
                    disabled={isContinuing}
                  >
                    {isContinuing ? (
                      <span className='flex items-center gap-2'>
                        <svg
                          className='animate-spin h-4 w-4 text-white'
                          xmlns='http://www.w3.org/2000/svg'
                          fill='none'
                          viewBox='0 0 24 24'
                        >
                          <circle className='opacity-25' cx='12' cy='12' r='10' stroke='currentColor' strokeWidth='4'></circle>
                          <path className='opacity-75' fill='currentColor' d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'></path>
                        </svg>
                        Importing...
                      </span>
                    ) : (
                      <>
                        <MessageSquare className='h-4 w-4' />
                        Continue conversation
                      </>
                    )}
                  </Button>
                ) : (
                  <Button
                    onClick={handleSignIn}
                    className='flex items-center gap-2 w-full sm:w-auto'
                    size='lg'
                  >
                    <LogIn className='h-4 w-4' />
                    Try SabiChat
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

function LoadingState() {
  return (
    <div className='space-y-6'>
      <Card className='border-none bg-card/50 shadow-sm'>
        <CardContent className='min-h-[500px] pt-6'>
          <div className='flex flex-col space-y-4'>
            {[
              {
                align: 'start',
                width: 'w-3/5',
                labelWidth: 'w-16',
                bubbleHeight: 'h-16',
              },
              {
                align: 'end',
                width: 'w-1/2',
                labelWidth: 'w-10',
                bubbleHeight: 'h-12',
              },
              {
                align: 'start',
                width: 'w-4/6',
                labelWidth: 'w-16',
                bubbleHeight: 'h-20',
              },
              {
                align: 'end',
                width: 'w-2/5',
                labelWidth: 'w-10',
                bubbleHeight: 'h-16',
              },
            ].map((item, index) => (
              <div
                key={index}
                className={`flex flex-col ${item.width} ${
                  item.align === 'start' ? 'self-start' : 'self-end'
                }`}
              >
                {/* <Skeleton className={`h-4 ${item.labelWidth} mb-1 rounded self-${item.align} bg-slate-200`} /> */}
                <Skeleton
                  className={`${item.bubbleHeight} w-full rounded-2xl bg-slate-200`}
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function ErrorState({ error }: { error: string }) {
  return (
    <div className='flex flex-col items-center justify-center py-12 text-center'>
      <h2 className='text-2xl font-bold mb-4'>Conversation Not Found</h2>
      <p className='text-muted-foreground mb-6'>{error}</p>
      <Link href='/'>
        <Button>Return to SabiChat</Button>
      </Link>
    </div>
  );
}
