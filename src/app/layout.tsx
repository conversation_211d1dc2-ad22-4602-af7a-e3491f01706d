import React from 'react';
import type { Metadata } from 'next';
import { Geist, <PERSON>eist_Mono } from 'next/font/google';
import { ThemeProvider } from '@/components/theme-provider';
import { Toaster } from '@/components/ui/sonner';
import { ChatProvider } from '@/providers/ChatProvider';

import './globals.css';
import AuthProvider from '@/providers/AuthProvider';
import { SidebarProvider } from '@/providers/SidebarProvider';
import { CodeThemeProvider as SyntaxThemeProvider } from '@/providers/CodeThemeProvider';
import { AppProvider } from '@/providers/AppProvider';
import ShareImportHandler from '@/components/ShareImportHandler';
import AnalyticsProvider from '@/providers/AnalyticsProvider';
import { BuilderProvider } from '@/providers/BuilderProvider';
import { BrowserTelemetry } from '@/services/instrumentation/honeycombClient';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'Sabi Chat',
  description: 'Sabi de ting',
  icons: {
    icon: '/favicon.ico',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <BrowserTelemetry />
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
        <AppProvider>
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
            storageKey="theme"
          >
            <SyntaxThemeProvider>
              <AuthProvider>
                <ChatProvider>
                  <SidebarProvider>
                    <AnalyticsProvider>
                      <BuilderProvider>
                        <ShareImportHandler />
                        {children}
                      </BuilderProvider>
                    </AnalyticsProvider>
                  </SidebarProvider>
                </ChatProvider>
                <Toaster />
              </AuthProvider>
            </SyntaxThemeProvider>
          </ThemeProvider>
        </AppProvider>
      </body>
    </html>
  );
}
