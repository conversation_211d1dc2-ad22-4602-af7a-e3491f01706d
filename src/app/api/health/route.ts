import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { logger } from '@/lib/logger';

const log = logger.child({ api: '/api/health' });

export async function GET() {
  try {
    const startTime = Date.now();

    // Check database connectivity
    const supabase = await createClient();
    const { error } = await supabase
      .from('llm_models')
      .select('id')
      .limit(1)
      .single();

    if (error) {
      log.error('Database health check failed', { error });
      return NextResponse.json(
        {
          status: 'unhealthy',
          timestamp: new Date().toISOString(),
          checks: {
            database: 'failed',
            error: error.message
          }
        },
        { status: 503 }
      );
    }

    const responseTime = Date.now() - startTime;

    return NextResponse.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '0.1.0',
      environment: process.env.NODE_ENV || 'development',
      checks: {
        database: 'ok',
        responseTime: `${responseTime}ms`
      },
      uptime: process.uptime()
    });

  } catch (error) {
    log.error('Health check failed', { error });
    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: 'Internal server error'
      },
      { status: 500 }
    );
  }
}
