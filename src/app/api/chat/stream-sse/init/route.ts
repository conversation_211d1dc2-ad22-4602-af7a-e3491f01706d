import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { DatabaseService, Conversation, Message } from '@/lib/supabase/db';
import { logger } from '@/lib/logger';
import { AppError, ErrorCode, handleError } from '@/lib/error';
import {
  canUserSendMessage,
  canUserAccessModel,
  incrementUserDailyMessageCount,
} from '@/lib/supabase/subscription';
import { QuotaService } from '@/services/quota';
import {
  TokenizerService,
  SupportedModel,
} from '@/services/tokenizer/tokenizer';
import { AttachmentMetadata, GroupConversation } from '@/lib/supabase/types';
import { LLMService } from '@/services/llm/service';

interface ChatRequestPayload {
  conversationId?: string;
  message: string;
  provider: string;
  model: string;
  groupConversationId?: string;
  comparisonIndex?: number;
  isComparison?: boolean;
  isTestMode: boolean;
  parentMessageId?: string;
  attachments?: Array<AttachmentMetadata>;
  useWebSearch?: boolean;
  useImageGeneration?: boolean;
  isTemporary?: boolean;
  messageId?: string;
  newContent?: string;
  isEdit?: boolean;
  workspaceId?: string;
}

function createErrorResponse(message: string, status = 400) {
  return NextResponse.json({ error: message }, { status });
}

async function generateGroupConversationTitle(
  db: DatabaseService,
  groupConversationId: string,
  message: string,
  isTestMode: boolean
) {
  const log = logger.child({ groupConversationId, message, isTestMode });
  try {
    const llm = LLMService.getInstance();
    const groupConversation =
      await db.getGroupConversation(groupConversationId);
    if (!groupConversation) {
      log.warn('Group conversation not found');
      return;
    }

    const title = await llm.generateGroupConversationTitle(message, isTestMode);

    if (title) {
      db.updateGroupConversation(groupConversationId, {
        title,
      });
    }
  } catch (error) {
    log.error('Error generating group conversation title', error);
  }
}

async function manageConversation(
  db: DatabaseService,
  {
    conversationId,
    userId,
    model,
    groupConversationId,
    comparisonIndex,
    isComparison,
    isTemporary,
    workspaceId,
  }: {
    conversationId?: string;
    userId: string;
    model: string;
    groupConversationId?: string;
    comparisonIndex?: number | null;
    isComparison?: boolean;
    isTemporary?: boolean;
    workspaceId?: string;
  }
): Promise<Conversation | null> {
  const llmModel = await db.getModel(model);
  if (!llmModel) {
    throw new AppError(
      `Invalid model: ${model}`,
      ErrorCode.INVALID_REQUEST,
      400
    );
  }

  if (conversationId) {
    // Existing conversation
    const conversation = await db.getConversation(conversationId);
    if (!conversation || conversation.user_id !== userId) {
      return null; // Unauthorized or not found
    }

    // Update the last model used if it changed
    if (conversation.last_model_used !== llmModel.id) {
      await db.updateConversation(conversationId, {
        last_model_used: llmModel.id,
      });
    }

    return conversation;
  } else {
    // New conversation
    const newGroupId = groupConversationId || crypto.randomUUID();
    let groupConversation: GroupConversation | null = null;

    // Only create/update a group conversation if needed
    if (!groupConversationId) {
      // Brand new conversation, create a new group
      groupConversation = await db.upsertGroupConversation(userId, {
        id: newGroupId,
        title: 'New Conversation',
        is_comparison: isComparison || false,
        is_temporary: isTemporary || false,
        workspace_id: workspaceId,
      });
    } else {
      // Using an existing group ID, verify it exists and belongs to user
      groupConversation = await db.getGroupConversation(groupConversationId);
      if (!groupConversation || groupConversation.user_id !== userId) {
        // If it doesn't exist or doesn't belong to user, create it
        groupConversation = await db.upsertGroupConversation(userId, {
          id: newGroupId,
          title: 'New Conversation',
          is_comparison: isComparison || false,
          is_temporary: isTemporary || false,
          workspace_id: workspaceId,
        });
      }
    }

    // Create the conversation
    return db.createConversation(userId, {
      group_conversation_id: groupConversation?.id || newGroupId,
      last_model_used: llmModel.id,
      comparison_index: comparisonIndex !== undefined ? comparisonIndex : null,
    });
  }
}

// Common code for both POST and PUT handlers
async function handleStreamInit(
  req: Request,
  isUpdate: boolean = false
): Promise<Response> {
  const requestId = crypto.randomUUID();
  const log = logger.child({ requestId, update: isUpdate });

  try {
    const supabase = await createClient();
    const db = DatabaseService.getInstance(supabase);

    const payload: ChatRequestPayload = await req.json();

    if (isUpdate) {
      // Update (edit/retry) path
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { messageId, model, isTestMode, newContent, isEdit, useWebSearch } =
        payload;

      if (!messageId) {
        log.warn('Message ID is required');
        return createErrorResponse('Message ID is required', 400);
      }

      const user = await db.getCurrentUser();
      if (!user) {
        log.warn('Unauthorized access attempt');
        return createErrorResponse('Unauthorized', 401);
      }

      const llmModel = await db.getModel(model);
      if (!llmModel) {
        throw new AppError(
          `Invalid model: ${model}`,
          ErrorCode.INVALID_REQUEST,
          400
        );
      }

      const canAccessModel = await canUserAccessModel(user.id, llmModel.id);
      if (!canAccessModel) {
        throw new AppError(
          'This model requires a premium subscription',
          ErrorCode.API_UNAUTHORIZED,
          403
        );
      }

      if (useWebSearch && !llmModel.allows_search) {
        throw new AppError(
          `Web search is not supported by the selected model (${llmModel.display_name}).`,
          ErrorCode.INVALID_REQUEST,
          400
        );
      }

      const message = await db.getMessage(messageId);
      if (!message) {
        log.warn('Message not found');
        return createErrorResponse('Message not found', 404);
      }

      const conversationId = message.conversation_id;
      if (!conversationId) {
        return createErrorResponse('Invalid message structure', 400);
      }

      // Update model if needed
      if (llmModel.id !== message.conversation?.last_model_used) {
        await db.updateConversation(conversationId, {
          last_model_used: llmModel.id,
        });
      }

      let userMessage: Message;

      // For edits, create a new user message with the updated content
      if (isEdit && newContent) {
        userMessage = await db.createMessage({
          conversationId: conversationId,
          content: newContent,
          role: 'user',
          parentMessageId: message.parent_message_id as string,
          modelId: llmModel.id,
          providerId: llmModel.provider_id as string,
        });
      } else {
        // For retries, use the existing message
        userMessage = message;
      }

      // Create placeholder assistant message
      const assistantMessageId = crypto.randomUUID();
      await db.createMessage({
        id: assistantMessageId,
        conversationId: conversationId,
        content: '', // Empty content initially
        role: 'assistant',
        parentMessageId: userMessage.id,
        modelId: llmModel.id,
        providerId: llmModel.provider_id as string,
      });

      return NextResponse.json({
        conversationId,
        assistantMessageId,
        userMessageId: userMessage.id,
      });
    } else {
      // New message path (original POST implementation)
      const {
        conversationId,
        message,
        model,
        groupConversationId,
        comparisonIndex,
        isComparison,
        isTestMode,
        parentMessageId,
        attachments,
        useWebSearch,
        isTemporary,
        workspaceId,
      } = payload;

      const isNewConversation = !conversationId;

      const user = await db.getCurrentUser();
      if (!user) {
        log.warn('Unauthorized access attempt');
        return createErrorResponse('Unauthorized', 401);
      }

      const llmModel = await db.getModel(model);
      if (!llmModel) {
        throw new AppError(
          `Invalid model: ${model}`,
          ErrorCode.INVALID_REQUEST,
          400
        );
      }

      const canAccessModel = await canUserAccessModel(user.id, llmModel.id);
      if (!canAccessModel) {
        throw new AppError(
          'This model requires a premium subscription',
          ErrorCode.API_UNAUTHORIZED,
          403
        );
      }

      if (useWebSearch && !llmModel.allows_search) {
        throw new AppError(
          `Web search is not supported by the selected model (${llmModel.display_name}).`,
          ErrorCode.INVALID_REQUEST,
          400
        );
      }

      // Check quota limits (message limits for free tier, token limits for paid tiers)
      const { allowed, reason } = await canUserSendMessage(user.id);
      if (!allowed) {
        throw new AppError(
          reason || 'You have reached your daily message limit',
          ErrorCode.API_RESOURCE_EXHAUSTED,
          403
        );
      }

      // Check token quota for paid tiers
      const quotaService = QuotaService.getInstance();
      const tokenQuota = await quotaService.checkTokenQuota(user.id);

      if (tokenQuota.tokenQuota !== null && !tokenQuota.canUse) {
        // User is on a paid tier and over quota
        const fallback = await quotaService.handleTokenOverage(user.id);

        if (fallback.reason === 'quota_exceeded' && fallback.modelId) {
          // Redirect to fallback model for starter tier
          throw new AppError(
            `Token quota exceeded. Please use ${fallback.modelId} or upgrade your plan.`,
            ErrorCode.API_RESOURCE_EXHAUSTED,
            403
          );
        } else if (fallback.reason === 'overage_available') {
          // Premium tier - offer overage options
          throw new AppError(
            'Token quota exceeded. You can purchase additional tokens or upgrade your plan.',
            ErrorCode.API_RESOURCE_EXHAUSTED,
            403
          );
        } else {
          throw new AppError(
            'Token quota exceeded. Please upgrade your plan.',
            ErrorCode.API_RESOURCE_EXHAUSTED,
            403
          );
        }
      }

      // Estimate tokens for the upcoming conversation
      if (tokenQuota.tokenQuota !== null) {
        try {
          const tokenizerService = TokenizerService.getInstance();
          const estimatedTokens = await tokenizerService.countTokens(
            message,
            llmModel.name as SupportedModel
          );

          // Check if estimated tokens would exceed quota
          if (tokenQuota.tokensUsed + estimatedTokens > tokenQuota.tokenQuota) {
            const fallback = await quotaService.handleTokenOverage(user.id);

            if (fallback.reason === 'quota_exceeded' && fallback.modelId) {
              throw new AppError(
                `This message would exceed your token quota. Please use ${fallback.modelId} or upgrade your plan.`,
                ErrorCode.API_RESOURCE_EXHAUSTED,
                403
              );
            } else if (fallback.reason === 'overage_available') {
              throw new AppError(
                'This message would exceed your token quota. You can purchase additional tokens or upgrade your plan.',
                ErrorCode.API_RESOURCE_EXHAUSTED,
                403
              );
            }
          }
        } catch (tokenError) {
          log.warn('Failed to estimate tokens', { error: tokenError });
          // Continue if token estimation fails
        }
      }

      // Check comparison quota for premium users
      if (isComparison) {
        const comparisonQuota = await quotaService.checkComparisonQuota(
          user.id
        );
        if (!comparisonQuota.canUse) {
          throw new AppError(
            `Daily comparison limit reached (${comparisonQuota.limit}). Upgrade to Premium for more comparisons.`,
            ErrorCode.API_RESOURCE_EXHAUSTED,
            403
          );
        }

        // Increment comparison usage
        await quotaService.incrementComparisonUsage(user.id);
      }

      await incrementUserDailyMessageCount(user.id);

      // Validate group conversation ID if provided
      let validatedGroupConversationId = groupConversationId;
      if (groupConversationId) {
        const existingGroup =
          await db.getGroupConversation(groupConversationId);
        if (!existingGroup || existingGroup.user_id !== user.id) {
          // If group doesn't exist or doesn't belong to user, generate a new one
          validatedGroupConversationId = undefined;
        }
      }

      const conversation = await manageConversation(db, {
        conversationId,
        userId: user.id,
        model,
        groupConversationId: validatedGroupConversationId,
        comparisonIndex,
        isComparison,
        isTemporary,
        workspaceId,
      });

      if (isNewConversation && conversation?.group_conversation_id) {
        generateGroupConversationTitle(
          db,
          conversation.group_conversation_id,
          message,
          isTestMode
        );
      }

      if (!conversation) {
        log.warn('Conversation not found or unauthorized');
        return createErrorResponse(
          'Conversation not found or unauthorized',
          404
        );
      }

      // Create user message
      const userMessage = await db.createMessage({
        conversationId: conversation.id,
        content: message,
        role: 'user',
        parentMessageId,
        attachments,
      });

      // Create the placeholder assistant message
      const assistantMessageId = crypto.randomUUID();
      await db.createMessage({
        id: assistantMessageId,
        conversationId: conversation.id,
        content: '', // Empty content initially
        role: 'assistant',
        parentMessageId: userMessage.id,
        modelId: llmModel.id,
        providerId: llmModel.provider_id as string,
      });

      // Get the group conversation to check if it's temporary
      const groupConversation = await db.getGroupConversation(
        conversation.group_conversation_id
      );
      const isTemp = groupConversation?.is_temporary || false;

      return NextResponse.json({
        conversationId: conversation.id,
        groupConversationId: conversation.group_conversation_id,
        assistantMessageId: assistantMessageId,
        userMessageId: userMessage.id,
        isNewConversation,
        isTemporary: isTemp,
      });
    }
  } catch (error) {
    log.error({ error }, 'Error processing chat request');
    const appError = error instanceof AppError ? error : handleError(error);
    return createErrorResponse(appError.message, appError.statusCode);
  }
}

export async function POST(req: Request): Promise<Response> {
  return handleStreamInit(req, false);
}

export async function PUT(req: Request): Promise<Response> {
  return handleStreamInit(req, true);
}
