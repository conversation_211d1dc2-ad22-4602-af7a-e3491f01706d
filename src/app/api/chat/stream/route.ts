import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import {
  DatabaseService,
  Conversation,
  Message,
  Model,
} from '@/lib/supabase/db';
import { LLMService } from '@/services/llm/service';
import {
  TokenizerService,
  SupportedModel,
} from '@/services/tokenizer/tokenizer';
import { logger } from '@/lib/logger';
import { AppError, ErrorCode, handleError } from '@/lib/error';
import { User } from '@supabase/supabase-js';
import {
  Message as LLMMessage,
  MessageContentPart,
} from '@/services/llm/types';
import {
  canUserSendMessage,
  canUserAccessModel,
  incrementUserDailyMessageCount,
} from '@/lib/supabase/subscription';
import {
  AttachmentMetadata,
  UrlCitationAnnotation,
} from '@/lib/supabase/types';

// export const config = { runtime: 'edge' };

interface ChatRequestPayload {
  conversationId?: string;
  message: string;
  provider: string;
  model: string;
  groupConversationId?: string;
  comparisonIndex?: number;
  isComparison?: boolean;
  isTestMode: boolean;
  parentMessageId?: string;
  messageId?: string;
  newContent?: string;
  isEdit?: boolean;
  attachments?: Array<AttachmentMetadata>;
  useWebSearch?: boolean;
  useImageGeneration?: boolean;
  isTemporary?: boolean;
}

const createErrorResponse = (
  message: string,
  status = 500,
  headers: Record<string, string> = {}
) => NextResponse.json({ error: message }, { status, headers });

function isAttachmentMetadata(obj: unknown): obj is AttachmentMetadata {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    typeof (obj as Record<string, unknown>).name === 'string' &&
    typeof (obj as Record<string, unknown>).type === 'string' &&
    typeof (obj as Record<string, unknown>).url === 'string'
  );
}

function formatMessage(message: Message): LLMMessage {
  const log = logger.child({ messageId: message.id });
  if (!['user', 'assistant', 'system'].includes(message.role)) {
    throw new AppError(
      `Invalid message role: ${message.role}`,
      ErrorCode.INVALID_REQUEST
    );
  }

  const msgWithAttachments = message as Message & {
    attachments?: AttachmentMetadata[] | null;
  };

  if (
    msgWithAttachments.role === 'user' &&
    msgWithAttachments.attachments &&
    Array.isArray(msgWithAttachments.attachments) &&
    msgWithAttachments.attachments.length > 0
  ) {
    const contentParts: MessageContentPart[] = [];

    if (msgWithAttachments.content?.trim()) {
      contentParts.push({ type: 'text', text: msgWithAttachments.content });
    }

    const validAttachments: AttachmentMetadata[] = [];
    for (const att of msgWithAttachments.attachments) {
      if (isAttachmentMetadata(att)) {
        validAttachments.push(att);
      } else {
        log.warn(
          'Invalid attachment format found in message:',
          msgWithAttachments.id,
          att
        );
      }
    }

    validAttachments.forEach((att) => {
      if (att.type.startsWith('image/')) {
        contentParts.push({
          type: 'image_url',
          image_url: { url: att.url, detail: 'auto' },
        });
      } else {
        log.info(`Adding file attachment reference: ${att.name} (${att.type})`);
        contentParts.push({
          type: 'file',
          filename: att.name,
          mime_type: att.type,
          url: att.url,
        });
      }
    });

    const finalContentParts = contentParts.filter((part) => part !== null);

    if (finalContentParts.length > 0) {
      return {
        role: msgWithAttachments.role as 'user',
        content: finalContentParts,
        id: msgWithAttachments.id,
        file_annotations: msgWithAttachments.file_annotations,
      };
    }
  }

  return {
    role: message.role as 'user' | 'assistant' | 'system',
    content:
      typeof message.content === 'string'
        ? message.content
        : JSON.stringify(message.content),
    id: message.id,
    file_annotations: message.file_annotations,
  };
}

async function generateGroupConversationTitle(
  db: DatabaseService,
  groupConversationId: string,
  message: string,
  isTestMode: boolean
) {
  const log = logger.child({ groupConversationId, message, isTestMode });
  try {
    const llm = LLMService.getInstance();
    const groupConversation = await db.getGroupConversation(
      groupConversationId
    );
    if (!groupConversation) {
      log.warn('Group conversation not found');
      return;
    }

    const title = await llm.generateGroupConversationTitle(message, isTestMode);

    if (title) {
      db.updateGroupConversation(groupConversationId, {
        title,
      });
    }
  } catch (error) {
    log.error('Error generating group conversation title', error);
  }
}

async function manageConversation(
  db: DatabaseService,
  {
    conversationId,
    userId,
    model,
    groupConversationId,
    comparisonIndex,
    isComparison,
    isTemporary,
  }: {
    conversationId?: string;
    userId: string;
    model: string;
    groupConversationId?: string;
    comparisonIndex?: number;
    isComparison?: boolean;
    isTemporary?: boolean;
  }
): Promise<Conversation | null> {
  if (conversationId) {
    const conversation = await db.getConversation(conversationId);
    if (!conversation || conversation.user_id !== userId) return null;
    await db.updateConversation(conversationId, { last_model_used: model });
    return conversation;
  }

  const groupConversation = await db.upsertGroupConversation(userId, {
    id: groupConversationId,
    title: 'New Group Conversation',
    is_comparison: isComparison,
    is_temporary: isTemporary,
  });

  return db.createConversation(userId, {
    last_model_used: model,
    group_conversation_id: groupConversation.id,
    comparison_index: comparisonIndex,
  });
}

async function buildMessageHistory(
  db: DatabaseService,
  conversationId: string,
  endMessageId?: string
): Promise<LLMMessage[]> {
  // Retrieve messages up to the endMessageId if provided.
  // We assume db.getConversationMessages fetches all necessary columns, including
  // 'content', 'role', 'id', 'parent_message_id', 'attachments', 'annotations', 'file_annotations'.
  const messages = await db.getConversationMessages(conversationId);
  const messageMap = new Map(messages.map((msg) => [msg.id, msg]));

  const history: Message[] = [];
  let current = endMessageId ? messageMap.get(endMessageId) : undefined;

  while (current) {
    history.unshift(current);
    current = current.parent_message_id
      ? messageMap.get(current.parent_message_id)
      : undefined;
  }

  return history.map(formatMessage);
}

async function trackUsage(
  db: DatabaseService,
  user: User,
  llmModel: Model,
  conversation: Conversation,
  prompt: string,
  completion: string,
  messageId: string,
  turnNumber: number
) {
  const tokenizer = TokenizerService.getInstance();
  const promptTokens = tokenizer.countTokens(
    prompt,
    llmModel.name as SupportedModel
  );
  const completionTokens = tokenizer.countTokens(
    completion,
    llmModel.name as SupportedModel
  );

  await db.createUsageLog({
    id: crypto.randomUUID(),
    user_id: user.id,
    provider_id: llmModel.provider_id,
    model_id: llmModel.id,
    conversation_id: conversation.id,
    tokens_used: promptTokens + completionTokens,
    prompt_tokens: promptTokens,
    completion_tokens: completionTokens,
    status: 'completed',
    metadata: { conversation_turn: Math.ceil(turnNumber / 2) },
    message_id: messageId,
    cost: null,
    created_at: null,
  });
}

async function streamAssistantResponse({
  llm,
  db,
  conversation,
  messageHistory,
  llmModel,
  user,
  userMessage,
  isNewConversation,
  isTestMode,
  useWebSearch,
  useImageGeneration,
}: {
  llm: LLMService;
  db: DatabaseService;
  conversation: Conversation;
  messageHistory: LLMMessage[];
  llmModel: Model;
  user: User;
  userMessage: Message;
  isNewConversation: boolean;
  isTestMode: boolean;
  useWebSearch?: boolean;
  useImageGeneration?: boolean;
}) {
  const log = logger.child({ conversationId: conversation.id });
  const assistantMessageId = crypto.randomUUID();
  let assistantContent = '';
  const assistantUrlAnnotations: UrlCitationAnnotation[] = [];
  const assistantFileAnnotations: unknown[] = [];
  let buffer = '';

  const providerNameLower = llmModel.provider?.name?.toLowerCase();
  if (!providerNameLower) {
    throw new AppError(
      `Provider not found for model: ${llmModel.name}`,
      ErrorCode.INVALID_REQUEST
    );
  }

  const isUsingOpenAIResponsesAPI =
    providerNameLower === 'openai' &&
    !isTestMode &&
    process.env.USE_OPENROUTER !== 'true';
  const isUsingOpenRouterClient =
    providerNameLower === 'openrouter' ||
    (process.env.USE_OPENROUTER === 'true' && process.env.OPENROUTER_API_KEY);

  const isProviderJsonStream =
    isUsingOpenAIResponsesAPI || isUsingOpenRouterClient;

  if (useWebSearch && !llmModel.allows_search) {
    throw new AppError(
      `Web search is not supported by the selected model (${llmModel.display_name}).`,
      ErrorCode.INVALID_REQUEST,
      400
    );
  }

  const stream = await llm.generateStreamingCompletion(
    providerNameLower,
    messageHistory,
    {
      model: llmModel,
      useWebSearch: useWebSearch,
      useImageGeneration: useImageGeneration,
    },
    isTestMode
  );

  const decoder = new TextDecoder();
  const encoder = new TextEncoder();

  const transformStream = new TransformStream({
    start(controller) {
      const dummyChunk = new TextEncoder().encode(
        isProviderJsonStream
          ? JSON.stringify({ type: 'delta', content: '' }) + '\n'
          : ' '
      );
      controller.enqueue(dummyChunk);
    },
    transform(chunk, controller) {
      if (isProviderJsonStream) {
        const decodedChunk = decoder.decode(chunk, { stream: true });
        buffer += decodedChunk;
        let newlineIndex;
        while ((newlineIndex = buffer.indexOf('\n')) >= 0) {
          const line = buffer.slice(0, newlineIndex).trim();
          buffer = buffer.slice(newlineIndex + 1);
          if (line && line !== '[DONE]') {
            try {
              const parsed = JSON.parse(line);
              if (parsed.type === 'delta' && parsed.content) {
                assistantContent += parsed.content;
                controller.enqueue(encoder.encode(line + '\n'));
              } else if (parsed.type === 'annotation' && parsed.annotation) {
                if (parsed.annotation.type === 'url_citation') {
                  assistantUrlAnnotations.push(parsed.annotation);
                  controller.enqueue(encoder.encode(line + '\n'));
                } else {
                  assistantFileAnnotations.push(parsed.annotation);
                }
              } else if (parsed.type === 'error') {
                log.error('Received error event from stream:', parsed.error);
              }
            } catch (e) {
              log.error(
                'Error parsing JSON chunk in backend transform:',
                e,
                line
              );
            }
          }
          if (buffer.length > 1_000_000) {
            log.error('NDJSON buffer exceeded 1 MB, aborting');
            stream.cancel('Buffer overflow');
            controller.error(new Error('Stream buffer overflow'));
            return;
          }
        }
      } else {
        const decoded = decoder.decode(chunk, { stream: true });
        assistantContent += decoded;
        controller.enqueue(chunk);
      }
    },
    async flush() {
      try {
        if (isProviderJsonStream && buffer.trim()) {
          const line = buffer.trim();
          if (line && line !== '[DONE]') {
            try {
              const parsed = JSON.parse(line);
              if (parsed.type === 'delta' && parsed.content) {
                assistantContent += parsed.content;
              } else if (parsed.type === 'annotation' && parsed.annotation) {
                if (parsed.annotation.type === 'url_citation') {
                  assistantUrlAnnotations.push(parsed.annotation);
                } else {
                  assistantFileAnnotations.push(parsed.annotation);
                }
              }
            } catch (e) {
              log.error(
                'Error parsing final JSON chunk in backend transform flush:',
                e,
                line
              );
            }
          }
        }

        await db.createMessage({
          id: assistantMessageId,
          conversationId: conversation.id,
          content: assistantContent.trim(),
          role: 'assistant',
          parentMessageId: userMessage.id,
          modelId: llmModel.id,
          providerId: llmModel.provider_id as string,
          annotations:
            assistantUrlAnnotations.length > 0 ? assistantUrlAnnotations : null,
          file_annotations:
            assistantFileAnnotations.length > 0
              ? assistantFileAnnotations
              : null,
        });

        await trackUsage(
          db,
          user,
          llmModel,
          conversation,
          typeof userMessage.content === 'string'
            ? userMessage.content
            : '[User message with attachments]',
          assistantContent,
          assistantMessageId,
          messageHistory.length
        );
      } catch (error) {
        log.error('Error during stream flush (DB save or tracking):', error);
      }
    },
  });

  const groupConversation = await db.getGroupConversation(
    conversation.group_conversation_id
  );
  const isTemporary = groupConversation?.is_temporary || false;

  return new Response(stream.pipeThrough(transformStream), {
    headers: {
      'Content-Type': isProviderJsonStream
        ? 'application/x-ndjson'
        : 'text/plain; charset=utf-8',
      'X-Conversation-Id': conversation.id,
      'X-Is-New-Conversation': String(isNewConversation),
      'X-Assistant-Message-Id': assistantMessageId,
      'X-Group-Conversation-Id': conversation.group_conversation_id,
      'X-User-Message-Id': userMessage.id,
      'X-Is-Temporary': String(isTemporary),
    },
  });
}

async function handleNewChatRequest(req: Request): Promise<Response> {
  const requestId = crypto.randomUUID();
  const log = logger.child({ requestId, update: false });

  try {
    const supabase = await createClient();
    const db = DatabaseService.getInstance(supabase);
    const llm = LLMService.getInstance();

    const payload: ChatRequestPayload = await req.json();
    const {
      conversationId,
      message,
      model,
      groupConversationId,
      comparisonIndex,
      isComparison,
      isTestMode,
      parentMessageId,
      attachments,
      useWebSearch,
      useImageGeneration,
      isTemporary,
    } = payload;

    const isNewConversation = !conversationId;

    const user = await db.getCurrentUser();
    if (!user) {
      log.warn('Unauthorized access attempt');
      return createErrorResponse('Unauthorized', 401);
    }

    const llmModel = await db.getModel(model);
    if (!llmModel) {
      throw new AppError(
        `Invalid model: ${model}`,
        ErrorCode.INVALID_REQUEST,
        400
      );
    }

    const canAccessModel = await canUserAccessModel(user.id, llmModel.id);
    if (!canAccessModel) {
      throw new AppError(
        'This model requires a premium subscription',
        ErrorCode.API_UNAUTHORIZED,
        403
      );
    }

    if (useWebSearch && !llmModel.allows_search) {
      throw new AppError(
        `Web search is not supported by the selected model (${llmModel.display_name}).`,
        ErrorCode.INVALID_REQUEST,
        400
      );
    }

    const { allowed, reason } = await canUserSendMessage(user.id);
    if (!allowed) {
      throw new AppError(
        reason || 'You have reached your daily message limit',
        ErrorCode.API_RESOURCE_EXHAUSTED,
        403
      );
    }

    await incrementUserDailyMessageCount(user.id);

    const conversation = await manageConversation(db, {
      conversationId,
      userId: user.id,
      model,
      groupConversationId,
      comparisonIndex,
      isComparison,
      isTemporary,
    });

    if (isNewConversation && conversation?.group_conversation_id) {
      generateGroupConversationTitle(
        db,
        conversation.group_conversation_id,
        message,
        isTestMode
      );
    }

    if (!conversation) {
      log.warn('Conversation not found or unauthorized');
      return createErrorResponse('Conversation not found or unauthorized', 404);
    }

    const messageHistory = await buildMessageHistory(
      db,
      conversation.id,
      parentMessageId
    );
    const userMessage = await db.createMessage({
      conversationId: conversation.id,
      content: message,
      role: 'user',
      parentMessageId,
      attachments,
    });

    messageHistory.push(formatMessage(userMessage));

    return streamAssistantResponse({
      llm,
      db,
      conversation,
      messageHistory,
      llmModel,
      user,
      userMessage,
      isNewConversation,
      isTestMode,
      useWebSearch,
      useImageGeneration,
    });
  } catch (error) {
    log.error({ error }, 'Error processing chat request');
    const appError = error instanceof AppError ? error : handleError(error);
    return createErrorResponse(appError.message, appError.statusCode);
  }
}

async function handleUpdateChatRequest(req: Request): Promise<Response> {
  const requestId = crypto.randomUUID();
  let log = logger.child({ requestId, update: true });

  try {
    const supabase = await createClient();
    const db = DatabaseService.getInstance(supabase);
    const llm = LLMService.getInstance();

    const payload: ChatRequestPayload = await req.json();
    const {
      messageId,
      model,
      isTestMode,
      newContent,
      isEdit,
      useWebSearch,
      useImageGeneration,
    } = payload;

    log = logger.child({ requestId, isRetry: !isEdit, update: true });

    const user = await db.getCurrentUser();
    if (!user) {
      log.warn('Unauthorized access attempt');
      return createErrorResponse('Unauthorized', 401);
    }

    if (!messageId) {
      log.warn('Message ID is required');
      return createErrorResponse('Message ID is required', 400);
    }

    const llmModel = await db.getModel(model);
    if (!llmModel) {
      throw new AppError(
        `Invalid model: ${model}`,
        ErrorCode.INVALID_REQUEST,
        400
      );
    }

    const canAccessModel = await canUserAccessModel(user.id, llmModel.id);
    if (!canAccessModel) {
      throw new AppError(
        'This model requires a premium subscription',
        ErrorCode.API_UNAUTHORIZED,
        403
      );
    }

    if (useWebSearch && !llmModel.allows_search) {
      throw new AppError(
        `Web search is not supported by the selected model (${llmModel.display_name}).`,
        ErrorCode.INVALID_REQUEST,
        400
      );
    }

    const message = await db.getMessage(messageId);
    if (!message) {
      log.warn('Message not found');
      return createErrorResponse('Message not found', 404);
    }

    if (llmModel.id !== message.conversation?.last_model_used) {
      await db.updateConversation(message.conversation_id as string, {
        last_model_used: llmModel.id,
      });
    }

    let messageHistory = await buildMessageHistory(
      db,
      message.conversation_id as string,
      messageId
    );

    let finalUserMessage: Message = message;

    if (isEdit) {
      const editedMessage = await db.createMessage({
        conversationId: message.conversation_id as string,
        content: newContent as string,
        role: 'user',
        parentMessageId: message.parent_message_id as string,
        modelId: llmModel.id,
        providerId: llmModel.provider_id as string,
      });

      finalUserMessage = editedMessage;
      // remove message with messageId from messageHistory
      messageHistory = messageHistory.filter((msg) => msg.id !== messageId);
      messageHistory.push(formatMessage(finalUserMessage));
    }

    return streamAssistantResponse({
      llm,
      db,
      conversation: message.conversation as Conversation,
      messageHistory,
      llmModel,
      user,
      userMessage: finalUserMessage,
      isNewConversation: false,
      isTestMode,
      useWebSearch,
      useImageGeneration,
    });
  } catch (error) {
    log.error({ error }, 'Error processing chat request');
    const appError = error instanceof AppError ? error : handleError(error);
    return createErrorResponse(appError.message, appError.statusCode);
  }
}

export const POST = (req: Request) => handleNewChatRequest(req);
export const PUT = (req: Request) => handleUpdateChatRequest(req);
