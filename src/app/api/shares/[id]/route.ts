import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { DatabaseService } from '@/lib/supabase/db';

export async function GET(
  _: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: shareId } = await params;
    const supabase = await createClient();

    // Get the shared conversation details
    const { data: sharedConversation, error: conversationError } =
      await supabase
        .from('shared_conversations')
        .select('*')
        .eq('id', shareId)
        .single();

    if (conversationError) {
      console.error('Error fetching shared conversation:', conversationError);
      return NextResponse.json(
        { error: 'Shared conversation not found' },
        { status: 404 }
      );
    }

    // Get the shared messages
    const { data: sharedMessages, error: messagesError } = await supabase
      .from('shared_messages')
      .select('*')
      .eq('shared_conversation_id', shareId)
      .order('created_at', { ascending: true });

    if (messagesError) {
      console.error('Error fetching shared messages:', messagesError);
      return NextResponse.json(
        { error: 'Failed to fetch shared messages' },
        { status: 500 }
      );
    }

    // Build a tree structure from the messages
    const messagesMap = new Map();
    const rootMessages = [];

    // First pass: create nodes
    sharedMessages.forEach((message) => {
      messagesMap.set(message.id, {
        ...message,
        children: [],
      });
    });

    // Second pass: establish parent-child relationships
    sharedMessages.forEach((message) => {
      const node = messagesMap.get(message.id);

      if (message.parent_shared_message_id) {
        const parent = messagesMap.get(message.parent_shared_message_id);
        if (parent) {
          parent.children.push(node);
        }
      } else {
        rootMessages.push(node);
      }
    });

    // Return the shared conversation data
    return NextResponse.json({
      id: sharedConversation.id,
      title: sharedConversation.title,
      created_at: sharedConversation.created_at,
      messages: sharedMessages,
    });
  } catch (error) {
    console.error('Error fetching shared conversation:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: shareId } = await params;
    const supabase = await createClient();

    // Verify the user is authenticated
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check that the user owns this shared conversation
    const { data: sharedConversation, error: fetchError } = await supabase
      .from('shared_conversations')
      .select('*')
      .eq('id', shareId)
      .single();

    if (fetchError || !sharedConversation) {
      return NextResponse.json(
        { error: 'Shared conversation not found' },
        { status: 404 }
      );
    }

    if (sharedConversation.shared_by_user_id !== session.user.id) {
      return NextResponse.json(
        {
          error:
            'You do not have permission to delete this shared conversation',
        },
        { status: 403 }
      );
    }

    // Delete the shared conversation (shared_messages will be cascade deleted)
    const { error: deleteError } = await supabase
      .from('shared_conversations')
      .delete()
      .eq('id', shareId);

    if (deleteError) {
      console.error('Error deleting shared conversation:', deleteError);
      return NextResponse.json(
        { error: 'Failed to delete shared conversation' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting shared conversation:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Add a PATCH method to update an existing share
export async function PATCH(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: shareId } = await params;
    const supabase = await createClient();
    const { message_id } = await request.json();

    if (!message_id) {
      return NextResponse.json(
        { error: 'Missing required field: message_id' },
        { status: 400 }
      );
    }

    // Verify the user is authenticated
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check that the user owns this shared conversation
    const { data: sharedConversation, error: fetchError } = await supabase
      .from('shared_conversations')
      .select('*, original_conversation_id')
      .eq('id', shareId)
      .single();

    if (fetchError || !sharedConversation) {
      return NextResponse.json(
        { error: 'Shared conversation not found' },
        { status: 404 }
      );
    }

    if (sharedConversation.shared_by_user_id !== session.user.id) {
      return NextResponse.json(
        {
          error:
            'You do not have permission to update this shared conversation',
        },
        { status: 403 }
      );
    }

    const db = DatabaseService.getInstance(supabase);

    // Get the conversation to ensure it belongs to the user
    const conversation = await db.getConversation(
      sharedConversation.original_conversation_id
    );
    if (!conversation || conversation.user_id !== session.user.id) {
      return NextResponse.json(
        { error: 'Conversation not found' },
        { status: 404 }
      );
    }

    // Delete existing shared messages (they will be recreated)
    const { error: deleteError } = await supabase
      .from('shared_messages')
      .delete()
      .eq('shared_conversation_id', shareId);

    if (deleteError) {
      console.error('Error deleting existing shared messages:', deleteError);
      return NextResponse.json(
        { error: 'Failed to update shared conversation' },
        { status: 500 }
      );
    }

    // Get the message path from the original conversation
    const messages = await db.getConversationMessages(
      sharedConversation.original_conversation_id
    );

    // Build a map of messages to quickly look up by id
    const messagesMap = new Map();
    messages.forEach((msg) => messagesMap.set(msg.id, msg));

    // Trace the path from the selected message back to the root
    const messagePath = [];
    let currentMessageId = message_id;

    while (currentMessageId) {
      const currentMessage = messagesMap.get(currentMessageId);
      if (!currentMessage) break;

      messagePath.unshift(currentMessage); // Add to the beginning to maintain order
      currentMessageId = currentMessage.parent_message_id || null;
    }

    // Create a mapping from original message IDs to shared message IDs
    const messageIdMap = new Map();

    // Copy all messages in the path to shared_messages
    for (const message of messagePath) {
      const { data: sharedMessage, error } = await supabase
        .from('shared_messages')
        .insert({
          shared_conversation_id: shareId,
          original_message_id: message.id,
          parent_shared_message_id: message.parent_message_id
            ? messageIdMap.get(message.parent_message_id)
            : null,
          role: message.role,
          content: message.content,
          created_at: message.created_at,
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating shared message:', error);
        return NextResponse.json(
          { error: 'Failed to update shared messages' },
          { status: 500 }
        );
      }

      // Store the mapping from original to shared message IDs
      messageIdMap.set(message.id, sharedMessage.id);
    }

    // Update the shared conversation with the new last message ID
    const { error: updateError } = await supabase
      .from('shared_conversations')
      .update({
        last_original_message_id: message_id,
        // Don't update title or we'll lose custom titles
      })
      .eq('id', shareId);

    if (updateError) {
      console.error('Error updating shared conversation:', updateError);
      return NextResponse.json(
        { error: 'Failed to update shared conversation record' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      share_id: shareId,
      share_url: `/share/${shareId}`,
    });
  } catch (error) {
    console.error('Error updating shared conversation:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
