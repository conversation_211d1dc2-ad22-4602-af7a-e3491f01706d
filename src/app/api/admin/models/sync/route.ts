import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { handleError } from '@/lib/error';
import { ModelSyncService } from '@/services/modelSync';

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();

    // Check if user is admin
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const {
      onlyUpdateExisting = true,
      defaultTier = 'premium',
      defaultPriority = 100,
      modelId = null,
    } = body;

    // Sync models
    const modelSyncService = ModelSyncService.getInstance();

    if (modelId) {
      // Sync a specific model
      await modelSyncService.updateSingleModel(modelId);
      return NextResponse.json({
        success: true,
        message: `Updated model ${modelId}`,
      });
    } else {
      // Sync all models
      await modelSyncService.syncOpenRouterModels({
        onlyUpdateExisting,
        defaultTier,
        defaultPriority,
      });
      return NextResponse.json({
        success: true,
        message: 'Successfully synced OpenRouter models',
      });
    }
  } catch (error) {
    const appError = handleError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}
