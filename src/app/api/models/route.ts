import { LLMService } from '@/services/llm/service';
import { logger } from '@/lib/logger';
import { handleError } from '@/lib/error';
const log = logger.child({
  module: 'ModelsRoute',
});

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const providerName = searchParams.get('provider');

    const llmService = LLMService.getInstance();
    const models = await llmService.getModels(providerName || '');

    return Response.json(models);
  } catch (error: unknown) {
    log.error('Error fetching models:', error);
    const appError = handleError(error);
    const statusCode = appError.statusCode;
    return Response.json({ error: appError.message }, { status: statusCode });
  }
}