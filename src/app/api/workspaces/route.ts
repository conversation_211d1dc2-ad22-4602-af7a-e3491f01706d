import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { DatabaseService } from '@/lib/supabase/db';
import { AppError, handleError } from '@/lib/error';
import { logger } from '@/lib/logger';
import { getUserSubscription } from '@/lib/supabase/subscription';

const log = logger.child({ api: '/api/workspaces' });

// GET /api/workspaces - List all workspaces for the current user
export async function GET() {
  try {
    const supabase = await createClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const db = DatabaseService.getInstance(supabase);
    const workspaces = await db.getUserWorkspaces(user.id);

    return NextResponse.json({ workspaces });
  } catch (error) {
    log.error({ error }, 'Error fetching workspaces');
    const appError = error instanceof AppError ? error : handleError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}

// POST /api/workspaces - Create a new workspace
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { name, description, icon, default_model_id } = body;

    if (!name || name.trim() === '') {
      return NextResponse.json(
        { error: 'Workspace name is required' },
        { status: 400 }
      );
    }

    if (name.length > 80) {
      return NextResponse.json(
        { error: 'Workspace name must be 80 characters or less' },
        { status: 400 }
      );
    }

    const db = DatabaseService.getInstance(supabase);

    // Check workspace limits before creating
    const existingWorkspaces = await db.getUserWorkspaces(user.id);
    const workspaceCount = existingWorkspaces.length;

    // Get user's subscription to check limits
    const subscription = await getUserSubscription(user.id);
    const currentPlan = subscription?.plan || 'free';
    const isActive = subscription
      ? ['active', 'trialing'].includes(subscription.status)
      : false;

    // If subscription exists but isn't active, treat as free
    const effectivePlan = subscription && !isActive ? 'free' : currentPlan;
    const { SUBSCRIPTION_PLANS } = await import('@/lib/supabase/types');
    const planDetails = SUBSCRIPTION_PLANS[effectivePlan];

    // Check if user has reached workspace limit
    if (planDetails.maxWorkspaces !== -1 && workspaceCount >= planDetails.maxWorkspaces) {
      return NextResponse.json(
        {
          error: `Workspace limit reached. ${planDetails.name} allows up to ${planDetails.maxWorkspaces} workspace${planDetails.maxWorkspaces === 1 ? '' : 's'}. Upgrade your plan to create more workspaces.`,
          code: 'WORKSPACE_LIMIT_REACHED',
          limit: planDetails.maxWorkspaces,
          current: workspaceCount
        },
        { status: 403 }
      );
    }

    const workspace = await db.createWorkspace(user.id, {
      name,
      description,
      icon,
      default_model_id,
    });

    log.info(
      { userId: user.id, workspaceId: workspace.id },
      'Workspace created'
    );

    return NextResponse.json({ workspace });
  } catch (error) {
    log.error({ error }, 'Error creating workspace');
    const appError = error instanceof AppError ? error : handleError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}
