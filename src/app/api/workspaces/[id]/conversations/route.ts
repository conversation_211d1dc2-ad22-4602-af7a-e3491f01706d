import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/supabase/db';
import { logger } from '@/lib/logger';
import { createClient } from '@/utils/supabase/server';
import { AppError } from '@/lib/error';

const log = logger.child({
  module: 'workspace-conversations',
});

export async function GET(
  _: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id: workspaceId } = await params;

  try {
    const supabase = await createClient();
    const db = DatabaseService.getInstance(supabase);

    const activeUser = await db.getCurrentUser();
    if (!activeUser) {
      return NextResponse.json(
        { data: [], error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Fetch the 5 most recent conversations for this workspace
    const { data: conversations } = await db.getUserGroupConversations(
      5, // limit to 5 conversations
      undefined, // no cursor
      workspaceId // filter by workspace ID
    );

    return NextResponse.json({
      data: conversations,
      error: null,
    });
  } catch (error) {
    log.error({ error, workspaceId }, 'Error fetching workspace conversations');
    const statusCode = error instanceof AppError ? error.statusCode : 500;
    return NextResponse.json(
      { data: [], error: 'Failed to fetch workspace conversations' },
      { status: statusCode }
    );
  }
}
