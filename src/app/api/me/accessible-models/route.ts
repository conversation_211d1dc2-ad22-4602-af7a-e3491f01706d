import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { getAvailableModelsForUser } from '@/lib/supabase/subscription';
import { AppError, handleError, ErrorCode } from '@/lib/error';
import { logger } from '@/lib/logger';

const log = logger.child({ module: 'api/me/accessible-models' });

export async function GET() {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      throw new AppError(
        'User not authenticated',
        ErrorCode.API_UNAUTHORIZED,
        401
      );
    }

    log.info({ userId: user.id }, 'Fetching accessible models for user');
    const accessibleModels = await getAvailableModelsForUser(user.id);
    log.info(
      { userId: user.id, count: accessibleModels.length },
      'Successfully fetched accessible models'
    );

    return NextResponse.json(accessibleModels);
  } catch (error) {
    const appError = handleError(error);
    return NextResponse.json(
      { error: appError.toJSON() },
      { status: appError.statusCode }
    );
  }
}