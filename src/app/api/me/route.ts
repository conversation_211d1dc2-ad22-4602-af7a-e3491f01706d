// GET /api/me

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { DatabaseService } from '@/lib/supabase/db';
import { Me, UserPreferences } from '@/lib/supabase/types';

export async function GET() {
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const db = DatabaseService.getInstance(supabase);

  const [preferences] = await Promise.all([db.getUserPreferences(user.id)]);

  const me: Me = {
    preferences: preferences as UserPreferences,
  };

  return NextResponse.json({ me });
}

export async function POST(request: NextRequest) {
  const supabase = await createClient();
  const db = DatabaseService.getInstance(supabase);

  const { defaultModelId, explicitlyHiddenModelIds, explicitlyShownModelIds } = await request.json();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const explicitlyHiddenModelIdsArray = explicitlyHiddenModelIds || [];
  const explicitlyShownModelIdsArray = explicitlyShownModelIds || [];

  const updatedPreferences = await db.updateUserPreferences(user.id, {
    default_model_id: defaultModelId,
    explicitly_hidden_model_ids: explicitlyHiddenModelIdsArray,
    explicitly_shown_model_ids: explicitlyShownModelIdsArray,
  });

  return NextResponse.json({ preferences: updatedPreferences });
}
