import { DatabaseService } from '@/lib/supabase/db';
import { createClient } from '@/utils/supabase/server';
import { handleError } from '@/lib/error';
import { ModelMapper } from '@/services/modelMapper';
import { Model } from '@/lib/supabase/types';
import { NextResponse } from 'next/server';

export async function GET() {
  try {
    const supabase = await createClient();
    const db = DatabaseService.getInstance(supabase);
    const providersAndModels = await db.getProvidersAndModels();

    const providersList = providersAndModels.map((provider) => ({
      id: provider.id,
      name: provider.name,
      display_name: provider.display_name,
      models: provider.models.map((model) => {
        // Instead of unsafe casting, manually construct the API response
        // This avoids the type cast issue while ensuring we have all required fields
        try {
          // Validate essential fields
          if (!model.id || !model.name) {
            throw new Error('Invalid model data: missing id or name');
          }

          // For models that have been properly processed by getProvidersAndModels,
          // we can safely use ModelMapper. For others, we construct the response manually.
          if (model.capabilities && Array.isArray(model.supported_parameters)) {
            // This model has been processed and should be safe to use with ModelMapper
            const safeModel = model as unknown as Model;
            safeModel.provider = model.provider; // Ensure provider is set
            return ModelMapper.mapInternalModelToApiResponse(safeModel);
          } else {
            // Manually construct the API response for models that might be incomplete
            return {
              id: model.id,
              name: model.name,
              display_name: model.display_name || model.name,
              description: model.description || '',
              provider: {
                id: provider.id,
                name: provider.name,
                display_name: provider.display_name,
              },
              tier: model.tier || 'free',
              capabilities: {
                file_upload: model.allows_file_upload || false,
                web_search: model.allows_search || false,
                visible_by_default: model.is_visible_by_default || true,
                image_generation: false,
                code_generation: true,
                function_calling: false,
                reasoning: false,
                structured_output: false,
              },
              context_length: model.context_length || 4096,
              architecture: model.architecture || null,
              pricing: model.pricing || null,
              supported_parameters: Array.isArray(model.supported_parameters)
                ? model.supported_parameters
                : ['temperature', 'max_tokens', 'stop'],
            };
          }
        } catch (modelError) {
          // Log the error but don't fail the entire request for one bad model
          console.error(`Error processing model ${model.id}:`, modelError);
          // Return a minimal safe model representation
          return {
            id: model.id || 'unknown',
            name: model.name || 'Unknown Model',
            display_name: model.display_name || model.name || 'Unknown Model',
            description: model.description || '',
            provider: {
              id: provider.id,
              name: provider.name,
              display_name: provider.display_name,
            },
            tier: 'free',
            capabilities: {
              file_upload: false,
              web_search: false,
              visible_by_default: true,
            },
            context_length: 4096,
            architecture: null,
            pricing: null,
            supported_parameters: ['temperature', 'max_tokens', 'stop'],
          };
        }
      }),
    }));

    return NextResponse.json(providersList);
  } catch (error) {
    const appError = handleError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}
