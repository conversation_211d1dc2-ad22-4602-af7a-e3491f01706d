import { NextResponse } from 'next/server';
import { getUserDailyMessageCount } from '@/lib/supabase/subscription';
import { createClient } from '@/utils/supabase/server';
import { logger } from '@/lib/logger';
import { AppError } from '@/lib/error';

const log = logger.child({
  module: 'usage-daily',
});

export async function GET() {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const count = await getUserDailyMessageCount(user.id);

    return NextResponse.json({
      data: {
        count,
      },
    });
  } catch (error: unknown) {
    log.error({ error }, 'Error fetching daily usage');
    const statusCode = error instanceof AppError ? error.statusCode : 500;
    return Response.json(
      { error: 'Failed to fetch daily usage' },
      { status: statusCode }
    );
  }
}
