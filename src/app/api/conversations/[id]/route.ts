import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/supabase/db';
import { createClient } from '@/utils/supabase/server';
import { handleError } from '@/lib/error';

export async function GET(_: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;
    const supabase = await createClient();
    const db = DatabaseService.getInstance(supabase);
    const conversationMessages = await db.getConversationMessages(id);
    return Response.json(conversationMessages);
  } catch (error) {
    const appError = handleError(error);
    const statusCode = appError.statusCode;
    return Response.json({ error: appError.message }, { status: statusCode });
  }
}

export async function DELETE(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;
    if (!id) {
      return NextResponse.json({ data: null, error: 'Missing conversationId' }, { status: 400 });
  }
  const supabase = await createClient();
  const db = DatabaseService.getInstance(supabase);
  const conversation = await db.deleteConversation(id);
    return NextResponse.json({ data: conversation, error: null });
  } catch (error) {
    const appError = handleError(error);
    const statusCode = appError.statusCode;
    return NextResponse.json({ data: null, error: appError.message }, { status: statusCode });
  }
}
