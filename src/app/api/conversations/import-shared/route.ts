import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { DatabaseService } from '@/lib/supabase/db';
import { UrlCitationAnnotation } from '@/lib/supabase/types';
import { logger } from '@/lib/logger';

// edge function
export const runtime = 'edge';

const log = logger.child({
  module: 'import-shared',
});

export async function POST(request: NextRequest) {
  try {
    const { shareId } = await request.json();

    if (!shareId) {
      return NextResponse.json(
        { error: 'Missing required field: shareId' },
        { status: 400 }
      );
    }

    const supabase = await createClient();

    // Verify the user is authenticated
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = session.user.id;
    const db = DatabaseService.getInstance(supabase);

    log.info('Importing shared conversation', { shareId });

    // 1. Check if this shared conversation exists and is valid
    const { data: sharedConversation, error: sharedConversationError } =
      await supabase
        .from('shared_conversations')
        .select('*')
        .eq('id', shareId)
        .single();

    if (sharedConversationError || !sharedConversation) {
      log.error('Shared conversation not found', { shareId, error: sharedConversationError });
      return NextResponse.json(
        { error: 'Shared conversation not found' },
        { status: 404 }
      );
    }

    log.info('Shared conversation found', { sharedConversation });

    // check if user has the conversation
    const { data: userConversation, error: userConversationError } = await supabase
      .from('conversations')
      .select('group_conversation_id')
      .eq('user_id', userId)
      .eq('id', sharedConversation.original_conversation_id)
      .maybeSingle();

    if (userConversationError) {
      log.error('Error checking for user conversation', { userConversationError });
      return NextResponse.json(
        { error: 'Failed to check for user conversation' },
        { status: 500 }
      );
    }

    if (userConversation) {
      log.info('User already has this conversation', { userConversation });
      return NextResponse.json({
        conversationId: userConversation.group_conversation_id,
        isExisting: true,
      });
    }

    // 2. Check if the user has already imported this shared conversation
    const { data: existingConversations, error: existingError } = await supabase
      .from('conversations')
      .select('group_conversation_id')
      .eq('user_id', userId)
      .eq('imported_from_share_id', shareId)
      .limit(1);

    if (existingError) {
      log.error('Error checking for existing imports:', existingError);
      return NextResponse.json(
        { error: 'Failed to check for existing imports' },
        { status: 500 }
      );
    }

    log.info('Checking for existing imports', { existingConversations });

    // If the user has already imported this conversation, return the existing one
    if (existingConversations && existingConversations.length > 0) {
      return NextResponse.json({
        conversationId: existingConversations[0].group_conversation_id,
        isExisting: true,
      });
    }

    // 3. Get the shared messages
    const { data: sharedMessages, error: messagesError } = await supabase
      .from('shared_messages')
      .select('*')
      .eq('shared_conversation_id', shareId)
      .order('created_at', { ascending: true });

    if (messagesError) {
      log.error('Error fetching shared messages:', messagesError);
      return NextResponse.json(
        { error: 'Failed to fetch shared messages' },
        { status: 500 }
      );
    }

    log.info('Shared messages fetched', { sharedMessages: sharedMessages.length });


    // 4. Create a new group conversation
    const groupConversationId = crypto.randomUUID();

    log.info('Creating group conversation', { groupConversationId });
    await db.upsertGroupConversation(userId, {
      id: groupConversationId,
      title: sharedConversation.title,
      is_comparison: false,
    });

    // 5. Create a new conversation in the group with the imported_from_share_id
    const conversation = await db.createConversation(userId, {
      title: sharedConversation.title,
      group_conversation_id: groupConversationId,
      imported_from_share_id: shareId,

    });

    log.info('Conversation created', { conversation });

    // 6. Create messages for the conversation
    // Track parent message mapping (shared message ID -> new message ID)
    const messageIdMapping = new Map<string, string>();

    // First, create all messages with null parent IDs (we'll update them later)
    for (const sharedMessage of sharedMessages) {
      const newMessageId = crypto.randomUUID();
      messageIdMapping.set(sharedMessage.id, newMessageId);

      await db.createMessage({
        id: newMessageId,
        conversationId: conversation.id,
        content: sharedMessage.content,
        role: sharedMessage.role,
        modelId: sharedMessage.model_id,
        providerId: sharedMessage.provider_id,
        annotations: sharedMessage.annotations as UrlCitationAnnotation[] | null,
      });
    }

    log.info('Messages created');

    // Then, update parent message IDs
    for (const sharedMessage of sharedMessages) {
      if (sharedMessage.parent_shared_message_id) {
        const newParentId = messageIdMapping.get(sharedMessage.parent_shared_message_id);
        const newMessageId = messageIdMapping.get(sharedMessage.id);

        if (newParentId && newMessageId) {
          // Update the parent message ID
          const { error: updateError } = await supabase
            .from('messages')
            .update({ parent_message_id: newParentId })
            .eq('id', newMessageId);

          if (updateError) {
            log.error('Error updating parent message ID:', updateError);
            // Continue anyway, not critical
          }
        }
      }
    }

    return NextResponse.json({
      conversationId: groupConversationId,
      isExisting: false,
    });
  } catch (error) {
    log.error('Error importing shared conversation:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}