import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/supabase/db';
import { logger } from '@/lib/logger';
import { createClient } from '@/utils/supabase/server';
import { AppError } from '@/lib/error';

const log = logger.child({
  module: 'conversation-workspace',
});

export async function GET(
  _: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id: conversationId } = await params;

  try {
    const supabase = await createClient();
    const db = DatabaseService.getInstance(supabase);

    const activeUser = await db.getCurrentUser();
    if (!activeUser) {
      return NextResponse.json(
        { data: null, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the group conversation to find workspace_id
    const { data: conversation, error: conversationError } = await supabase
      .from('group_conversations')
      .select('workspace_id')
      .eq('id', conversationId)
      .eq('user_id', activeUser.id)
      .single();

    if (conversationError || !conversation) {
      return NextResponse.json(
        { data: null, error: 'Conversation not found' },
        { status: 404 }
      );
    }

    // If no workspace_id, return null
    if (!conversation.workspace_id) {
      return NextResponse.json({
        data: null,
        error: null,
      });
    }

    // Fetch workspace information
    const { data: workspace, error: workspaceError } = await supabase
      .from('workspaces')
      .select('id, name, icon, description')
      .eq('id', conversation.workspace_id)
      .eq('owner_id', activeUser.id)
      .single();

    if (workspaceError || !workspace) {
      return NextResponse.json(
        { data: null, error: 'Workspace not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      data: workspace,
      error: null,
    });
  } catch (error) {
    log.error(
      { error, conversationId },
      'Error fetching conversation workspace'
    );
    const statusCode = error instanceof AppError ? error.statusCode : 500;
    return NextResponse.json(
      { data: null, error: 'Failed to fetch workspace information' },
      { status: statusCode }
    );
  }
}
