import { DatabaseService } from '@/lib/supabase/db';
import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { handleError } from '@/lib/error';
import { GroupConversation } from '@/lib/supabase/types';
import Bugsnag from '@bugsnag/js';

interface GroupConversationsResponse {
  data: GroupConversation[];
  nextCursor: string | null;
  error: string | null;
}

export async function GET(
  request: Request
): Promise<NextResponse<GroupConversationsResponse>> {
  const url = new URL(request.url);
  const limit = parseInt(url.searchParams.get('limit') || '30');
  const cursor = url.searchParams.get('cursor') || undefined;

  Bugsnag.notify(new Error('Test error'))

  const supabase = await createClient();
  const db = DatabaseService.getInstance(supabase);

  try {
    const activeUser = await db.getCurrentUser();

    if (!activeUser) {
      return NextResponse.json(
        { data: [], nextCursor: null, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { data: conversations, nextCursor } =
      await db.getUserGroupConversations(limit, cursor);
    return NextResponse.json({ data: conversations, nextCursor, error: null });
  } catch (error) {
    // If it's an AppError, use its status code, otherwise default to 500
    const appError = handleError(error);
    const statusCode = appError.statusCode;

    return NextResponse.json(
      { data: [], nextCursor: null, error: appError.message },
      { status: statusCode }
    );
  }
}

export async function DELETE() {
  const supabase = await createClient();
  const db = DatabaseService.getInstance(supabase);

  try {
    const conversation = await db.deleteAllConversations();
    return NextResponse.json({ data: conversation, error: null });
  } catch (error) {
    const appError = handleError(error);
    const statusCode = appError.statusCode;
    return NextResponse.json(
      { data: null, error: appError.message },
      { status: statusCode }
    );
  }
}
