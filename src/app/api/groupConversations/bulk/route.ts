import { DatabaseService } from '@/lib/supabase/db';
import { NextResponse, NextRequest } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { handleError } from '@/lib/error';

interface BulkOperationRequest {
  operation: 'archive' | 'delete';
  conversationIds: string[];
}

interface BulkOperationResponse {
  data: {
    success: boolean;
    processedCount: number;
    failedIds: string[];
  } | null;
  error: string | null;
}

export async function POST(
  request: NextRequest
): Promise<NextResponse<BulkOperationResponse>> {
  try {
    const body: BulkOperationRequest = await request.json();
    const { operation, conversationIds } = body;

    if (!operation || !conversationIds || !Array.isArray(conversationIds)) {
      return NextResponse.json(
        {
          data: null,
          error:
            'Invalid request. Operation and conversationIds array are required.',
        },
        { status: 400 }
      );
    }

    if (conversationIds.length === 0) {
      return NextResponse.json(
        {
          data: null,
          error: 'No conversation IDs provided.',
        },
        { status: 400 }
      );
    }

    if (!['archive', 'delete'].includes(operation)) {
      return NextResponse.json(
        {
          data: null,
          error: 'Invalid operation. Must be "archive" or "delete".',
        },
        { status: 400 }
      );
    }

    const supabase = await createClient();
    const db = DatabaseService.getInstance(supabase);

    // Verify user is authenticated
    const activeUser = await db.getCurrentUser();
    if (!activeUser) {
      return NextResponse.json(
        { data: null, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const failedIds: string[] = [];
    let processedCount = 0;

    // Process each conversation ID
    for (const conversationId of conversationIds) {
      try {
        if (operation === 'archive') {
          await db.archiveGroupConversation(conversationId);
        } else if (operation === 'delete') {
          await db.deleteGroupConversation(conversationId);
        }
        processedCount++;
      } catch (error) {
        console.error(
          `Failed to ${operation} conversation ${conversationId}:`,
          error
        );
        failedIds.push(conversationId);
      }
    }

    return NextResponse.json({
      data: {
        success: failedIds.length === 0,
        processedCount,
        failedIds,
      },
      error: null,
    });
  } catch (error) {
    const appError = handleError(error);
    const statusCode = appError.statusCode;
    return NextResponse.json(
      { data: null, error: appError.message },
      { status: statusCode }
    );
  }
}
