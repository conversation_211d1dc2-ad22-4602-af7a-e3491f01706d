import { DatabaseService } from '@/lib/supabase/db';
import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { handleError } from '@/lib/error';
export async function GET() {
  try {
    const supabase = await createClient();
    const db = DatabaseService.getInstance(supabase);

    const conversations = await db.getArchivedGroupConversations();
    return NextResponse.json({ data: conversations, error: null });
  } catch (error) {
    const appError = handleError(error);
    const statusCode = appError.statusCode;
    return NextResponse.json({ data: null, error: appError.message }, { status: statusCode });
  }
}
