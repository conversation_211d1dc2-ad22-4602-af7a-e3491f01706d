import { DatabaseService } from '@/lib/supabase/db';
import { NextResponse, NextRequest } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { handleError } from '@/lib/error';
export async function POST(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;
    if (!id) {
      return NextResponse.json({ data: null, error: 'Missing groupConversationId' }, { status: 400 });
  }

  const supabase = await createClient();
  const db = DatabaseService.getInstance(supabase);

    const conversation = await db.archiveGroupConversation(id);
    return NextResponse.json({ data: conversation, error: null });
  } catch (error) {
    const appError = handleError(error);
    const statusCode = appError.statusCode;
    return NextResponse.json({ data: null, error: appError.message }, { status: statusCode });
  }
}

export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;
    if (!id) {
      return NextResponse.json({ data: null, error: 'Missing groupConversationId' }, { status: 400 });
  }

  const supabase = await createClient();
  const db = DatabaseService.getInstance(supabase);

    const conversation = await db.restoreGroupConversation(id);
    return NextResponse.json({ data: conversation, error: null });
  } catch (error) {
    const appError = handleError(error);
    const statusCode = appError.statusCode;
    return NextResponse.json({ data: null, error: appError.message }, { status: statusCode });
  }
}
