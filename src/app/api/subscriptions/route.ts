import { NextRequest, NextResponse } from 'next/server';
import {
  getUserSubscription,
  createSubscription,
} from '@/lib/supabase/subscription';
import {
  createStripeCustomer,
  createStripeSession,
  manageSubscription,
} from '@/lib/stripe';
import { createClient } from '@/utils/supabase/server';
import { logger } from '@/lib/logger';
import { AppError } from '@/lib/error';
import { SubscriptionPlan } from '@/lib/supabase/types';

export async function GET() {
  // Get user from session
  const supabase = await createClient();
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  // Get subscription for the user
  const subscription = await getUserSubscription(user.id);

  // If no subscription exists, create a free one
  if (!subscription) {
    const newSubscription = await createSubscription(user.id);
    return NextResponse.json({ data: newSubscription });
  }

  return NextResponse.json({ data: subscription });
}

export async function POST(request: NextRequest) {
  const { plan, returnUrl } = await request.json();

  const log = logger.child({
    module: 'subscriptions',
    plan,
    returnUrl,
  });
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    log.info('Creating subscription');

    if (!plan || !returnUrl) {
      log.error('Missing plan or returnUrl');
      return NextResponse.json(
        { error: 'Missing plan or returnUrl' },
        { status: 400 }
      );
    }

    // Get existing subscription if it exists
    let subscription = await getUserSubscription(user.id);
    log.info({ subscription: subscription?.id }, 'Subscription');

    // Create a free subscription if one doesn't exist
    if (!subscription) {
      subscription = await createSubscription(user.id);
      log.info({ subscription: subscription?.id }, 'Subscription created');
    }

    // If the user is already on a paid plan and wants a free plan
    if (plan === 'free' && subscription.plan !== 'free') {
      log.info('Downgrading to free plan');
      return NextResponse.json({
        data: {
          message:
            'To downgrade to the free plan, please cancel your current subscription',
          url: null,
        },
      });
    }

    // If the user is selecting a paid plan
    if (plan !== 'free') {
      let customerId = subscription.stripe_customer_id;

      // Create Stripe customer if one doesn't exist
      if (!customerId) {
        log.info('Creating Stripe customer');
        const customer = await createStripeCustomer(
          user.email!,
          user.user_metadata?.name
        );
        customerId = customer.id;

        // Update the subscription with the Stripe customer ID
        await updateSubscriptionWithStripeCustomerId(
          subscription.id,
          customerId
        );
      }

      // Create Stripe checkout session
      const session = await createStripeSession(
        customerId,
        plan as SubscriptionPlan,
        returnUrl
      );

      log.info({ session: session.url }, 'Stripe checkout session created');

      return NextResponse.json({
        data: {
          url: session.url,
        },
      });
    }

    // If switching to free plan from free plan
    return NextResponse.json({
      data: {
        message: 'You are already on the free plan',
        url: null,
      },
    });
  } catch (error: unknown) {
    log.error({ error }, 'Error creating subscription');
    const statusCode = error instanceof AppError ? error.statusCode : 500;
    return Response.json(
      { error: 'Failed to create subscription' },
      { status: statusCode }
    );
  }
}

// API endpoint for managing existing subscriptions
export async function PUT(request: NextRequest) {
  const log = logger.child({
    module: 'subscriptions',
    action: 'manage',
  });

  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { action, returnUrl } = await request.json();

    if (!action || !returnUrl) {
      return NextResponse.json(
        { error: 'Missing action or returnUrl' },
        { status: 400 }
      );
    }

    // Get existing subscription
    const subscription = await getUserSubscription(user.id);

    if (!subscription) {
      return NextResponse.json(
        { error: 'No subscription found' },
        { status: 404 }
      );
    }

    // Check if user has a Stripe customer ID
    if (!subscription.stripe_customer_id) {
      return NextResponse.json(
        { error: 'No Stripe customer found' },
        { status: 400 }
      );
    }

    if (action === 'manage') {
      const session = await manageSubscription(
        subscription.stripe_customer_id,
        returnUrl
      );

      return NextResponse.json({
        data: {
          url: session.url,
        },
      });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
  } catch (error: unknown) {
    log.error({ error }, 'Error managing subscription');
    const statusCode = error instanceof AppError ? error.statusCode : 500;
    return Response.json(
      { error: 'Failed to manage subscription' },
      { status: statusCode }
    );
  }
}

// Helper function to update subscription with Stripe customer ID
async function updateSubscriptionWithStripeCustomerId(
  subscriptionId: string,
  stripeCustomerId: string
) {
  const log = logger.child({
    module: 'subscriptions',
    action: 'update_stripe_customer',
  });

  const { createClient } = await import('@/utils/supabase/server');
  const supabase = await createClient();

  log.info(
    { subscriptionId, stripeCustomerId },
    'Updating subscription with Stripe customer ID'
  );

  const { error } = await supabase
    .from('subscriptions')
    .update({
      stripe_customer_id: stripeCustomerId,
      updated_at: new Date().toISOString(),
    })
    .eq('id', subscriptionId);

  if (error) {
    log.error({ error }, 'Error updating subscription with Stripe customer ID');
    throw error;
  }
}
