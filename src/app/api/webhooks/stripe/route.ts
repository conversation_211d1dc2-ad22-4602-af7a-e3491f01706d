import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import <PERSON><PERSON> from 'stripe';
import { constructEventFromPayload } from '@/lib/stripe';
import { updateSubscription } from '@/lib/supabase/subscription';
import { logger } from '@/lib/logger';
import { AppError } from '@/lib/error';
import { SubscriptionStatus, SubscriptionPlan } from '@/lib/supabase/types';
import { createClient } from '@/utils/supabase/server';
import { SupabaseClient } from '@supabase/supabase-js';

const log = logger.child({ module: 'stripe-webhook' });

export async function POST(req: NextRequest) {
  const body = await req.text();
  const signature = (await headers()).get('stripe-signature');

  if (!signature) {
    log.warn('Webhook signature missing');
    return NextResponse.json(
      { error: 'Webhook signature missing' },
      { status: 400 }
    );
  }

  let event: Stripe.Event;

  try {
    event = constructEventFromPayload(signature, Buffer.from(body));
    log.info(
      { eventType: event.type, eventId: event.id },
      'Event constructed successfully'
    );
  } catch (error: unknown) {
    log.error({ error }, 'Error constructing event from payload');
    const statusCode = error instanceof AppError ? error.statusCode : 500;
    return NextResponse.json(
      { error: 'Failed to construct event from payload' },
      { status: statusCode }
    );
  }

  try {
    const supabase = await createClient();

    // Idempotency check
    const { data: existingEvent } = await supabase
      .from('stripe_events')
      .select('id')
      .eq('event_id', event.id)
      .single();

    if (existingEvent) {
      log.info({ eventId: event.id }, 'Event already processed, skipping.');
      return NextResponse.json(
        { message: 'Event already processed' },
        { status: 200 }
      );
    }

    await handleStripeEvent(event, supabase);

    // Mark event as processed
    await supabase.from('stripe_events').insert({ event_id: event.id });

    log.info(
      { eventType: event.type, eventId: event.id },
      'Webhook event processed successfully'
    );
    return NextResponse.json({ message: 'Webhook received' }, { status: 200 });
  } catch (error: unknown) {
    log.error(
      { error, eventType: event.type, eventId: event.id },
      'Error handling webhook'
    );
    const statusCode = error instanceof AppError ? error.statusCode : 500;
    return NextResponse.json(
      { error: 'Failed to handle webhook' },
      { status: statusCode }
    );
  }
}

async function handleStripeEvent(
  event: Stripe.Event,
  supabase: SupabaseClient
) {
  switch (event.type) {
    case 'checkout.session.completed': {
      const session = event.data.object as Stripe.Checkout.Session;
      if (
        session.mode === 'subscription' &&
        session.customer &&
        session.subscription
      ) {
        await handleSessionCreated(
          String(session.customer),
          String(session.subscription),
          supabase
        );
      } else if (session.mode === 'payment' && session.metadata) {
        // Handle add-on purchases
        await handleAddonPurchase(session, supabase);
      }
      break;
    }

    case 'customer.subscription.created': {
      const subscription = event.data.object as Stripe.Subscription;
      const customerId =
        typeof subscription.customer === 'string'
          ? subscription.customer
          : subscription.customer.id;

      // Use current_period_end from the main subscription object if available, else from items
      await handleSubscriptionCreated(customerId, subscription, supabase);
      break;
    }

    case 'customer.subscription.updated':
      await handleSubscriptionUpdated(
        event.data.object as Stripe.Subscription,
        supabase
      );
      break;

    case 'customer.subscription.deleted':
      await handleSubscriptionDeleted(
        event.data.object as Stripe.Subscription,
        supabase
      );
      break;

    case 'invoice.payment_succeeded':
      await handleInvoicePaymentSucceeded(
        event.data.object as Stripe.Invoice & {
          subscription: string | Stripe.Subscription;
        },
        supabase
      );
      break;

    case 'invoice.payment_failed':
      await handleInvoicePaymentFailed(
        event.data.object as Stripe.Invoice & {
          subscription: string | Stripe.Subscription;
        },
        supabase
      );
      break;

    default:
      log.info({ eventType: event.type }, 'Unhandled event type');
  }
}

async function handleSessionCreated(
  customerId: string,
  subscriptionId: string,
  supabase: SupabaseClient
) {
  const subscription = await findSubscriptionByCustomerId(customerId, supabase);

  await updateSubscription(subscription.id, {
    stripe_subscription_id: subscriptionId,
    status: 'active',
  });
}

async function handleSubscriptionCreated(
  customerId: string,
  subscription: Stripe.Subscription,
  supabase: SupabaseClient
) {
  const dbSubscription = await findSubscriptionByCustomerId(
    customerId,
    supabase
  );

  const priceId = subscription.items.data[0]?.price.id;
  const plan = getPlanFromPriceId(priceId);
  const currentPeriodEnd = new Date(
    (subscription.items.data[0]?.current_period_end || Date.now() / 1000) * 1000
  ).toISOString();

  await updateSubscription(dbSubscription.id, {
    plan: plan as SubscriptionPlan,
    current_period_end: currentPeriodEnd,
    stripe_subscription_id: subscription.id,
    status: subscription.status as SubscriptionStatus,
  });
}

async function handleSubscriptionUpdated(
  subscription: Stripe.Subscription,
  supabase: SupabaseClient
) {
  log.info(
    { subscriptionId: subscription.id, customerId: subscription.customer },
    'Handling Subscription Updated event'
  );

  // Try finding by customer ID instead, as it's less likely to change during upgrade
  const customerId =
    typeof subscription.customer === 'string'
      ? subscription.customer
      : subscription.customer.id;
  if (!customerId) {
    log.error(
      { subscriptionId: subscription.id },
      'Customer ID missing in subscription update event'
    );
    throw new AppError('Customer ID missing', '400');
  }

  // Use findSubscriptionByCustomerId
  const dbSubscription = await findSubscriptionByCustomerId(
    customerId,
    supabase
  );

  // Now that we have the correct DB record, proceed with updates
  const priceId = subscription.items.data[0]?.price.id;
  const plan = getPlanFromPriceId(priceId);

  await updateSubscription(dbSubscription.id, {
    plan: plan as SubscriptionPlan,
    status: subscription.status as SubscriptionStatus,
    cancel_at_period_end: subscription.cancel_at_period_end,
    // Make sure to update the subscription ID if it changed!
    stripe_subscription_id: subscription.id,
    current_period_end: new Date(
      (subscription.items.data[0]?.current_period_end || Date.now() / 1000) *
        1000
    ).toISOString(),
  });
  log.info(
    { dbSubscriptionId: dbSubscription.id, newStripeSubId: subscription.id },
    'Subscription record updated'
  );
}

async function handleSubscriptionDeleted(
  subscription: Stripe.Subscription,
  supabase: SupabaseClient
) {
  const customerId =
    typeof subscription.customer === 'string'
      ? subscription.customer
      : subscription.customer.id;
  if (!customerId) return;

  const dbSubscription = await findSubscriptionByCustomerId(
    customerId,
    supabase
  );

  await updateSubscription(dbSubscription.id, {
    status: 'canceled',
    plan: 'free',
    cancel_at_period_end: true,
  });
}

async function handleInvoicePaymentSucceeded(
  invoice: Stripe.Invoice & { subscription: string | Stripe.Subscription },
  supabase: SupabaseClient
) {
  const customerId =
    invoice.customer &&
    (typeof invoice.customer === 'string'
      ? invoice.customer
      : invoice.customer.id);
  if (!customerId) return;

  const dbSubscription = await findSubscriptionByCustomerId(
    customerId,
    supabase
  );
  const periodEnd = invoice.lines.data[0]?.period?.end;

  if (periodEnd) {
    await updateSubscription(dbSubscription.id, {
      current_period_end: new Date(periodEnd * 1000).toISOString(),
      status: 'active',
    });
  }
}

async function handleInvoicePaymentFailed(
  invoice: Stripe.Invoice & { subscription: string | Stripe.Subscription },
  supabase: SupabaseClient
) {
  const customerId =
    invoice.customer &&
    (typeof invoice.customer === 'string'
      ? invoice.customer
      : invoice.customer.id);
  if (!customerId) return;

  const dbSubscription = await findSubscriptionByCustomerId(
    customerId,
    supabase
  );

  await updateSubscription(dbSubscription.id, {
    status: 'past_due',
  });
}

// Helper functions
async function findSubscriptionByCustomerId(
  customerId: string,
  supabase: SupabaseClient
) {
  const { data, error } = await supabase
    .from('subscriptions')
    .select('*')
    .eq('stripe_customer_id', customerId)
    .single();

  if (error || !data) {
    log.error({ error, customerId }, 'Subscription not found by customer ID');
    throw new AppError('Subscription not found', '404');
  }

  return data;
}

async function handleAddonPurchase(
  session: Stripe.Checkout.Session,
  supabase: SupabaseClient
) {
  const { userId, addonType, tokens, credits } = session.metadata || {};

  if (!userId || !addonType) {
    log.warn('Invalid addon purchase metadata', { metadata: session.metadata });
    return;
  }

  if (addonType === 'tokens' && tokens) {
    // Add tokens to user's quota
    const yearMonth = new Date().toISOString().slice(0, 7);

    // Insert or update the user's token grant
    await supabase.rpc('increment_monthly_token_usage', {
      p_user_id: userId,
      p_year_month: yearMonth,
      p_tokens: -parseInt(tokens), // Negative to add to quota
    });

    log.info('Token purchase processed', { userId, tokens });
  } else if (addonType === 'images' && credits) {
    // Add image credits to user's grants
    await supabase.from('image_credit_grants').insert({
      user_id: userId,
      credits: parseInt(credits),
      reason: 'addon_purchase',
      expires_at: null, // No expiration for purchased credits
    });

    log.info('Image credit purchase processed', { userId, credits });
  }
}

function getPlanFromPriceId(priceId?: string): string {
  const starterPrice = process.env.STRIPE_PRICE_STARTER;
  const premiumPrice = process.env.STRIPE_PRICE_PREMIUM;

  if (!starterPrice || !premiumPrice) {
    log.error('Stripe price IDs are not configured properly.');
    throw new AppError('Server configuration error', '500');
  }

  if (priceId === starterPrice) return 'starter';
  if (priceId === premiumPrice) return 'premium';

  log.warn({ priceId }, 'Unknown price ID received');
  return 'free';
}
