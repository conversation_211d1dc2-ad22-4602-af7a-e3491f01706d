import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { logger } from '@/lib/logger';
const log = logger.child({
  module: 'auth/callback',
});

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);

  log.info('auth callback', { searchParams, url: request.url });

  const code = searchParams.get('code');
  const next = searchParams.get('next') ?? '/';

  if (code) {
    const supabase = await createClient();
    const { error } = await supabase.auth.exchangeCodeForSession(code);

    if (!error) {
      const redirectUrl = `${process.env.NEXT_PUBLIC_SITE_URL}${next}`;
      log.info('redirecting to', { redirectUrl });
      return NextResponse.redirect(redirectUrl);
    }
  }

  const errorRedirectUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/auth/auth-code-error`;
  log.info('redirecting to error', { errorRedirectUrl });
  return NextResponse.redirect(errorRedirectUrl);
}
