import { DatabaseService } from '@/lib/supabase/db';
import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { handleError } from '@/lib/error';
import { Tables } from '@/types/database.types';

type Prompt = Tables<'prompts'>;

interface PromptResponse {
  data: Prompt | null;
  error: string | null;
}

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<PromptResponse>> {
  const { id } = await params;
  const supabase = await createClient();
  const db = DatabaseService.getInstance(supabase);

  try {
    const activeUser = await db.getCurrentUser();

    if (!activeUser) {
      return NextResponse.json(
        { data: null, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { data: prompt, error } = await supabase
      .from('prompts')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      throw error;
    }

    // Check access permissions
    if (prompt.visibility === 'private' && prompt.owner_id !== activeUser.id) {
      return NextResponse.json(
        { data: null, error: 'Access denied' },
        { status: 403 }
      );
    }

    if (prompt.visibility === 'workspace') {
      // TODO: Check if user has access to the workspace
      // For now, we'll allow access if they're authenticated
    }

    return NextResponse.json({ data: prompt, error: null });
  } catch (error) {
    const appError = handleError(error);
    return NextResponse.json(
      { data: null, error: appError.message },
      { status: appError.statusCode }
    );
  }
}

export async function PATCH(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<PromptResponse>> {
  const { id } = await params;
  const supabase = await createClient();
  const db = DatabaseService.getInstance(supabase);

  try {
    const activeUser = await db.getCurrentUser();

    if (!activeUser) {
      return NextResponse.json(
        { data: null, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // First, get the existing prompt to check ownership
    const { data: existingPrompt, error: fetchError } = await supabase
      .from('prompts')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError) {
      throw fetchError;
    }

    // Check if user can edit this prompt
    if (existingPrompt.owner_id !== activeUser.id) {
      return NextResponse.json(
        { data: null, error: 'Access denied' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { title, body_md, tags, default_model, visibility } = body;

    const updateData: Partial<Prompt> = {
      updated_at: new Date().toISOString(),
    };

    // Only update fields that are provided
    if (title !== undefined) updateData.title = title;
    if (body_md !== undefined) updateData.body_md = body_md;
    if (tags !== undefined) updateData.tags = tags;
    if (default_model !== undefined) updateData.default_model = default_model;
    if (visibility !== undefined) updateData.visibility = visibility;

    // If body_md is being updated, increment version and create version history
    if (body_md !== undefined && body_md !== existingPrompt.body_md) {
      updateData.version = existingPrompt.version + 1;

      // Create version history entry
      await supabase.from('prompt_versions').insert({
        prompt_id: id,
        body_md,
        version: updateData.version,
        edited_by: activeUser.id,
      });
    }

    const { data: prompt, error } = await supabase
      .from('prompts')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return NextResponse.json({ data: prompt, error: null });
  } catch (error) {
    const appError = handleError(error);
    return NextResponse.json(
      { data: null, error: appError.message },
      { status: appError.statusCode }
    );
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<{ data: null; error: string | null }>> {
  const { id } = await params;
  const supabase = await createClient();
  const db = DatabaseService.getInstance(supabase);

  try {
    const activeUser = await db.getCurrentUser();

    if (!activeUser) {
      return NextResponse.json(
        { data: null, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // First, get the existing prompt to check ownership
    const { data: existingPrompt, error: fetchError } = await supabase
      .from('prompts')
      .select('owner_id')
      .eq('id', id)
      .single();

    if (fetchError) {
      throw fetchError;
    }

    // Check if user can delete this prompt
    if (existingPrompt.owner_id !== activeUser.id) {
      return NextResponse.json(
        { data: null, error: 'Access denied' },
        { status: 403 }
      );
    }

    // Soft delete by setting a deleted flag or remove entirely
    const { error } = await supabase.from('prompts').delete().eq('id', id);

    if (error) {
      throw error;
    }

    return NextResponse.json({ data: null, error: null });
  } catch (error) {
    const appError = handleError(error);
    return NextResponse.json(
      { data: null, error: appError.message },
      { status: appError.statusCode }
    );
  }
}
