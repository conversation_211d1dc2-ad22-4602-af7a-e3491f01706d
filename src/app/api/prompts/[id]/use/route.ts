import { DatabaseService } from '@/lib/supabase/db';
import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { handleError } from '@/lib/error';

interface UsePromptResponse {
  data: { success: boolean } | null;
  error: string | null;
}

export async function POST(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<UsePromptResponse>> {
  const { id } = await params;
  const supabase = await createClient();
  const db = DatabaseService.getInstance(supabase);

  try {
    const activeUser = await db.getCurrentUser();

    if (!activeUser) {
      return NextResponse.json(
        { data: null, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { sent = false } = body;

    // Check if prompt exists and is accessible
    const { data: prompt, error: promptError } = await supabase
      .from('prompts')
      .select('id, owner_id, visibility, workspace_id')
      .eq('id', id)
      .single();

    if (promptError) {
      throw promptError;
    }

    // Check access permissions
    if (prompt.visibility === 'private' && prompt.owner_id !== activeUser.id) {
      return NextResponse.json(
        { data: null, error: 'Access denied' },
        { status: 403 }
      );
    }

    // Record usage
    const { error: usageError } = await supabase.from('prompt_usage').insert({
      user_id: activeUser.id,
      prompt_id: id,
      sent,
      inserted_at: new Date().toISOString(),
    });

    if (usageError) {
      throw usageError;
    }

    return NextResponse.json({
      data: { success: true },
      error: null,
    });
  } catch (error) {
    const appError = handleError(error);
    return NextResponse.json(
      { data: null, error: appError.message },
      { status: appError.statusCode }
    );
  }
}
