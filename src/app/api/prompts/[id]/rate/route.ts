import { DatabaseService } from '@/lib/supabase/db';
import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { handleError } from '@/lib/error';

interface RateResponse {
  data: { rating: number } | null;
  error: string | null;
}

export async function POST(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<RateResponse>> {
  const { id } = await params;
  const supabase = await createClient();
  const db = DatabaseService.getInstance(supabase);

  try {
    const activeUser = await db.getCurrentUser();

    if (!activeUser) {
      return NextResponse.json(
        { data: null, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { rating } = body;

    if (!rating || rating < 1 || rating > 5) {
      return NextResponse.json(
        { data: null, error: 'Rating must be between 1 and 5' },
        { status: 400 }
      );
    }

    // Check if prompt exists and is accessible
    const { data: prompt, error: promptError } = await supabase
      .from('prompts')
      .select('id, owner_id, visibility, workspace_id')
      .eq('id', id)
      .single();

    if (promptError) {
      throw promptError;
    }

    // Check access permissions
    if (prompt.visibility === 'private' && prompt.owner_id !== activeUser.id) {
      return NextResponse.json(
        { data: null, error: 'Access denied' },
        { status: 403 }
      );
    }

    // Upsert the rating
    const { error: existingRatingError } = await supabase
      .from('prompt_ratings')
      .select('rating')
      .eq('user_id', activeUser.id)
      .eq('prompt_id', id)
      .single();

    if (existingRatingError) {
      throw existingRatingError;
    }

    const { error: ratingError } = await supabase
      .from('prompt_ratings')
      .upsert({
        user_id: activeUser.id,
        prompt_id: id,
        rating,
        created_at: new Date().toISOString(),
      });

    if (ratingError) {
      throw ratingError;
    }

    // Update the prompt's rating aggregates
    const { data: allRatings, error: aggregateError } = await supabase
      .from('prompt_ratings')
      .select('rating')
      .eq('prompt_id', id);

    if (aggregateError) {
      throw aggregateError;
    }

    const ratingSum = allRatings.reduce((sum, r) => sum + r.rating, 0);
    const ratingCount = allRatings.length;

    await supabase
      .from('prompts')
      .update({
        rating_sum: ratingSum,
        rating_count: ratingCount,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id);

    return NextResponse.json({
      data: { rating },
      error: null,
    });
  } catch (error) {
    const appError = handleError(error);
    return NextResponse.json(
      { data: null, error: appError.message },
      { status: appError.statusCode }
    );
  }
}
