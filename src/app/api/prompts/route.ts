import { DatabaseService } from '@/lib/supabase/db';
import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { handleError } from '@/lib/error';
import { Tables, TablesInsert } from '@/types/database.types';

type Prompt = Tables<'prompts'>;
type PromptInsert = TablesInsert<'prompts'>;

interface PromptsResponse {
  data: Prompt[];
  nextCursor: string | null;
  error: string | null;
}

interface CreatePromptResponse {
  data: Prompt | null;
  error: string | null;
}

export async function GET(
  request: Request
): Promise<NextResponse<PromptsResponse>> {
  const url = new URL(request.url);
  const query = url.searchParams.get('q') || '';
  const tags = url.searchParams.getAll('tags');
  const visibility = url.searchParams.get('visibility') as
    | 'private'
    | 'workspace'
    | 'public'
    | null;
  const limit = parseInt(url.searchParams.get('limit') || '20');
  const offset = parseInt(url.searchParams.get('offset') || '0');

  const supabase = await createClient();
  const db = DatabaseService.getInstance(supabase);

  try {
    const activeUser = await db.getCurrentUser();

    if (!activeUser) {
      return NextResponse.json(
        { data: [], nextCursor: null, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    let promptsQuery = supabase
      .from('prompts')
      .select('*')
      .order('created_at', { ascending: false });

    // Apply visibility filters
    if (visibility) {
      promptsQuery = promptsQuery.eq('visibility', visibility);
    } else {
      // Show all prompts user can access: their private ones, workspace ones they can see, and public ones
      promptsQuery = promptsQuery.or(
        `owner_id.eq.${activeUser.id},visibility.eq.public,and(visibility.eq.workspace,workspace_id.not.is.null)`
      );
    }

    // Apply search filter
    if (query) {
      promptsQuery = promptsQuery.or(
        `title.ilike.%${query}%,body_md.ilike.%${query}%,tags.cs.{${query}}`
      );
    }

    // Apply tags filter
    if (tags.length > 0) {
      promptsQuery = promptsQuery.contains('tags', tags);
    }

    // Apply pagination
    const { data: prompts, error } = await promptsQuery.range(
      offset,
      offset + limit - 1
    );

    if (error) {
      throw error;
    }

    // Calculate next cursor
    const nextCursor =
      prompts && prompts.length === limit ? (offset + limit).toString() : null;

    return NextResponse.json({
      data: prompts || [],
      nextCursor,
      error: null,
    });
  } catch (error) {
    const appError = handleError(error);
    return NextResponse.json(
      { data: [], nextCursor: null, error: appError.message },
      { status: appError.statusCode }
    );
  }
}

export async function POST(
  request: Request
): Promise<NextResponse<CreatePromptResponse>> {
  const supabase = await createClient();
  const db = DatabaseService.getInstance(supabase);

  try {
    const activeUser = await db.getCurrentUser();

    if (!activeUser) {
      return NextResponse.json(
        { data: null, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      title,
      body_md,
      tags = [],
      default_model,
      visibility = 'private',
      workspace_id,
    } = body;

    if (!title || !body_md) {
      return NextResponse.json(
        { data: null, error: 'Title and body are required' },
        { status: 400 }
      );
    }

    // Validate visibility and workspace_id combination
    if (visibility === 'workspace' && !workspace_id) {
      return NextResponse.json(
        {
          data: null,
          error: 'Workspace ID is required for workspace visibility',
        },
        { status: 400 }
      );
    }

    const promptData: PromptInsert = {
      owner_id: activeUser.id,
      title,
      body_md,
      tags,
      default_model,
      visibility,
      workspace_id: visibility === 'workspace' ? workspace_id : null,
      rating_sum: 0,
      rating_count: 0,
      version: 1,
    };

    const { data: prompt, error } = await supabase
      .from('prompts')
      .insert(promptData)
      .select()
      .single();

    if (error) {
      throw error;
    }

    // Create initial version entry
    await supabase.from('prompt_versions').insert({
      prompt_id: prompt.id,
      body_md,
      version: 1,
      edited_by: activeUser.id,
    });

    return NextResponse.json({ data: prompt, error: null });
  } catch (error) {
    const appError = handleError(error);
    return NextResponse.json(
      { data: null, error: appError.message },
      { status: appError.statusCode }
    );
  }
}
