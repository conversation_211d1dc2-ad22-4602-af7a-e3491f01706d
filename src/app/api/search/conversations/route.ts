import { DatabaseService } from '@/lib/supabase/db';
import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { handleError } from '@/lib/error';
import { GroupConversation } from '@/lib/supabase/types';

interface SearchResponse {
  results: GroupConversation[];
  error: string | null;
}

export async function GET(
  request: Request
): Promise<NextResponse<SearchResponse>> {
  const url = new URL(request.url);
  const query = url.searchParams.get('query') || '';
  const limit = parseInt(url.searchParams.get('limit') || '30');
  const offset = parseInt(url.searchParams.get('offset') || '0');

  // If no query is provided, return empty results
  if (!query.trim()) {
    return NextResponse.json({ results: [], error: null });
  }

  const supabase = await createClient();
  const db = DatabaseService.getInstance(supabase);

  try {
    const activeUser = await db.getCurrentUser();

    if (!activeUser) {
      return NextResponse.json(
        { results: [], error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Use the dedicated database method to search while excluding temporary chats
    const conversations = await db.searchGroupConversations(
      query,
      limit,
      offset
    );

    return NextResponse.json({
      results: conversations,
      error: null,
    });
  } catch (error) {
    const appError = handleError(error);
    const statusCode = appError.statusCode;

    return NextResponse.json(
      { results: [], error: appError.message },
      { status: statusCode }
    );
  }
}
