'use client';
import { Card } from '@/components/ui/card';
import Image from 'next/image';
import { ThemeProvider } from '@/components/theme-provider';
import { ThemeToggle } from '@/components/theme-toggle';

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ThemeProvider attribute='class' defaultTheme='system' enableSystem>
      <div className='absolute right-4 top-4'>
        <ThemeToggle />
      </div>
      <div className='flex min-h-screen items-center justify-center bg-background p-4'>
        <Card className='w-full max-w-md overflow-hidden rounded-lg border-border bg-background shadow-lg'>
          {/* Logo */}
          <div className='flex justify-center bg-muted p-6 items-center'>
            <Image
              src='/logo/logo5.png'
              alt='Chatty App Logo'
              width={60}
              height={60}
              priority
            />
            <h1 className='text-2xl font-bold tracking-tight text-foreground'>
              <PERSON><PERSON>
            </h1>
          </div>

          {children}
        </Card>
      </div>
    </ThemeProvider>
  );
}
