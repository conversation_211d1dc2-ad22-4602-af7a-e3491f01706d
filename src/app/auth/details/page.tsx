'use client';
import UserDetailsForm from '@/components/auth/Details'; // Corrected import path
import { useContext, useEffect } from 'react'; // Import hooks
import { UserContext } from '@/providers/AuthProvider'; // Import UserContext
import { useRouter } from 'next/navigation';
import { Loader2 } from 'lucide-react'; // Import Loader
import { logger } from '@/lib/logger';
const log = logger.child({
  module: 'DetailsPage',
});

export default function DetailsPage() {
  const router = useRouter();
  const authContext = useContext(UserContext);

  useEffect(() => {
    // Wait for context to be available
    if (!authContext) return;

    const { isAuthenticated, user } = authContext;

    // If not authenticated, redirect to login
    if (!isAuthenticated) {
      log.info('Details page: Not authenticated, redirecting to login.');
      router.replace('/auth/login');
      return;
    }

    // If authenticated and names exist, redirect to chat
    if (user) {
      const firstName = user.user_metadata?.first_name;
      const lastName = user.user_metadata?.last_name;
      if (firstName && lastName) {
        log.info('Details page: User already has name, redirecting to chat.');
        router.replace('/chat');
      }
      // If user exists but names are missing, stay on page
    } else {
      // Should technically be covered by !isAuthenticated, but good safety check
      log.info(
        'Details page: User object missing despite being authenticated?, redirecting to login.'
      );
      router.replace('/auth/login');
    }
  }, [authContext, router]);

  // Show loading or skeleton while checking auth state
  if (!authContext || !authContext.user) {
    // Basic loading state with theme-appropriate styling
    return (
      <div className='flex min-h-screen items-center justify-center bg-background p-4'>
        <Loader2 className='h-8 w-8 animate-spin text-foreground/50' />
      </div>
    );
  }

  // User is authenticated and needs to enter details, render the form within the layout
  return <UserDetailsForm />;
}
