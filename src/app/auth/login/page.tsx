'use client';
import Login from '@/components/auth/Login';
import { useAuth } from '@/components/auth/useAuth';

export default function LoginPage() {
  // Renamed component
  const {
    email,
    isLoading,
    error,
    message,
    // Removed showVerify and OTP-related state/handlers
    handleEmailSignIn,
    handleGoogleSignIn,
    setEmail,
    setError,
    setMessage,
  } = useAuth();

  return (
    <Login
      email={email}
      setEmail={setEmail}
      setError={setError}
      setMessage={setMessage}
      isLoading={isLoading}
      loginWithGoogle={handleGoogleSignIn}
      loginWithEmail={handleEmailSignIn}
      error={error}
      message={message}
    />
  );
}
