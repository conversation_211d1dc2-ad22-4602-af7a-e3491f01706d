'use client';

import { useEffect, useState, useContext } from 'react';
import { toast } from 'sonner';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { ChatContext } from '@/providers/ChatProvider';
import { LockIcon, ChevronDownIcon } from 'lucide-react';
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectGroup,
  SelectLabel,
  SelectItem,
} from '@/components/ui/select';
import {
  Collapsible,
  CollapsibleTrigger,
  CollapsibleContent,
} from '@/components/ui/collapsible';
import { useSubscription } from '@/providers/SubscriptionProvider';
import { UserContext } from '@/providers/AuthProvider';
import { isModelVisible } from '@/lib/modelVisibility';
import { useAnalytics } from '@/hooks/useAnalytics';

export default function ModelsSettings() {
  const { providers } = useContext(ChatContext);
  const { accessibleModelIds } = useSubscription();
  const { me, fetchMe } = useContext(UserContext);
  const analytics = useAnalytics();

  const [isSaving, setIsSaving] = useState(false);
  const [settings, setSettings] = useState<{
    defaultModelId: string | null;
    explicitlyHiddenModelIds: string[];
    explicitlyShownModelIds: string[];
  }>({
    defaultModelId: null,
    explicitlyHiddenModelIds: [],
    explicitlyShownModelIds: [],
  });

  // Get all available models
  const allModels = providers
    .flatMap((provider) => provider.models)
    .sort((a, b) => (a.priority || 0) - (b.priority || 0));

  useEffect(() => {
    if (!me) return;
    setSettings({
      defaultModelId: me?.preferences?.default_model_id || null,
      explicitlyHiddenModelIds:
        (me?.preferences?.explicitly_hidden_model_ids as string[]) || [],
      explicitlyShownModelIds:
        (me?.preferences?.explicitly_shown_model_ids as string[]) || [],
    });
  }, [me]);

  const handleSaveSettings = async () => {
    setIsSaving(true);
    try {
      // Get the previous settings for analytics comparison
      const previousSettings = {
        defaultModelId: me?.preferences?.default_model_id || null,
        explicitlyHiddenModelIds:
          (me?.preferences?.explicitly_hidden_model_ids as string[]) || [],
        explicitlyShownModelIds:
          (me?.preferences?.explicitly_shown_model_ids as string[]) || [],
      };

      // Send updated settings to the API
      const response = await fetch('/api/me', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          defaultModelId:
            settings.defaultModelId === 'last_used'
              ? null
              : settings.defaultModelId,
          explicitlyHiddenModelIds: settings.explicitlyHiddenModelIds,
          explicitlyShownModelIds: settings.explicitlyShownModelIds,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save preferences');
      }

      // Track settings changed events
      if (settings.defaultModelId !== previousSettings.defaultModelId) {
        analytics.trackSettingsChanged(
          'defaultModelId',
          settings.defaultModelId
        );
      }

      // Track changes to hidden/shown models if they changed
      if (
        JSON.stringify(settings.explicitlyHiddenModelIds) !==
        JSON.stringify(previousSettings.explicitlyHiddenModelIds)
      ) {
        analytics.trackSettingsChanged(
          'explicitlyHiddenModelIds',
          `${settings.explicitlyHiddenModelIds.length} models hidden`
        );
      }

      if (
        JSON.stringify(settings.explicitlyShownModelIds) !==
        JSON.stringify(previousSettings.explicitlyShownModelIds)
      ) {
        analytics.trackSettingsChanged(
          'explicitlyShownModelIds',
          `${settings.explicitlyShownModelIds.length} models shown`
        );
      }

      toast.success('Model preferences saved');
      await fetchMe();
    } catch (error) {
      console.error('Error saving model preferences:', error);
      toast.error(
        error instanceof Error
          ? error.message
          : 'Failed to save model preferences'
      );
    } finally {
      setIsSaving(false);
    }
  };

  // Check if a model is visible based on default visibility and user preferences
  const checkModelVisibility = (modelId: string) => {
    const model = allModels.find((m) => m.id === modelId);
    if (!model) return false;

    // Use the utility function but with the component's state rather than user preferences
    // since we're managing state locally before saving to the database
    return isModelVisible(modelId, model.is_visible_by_default ?? true, {
      explicitly_hidden_model_ids: settings.explicitlyHiddenModelIds,
      explicitly_shown_model_ids: settings.explicitlyShownModelIds,
    });
  };

  // Toggle visibility for a model
  const toggleModelVisibility = (modelId: string) => {
    const model = allModels.find((m) => m.id === modelId);
    if (!model) return;

    setSettings((prev) => {
      // Current state before toggle
      const isCurrentlyVisible = checkModelVisibility(modelId);

      // Calculate new state based on toggle action and default visibility
      if (isCurrentlyVisible) {
        // Toggling OFF - hide the model
        if (model.is_visible_by_default ?? true) {
          // For visible-by-default: add to explicitly hidden
          return {
            ...prev,
            explicitlyHiddenModelIds: [
              ...prev.explicitlyHiddenModelIds,
              modelId,
            ],
            // Also remove from explicitly shown if it was there for some reason
            explicitlyShownModelIds: prev.explicitlyShownModelIds.filter(
              (id) => id !== modelId
            ),
          };
        } else {
          // For hidden-by-default: remove from explicitly shown
          return {
            ...prev,
            explicitlyShownModelIds: prev.explicitlyShownModelIds.filter(
              (id) => id !== modelId
            ),
          };
        }
      } else {
        // Toggling ON - show the model
        if (model.is_visible_by_default ?? true) {
          // For visible-by-default: remove from explicitly hidden
          return {
            ...prev,
            explicitlyHiddenModelIds: prev.explicitlyHiddenModelIds.filter(
              (id) => id !== modelId
            ),
          };
        } else {
          // For hidden-by-default: add to explicitly shown
          return {
            ...prev,
            explicitlyShownModelIds: [...prev.explicitlyShownModelIds, modelId],
            // Also remove from explicitly hidden if it was there for some reason
            explicitlyHiddenModelIds: prev.explicitlyHiddenModelIds.filter(
              (id) => id !== modelId
            ),
          };
        }
      }
    });
  };

  const setDefaultModel = (modelId: string) => {
    setSettings((prev) => {
      // If setting a default model, ensure it's visible
      let updatedExplicitlyHiddenModelIds = prev.explicitlyHiddenModelIds;
      let updatedExplicitlyShownModelIds = prev.explicitlyShownModelIds;

      const model = allModels.find((m) => m.id === modelId);
      if (model) {
        if (model.is_visible_by_default ?? true) {
          // If visible by default, remove from explicitly hidden
          updatedExplicitlyHiddenModelIds =
            prev.explicitlyHiddenModelIds.filter((id) => id !== modelId);
        } else {
          // If hidden by default, add to explicitly shown
          if (!prev.explicitlyShownModelIds.includes(modelId)) {
            updatedExplicitlyShownModelIds = [
              ...prev.explicitlyShownModelIds,
              modelId,
            ];
          }
        }
      }

      return {
        ...prev,
        defaultModelId: modelId,
        explicitlyHiddenModelIds: updatedExplicitlyHiddenModelIds,
        explicitlyShownModelIds: updatedExplicitlyShownModelIds,
      };
    });
  };

  // if (isLoading) {
  //   return (
  //     <div className='space-y-4'>
  //       <h2 className='text-2xl font-bold mb-6'>Model Preferences</h2>
  //       <div className='space-y-4'>
  //         {[1, 2, 3, 4].map((i) => (
  //           <div
  //             key={i}
  //             className='flex items-center justify-between p-3 border rounded-md'
  //           >
  //             <Skeleton className='h-5 w-32' />
  //             <div className='flex items-center gap-4'>
  //               <Skeleton className='h-5 w-5 rounded-full' />
  //               <Skeleton className='h-5 w-10' />
  //             </div>
  //           </div>
  //         ))}
  //       </div>
  //     </div>
  //   );
  // }

  return (
    <div className='space-y-4'>
      <div className='flex justify-between items-center mb-6'>
        <h2 className='text-2xl font-bold'>Model Preferences</h2>
        <Button onClick={handleSaveSettings} disabled={isSaving}>
          {isSaving ? 'Saving...' : 'Save Settings'}
        </Button>
      </div>

      <div className='mb-8'>
        <h3 className='text-lg font-medium mb-4'>Default Model</h3>
        <p className='text-sm text-muted-foreground mb-4'>
          Select which model will be used by default when starting new
          conversations.
        </p>
        <Select
          value={settings.defaultModelId || 'last_used'}
          onValueChange={setDefaultModel}
        >
          <SelectTrigger className='w-[280px]'>
            <SelectValue placeholder='Select default model' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='last_used'>Use Last Used Model</SelectItem>
            {providers.map((provider) => (
              <SelectGroup key={provider.id}>
                <SelectLabel>{provider.display_name}</SelectLabel>
                {provider.models
                  .filter((model) => checkModelVisibility(model.id))
                  .sort((a, b) => (a.priority || 0) - (b.priority || 0))
                  .map((model) => (
                    <SelectItem
                      key={model.id}
                      value={model.id}
                      disabled={!accessibleModelIds.includes(model.id)}
                    >
                      <div className='flex items-center'>
                        {model.display_name}
                        {!accessibleModelIds.includes(model.id) && (
                          <LockIcon className='ml-2 h-3 w-3 text-amber-500' />
                        )}
                      </div>
                    </SelectItem>
                  ))}
              </SelectGroup>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div>
        <h3 className='text-lg font-medium mb-4'>Visible Models</h3>
        <p className='text-sm text-muted-foreground mb-4'>
          Choose which models are shown in the model selector. Hidden models
          will not appear in the UI.
        </p>
        <div className='space-y-3'>
          {providers.map((provider) => (
            <Collapsible key={provider.id} defaultOpen={false}>
              <CollapsibleTrigger asChild>
                <Button
                  variant='ghost'
                  className='w-full flex justify-between items-center px-3 py-2 border rounded-md font-medium text-left'
                >
                  {provider.display_name}
                  <ChevronDownIcon className='h-4 w-4 transition-transform [&[data-state=open]]:rotate-180' />
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <div className='space-y-2 mt-2 ml-4 border-l pl-4'>
                  {allModels
                    .filter((model) => model.provider?.id === provider.id)
                    .sort((a, b) => (a.priority || 0) - (b.priority || 0))
                    .map((model) => (
                      <div
                        key={model.id}
                        className='flex items-center justify-between p-3 border rounded-md'
                      >
                        <div className='flex flex-col'>
                          <span>{model.display_name}</span>
                          {!model.is_visible_by_default && (
                            <span className='text-xs text-muted-foreground'>
                              Hidden by default
                            </span>
                          )}
                        </div>
                        <div className='flex items-center gap-4'>
                          {settings.defaultModelId === model.id && (
                            <span className='text-xs bg-primary/20 text-primary px-2 py-1 rounded-full'>
                              Default
                            </span>
                          )}
                          <Switch
                            checked={checkModelVisibility(model.id)}
                            onCheckedChange={() =>
                              toggleModelVisibility(model.id)
                            }
                            disabled={settings.defaultModelId === model.id}
                          />
                        </div>
                      </div>
                    ))}
                </div>
              </CollapsibleContent>
            </Collapsible>
          ))}
        </div>
      </div>
    </div>
  );
}
