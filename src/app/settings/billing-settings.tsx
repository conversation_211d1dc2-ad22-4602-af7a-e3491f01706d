'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ArrowRightIcon, Zap, CreditCard } from 'lucide-react';
import { SubscriptionPlan, SUBSCRIPTION_PLANS } from '@/lib/supabase/types';
import { toast } from 'sonner';
import { useSubscription } from '@/providers/SubscriptionProvider';

interface UsageData {
  tokens?: {
    used: number;
    remaining: number;
    total: number;
  };
  images?: {
    used: number;
    remaining: number;
    total: number;
  };
  comparisons?: {
    used: number;
    remaining: number;
    total: number;
  };
}

export default function BillingSettings() {
  const { subscription } = useSubscription();
  const [actionLoading, setActionLoading] = useState(false);
  const [usageData, setUsageData] = useState<UsageData>({});
  const [addonPurchasing, setAddonPurchasing] = useState<string | null>(null);

  useEffect(() => {
    fetchUsageData();
  }, [subscription]);

  const fetchUsageData = async () => {
    try {
      // Fetch current usage data
      const response = await fetch('/api/me/quota');
      if (response.ok) {
        const data = await response.json();
        setUsageData(data);
      }
    } catch (error) {
      console.error('Failed to fetch usage data:', error);
    }
  };

  const purchaseAddon = async (
    addonType: 'tokens' | 'images',
    quantity = 1
  ) => {
    try {
      setAddonPurchasing(addonType);
      const response = await fetch('/api/purchases/addons', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ addonType, quantity }),
      });

      const { url, error } = await response.json();

      if (error) {
        throw new Error(error);
      }

      if (url) {
        window.location.href = url;
      }
    } catch (error: unknown) {
      toast.error(
        error instanceof Error ? error.message : 'Failed to purchase add-on'
      );
    } finally {
      setAddonPurchasing(null);
    }
  };

  const upgradeSubscription = async (plan: SubscriptionPlan) => {
    if (!subscription) return;

    try {
      setActionLoading(true);
      const response = await fetch('/api/subscriptions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          plan,
          returnUrl: window.location.href,
        }),
      });

      const { data, error } = await response.json();

      if (error) {
        throw new Error(error);
      }

      if (data.url) {
        window.location.href = data.url;
      } else if (data.message) {
        toast.info(data.message);
      }
    } catch (error: unknown) {
      toast.error(
        error instanceof Error
          ? error.message
          : 'Failed to upgrade subscription'
      );
    } finally {
      setActionLoading(false);
    }
  };

  const manageSubscription = async () => {
    if (!subscription) return;

    try {
      setActionLoading(true);
      const response = await fetch('/api/subscriptions', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'manage',
          returnUrl: window.location.href,
        }),
      });

      const { data, error } = await response.json();

      if (error) {
        throw new Error(error);
      }

      if (data.url) {
        window.location.href = data.url;
      }
    } catch (error: unknown) {
      toast.error(
        error instanceof Error ? error.message : 'Failed to manage subscription'
      );
    } finally {
      setActionLoading(false);
    }
  };

  const renderPlanCard = (plan: SubscriptionPlan) => {
    const planDetails = SUBSCRIPTION_PLANS[plan];
    const isCurrentPlan = subscription?.plan === plan;
    const isActive =
      subscription?.status === 'active' || subscription?.status === 'trialing';

    return (
      <Card className='flex flex-col' key={plan}>
        <CardHeader>
          <div className='flex items-center justify-between gap-2'>
            <CardTitle>{planDetails.name}</CardTitle>
            {isCurrentPlan && isActive && (
              <Badge
                variant='outline'
                className='text-green-600 bg-green-50 dark:bg-green-950 border-green-200 dark:border-green-800'
              >
                Active
              </Badge>
            )}
            {isCurrentPlan && subscription?.cancel_at_period_end && (
              <Badge
                variant='outline'
                className='text-orange-600 bg-orange-50 dark:bg-orange-950 border-orange-200 dark:border-orange-800'
              >
                Cancelling
              </Badge>
            )}
          </div>
          <CardDescription>
            {planDetails.price}
            {plan !== 'free' ? '/month' : ''}
          </CardDescription>
        </CardHeader>
        <CardContent className='flex-grow'>
          <ul className='space-y-2'>
            {planDetails.features.map((feature, index) => (
              <li key={index} className='flex items-center gap-2'>
                <span className='h-1.5 w-1.5 rounded-full bg-primary'></span>
                {feature}
              </li>
            ))}
          </ul>
        </CardContent>
        <CardFooter>
          {isCurrentPlan ? (
            <Button
              variant='outline'
              disabled={actionLoading || plan === 'free'}
              onClick={manageSubscription}
              className='w-full'
            >
              Manage Subscription
            </Button>
          ) : (
            <Button
              onClick={() => upgradeSubscription(plan)}
              disabled={actionLoading}
              className='w-full'
            >
              {subscription && subscription.plan === 'free' && plan !== 'free'
                ? 'Upgrade'
                : subscription &&
                  subscription.plan !== 'free' &&
                  plan === 'free'
                ? 'Downgrade'
                : 'Switch Plan'}
              <ArrowRightIcon className='ml-2 h-4 w-4' />
            </Button>
          )}
        </CardFooter>
      </Card>
    );
  };

  // if (isLoading) {
  //   return (
  //     <div className='space-y-6'>
  //       <div>
  //         <h2 className='text-2xl font-bold'>Billing & Subscription</h2>
  //         <p className='text-muted-foreground'>
  //           Loading subscription information...
  //         </p>
  //       </div>
  //     </div>
  //   );
  // }

  return (
    <div className='space-y-6'>
      <div>
        <h2 className='text-2xl font-bold'>Billing & Subscription</h2>
        <p className='text-muted-foreground'>
          Manage your subscription plan and payment methods
        </p>
      </div>

      <Separator />

      <div className='space-y-8'>
        {/* Usage Dashboard */}
        {subscription && subscription.plan !== 'free' && (
          <div className='space-y-4'>
            <h3 className='text-lg font-medium'>Usage Overview</h3>
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
              {/* Token Usage */}
              {usageData.tokens && (
                <Card>
                  <CardHeader className='pb-3'>
                    <CardTitle className='text-sm font-medium'>
                      Token Usage
                    </CardTitle>
                  </CardHeader>
                  <CardContent className='space-y-2'>
                    <div className='flex justify-between text-sm'>
                      <span>Used</span>
                      <span>
                        {usageData.tokens.used.toLocaleString()} /{' '}
                        {usageData.tokens.total.toLocaleString()}
                      </span>
                    </div>
                    <Progress
                      value={
                        usageData.tokens.total > 0
                          ? (usageData.tokens.used / usageData.tokens.total) *
                            100
                          : 0
                      }
                      className='h-2'
                    />
                    <p className='text-xs text-muted-foreground'>
                      {usageData.tokens.remaining.toLocaleString()} tokens
                      remaining
                    </p>
                  </CardContent>
                  {subscription.plan === 'premium' && (
                    <CardFooter className='pt-0'>
                      <Button
                        size='sm'
                        variant='outline'
                        onClick={() => purchaseAddon('tokens')}
                        disabled={addonPurchasing === 'tokens'}
                        className='w-full'
                      >
                        <Zap className='w-3 h-3 mr-1' />
                        Buy 100K Tokens ($3)
                      </Button>
                    </CardFooter>
                  )}
                </Card>
              )}

              {/* Image Credits */}
              {usageData.images && (
                <Card>
                  <CardHeader className='pb-3'>
                    <CardTitle className='text-sm font-medium'>
                      Image Credits
                    </CardTitle>
                  </CardHeader>
                  <CardContent className='space-y-2'>
                    <div className='flex justify-between text-sm'>
                      <span>Used</span>
                      <span>
                        {usageData.images.used} / {usageData.images.total}
                      </span>
                    </div>
                    <Progress
                      value={
                        usageData.images.total > 0
                          ? (usageData.images.used / usageData.images.total) *
                            100
                          : 0
                      }
                      className='h-2'
                    />
                    <p className='text-xs text-muted-foreground'>
                      {usageData.images.remaining} credits remaining
                    </p>
                  </CardContent>
                  <CardFooter className='pt-0'>
                    <Button
                      size='sm'
                      variant='outline'
                      onClick={() => purchaseAddon('images')}
                      disabled={addonPurchasing === 'images'}
                      className='w-full'
                    >
                      <CreditCard className='w-3 h-3 mr-1' />
                      Buy 1000 Credits ($5)
                    </Button>
                  </CardFooter>
                </Card>
              )}

              {/* Comparison Usage (Premium only) */}
              {subscription.plan === 'premium' && usageData.comparisons && (
                <Card>
                  <CardHeader className='pb-3'>
                    <CardTitle className='text-sm font-medium'>
                      Model Comparisons
                    </CardTitle>
                  </CardHeader>
                  <CardContent className='space-y-2'>
                    <div className='flex justify-between text-sm'>
                      <span>Used Today</span>
                      <span>
                        {usageData.comparisons.used} /{' '}
                        {usageData.comparisons.total}
                      </span>
                    </div>
                    <Progress
                      value={
                        usageData.comparisons.total > 0
                          ? (usageData.comparisons.used /
                              usageData.comparisons.total) *
                            100
                          : 0
                      }
                      className='h-2'
                    />
                    <p className='text-xs text-muted-foreground'>
                      {usageData.comparisons.remaining} comparisons remaining
                      today
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        )}

        {/* Subscription Plans */}
        <div className='space-y-4'>
          <h3 className='text-lg font-medium'>Plans</h3>
          <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6'>
            {Object.keys(SUBSCRIPTION_PLANS).map((plan) =>
              renderPlanCard(plan as SubscriptionPlan)
            )}
          </div>
        </div>

        {/* Current Subscription Details */}
        {subscription && subscription.plan !== 'free' && (
          <Card className='mt-8'>
            <CardHeader>
              <CardTitle>Current Subscription</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='flex flex-col sm:flex-row justify-between gap-4'>
                <div>
                  <div className='flex items-center gap-2 mb-1'>
                    <h4 className='font-semibold'>
                      {SUBSCRIPTION_PLANS[subscription.plan].name} Plan
                    </h4>
                    <Badge
                      variant='outline'
                      className={
                        subscription.status === 'active' ||
                        subscription.status === 'trialing'
                          ? 'text-green-600 bg-green-50 dark:bg-green-950 border-green-200 dark:border-green-800'
                          : 'text-red-600 bg-red-50 dark:bg-red-950 border-red-200 dark:border-red-800'
                      }
                    >
                      {subscription.status === 'active'
                        ? 'Active'
                        : subscription.status === 'trialing'
                        ? 'Trialing'
                        : subscription.status === 'past_due'
                        ? 'Past Due'
                        : subscription.status === 'canceled'
                        ? 'Canceled'
                        : subscription.status}
                    </Badge>
                  </div>
                  <p className='text-sm text-muted-foreground'>
                    {
                      subscription.cancel_at_period_end &&
                      typeof subscription.current_period_end === 'number'
                        ? `Your plan will be cancelled on ${new Date(
                            subscription.current_period_end * 1000
                          ).toLocaleDateString()}.`
                        : !subscription.cancel_at_period_end &&
                          typeof subscription.current_period_end === 'number'
                        ? `Your plan renews on ${new Date(
                            subscription.current_period_end * 1000
                          ).toLocaleDateString()}.`
                        : 'Renewal date information is unavailable.' // Handle cases where renewal date isn't available or not a number
                    }
                  </p>
                </div>
                <Button
                  variant='outline'
                  disabled={actionLoading}
                  onClick={manageSubscription}
                  className='shrink-0'
                >
                  Manage Subscription
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
