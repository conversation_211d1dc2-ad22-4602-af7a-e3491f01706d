'use client';

import { useState, useEffect, useContext } from 'react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { SunIcon, MoonIcon, MonitorIcon } from 'lucide-react';
import { useTheme } from 'next-themes';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { SidebarContext } from '@/providers/SidebarProvider';
import { SidebarState } from '@/providers/SidebarProvider';
import codeThemes from '@/lib/code-themes.json';
import { useCodeTheme } from '@/providers/CodeThemeProvider';
export default function AppearanceSettings() {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const { saveCollapseSidebarState, collapseSidebarState } =
    useContext(SidebarContext);
  const { codeTheme, setCodeTheme } = useCodeTheme();
  // const [fontSize, setFontSize] = useState(16)
  // const [reducedMotion, setReducedMotion] = useState(false)
  // const [colorScheme, setColorScheme] = useState('default')

  // Handle initial client-side render
  useEffect(() => {
    setMounted(true);
  }, []);

  // Don't render the radio group until client-side
  if (!mounted) {
    return null;
  }

  const handleThemeChange = (value: string) => {
    setTheme(value);
  };

  const handleCodeThemeChange = (value: string) => {
    setCodeTheme(value);
  };

  return (
    <div className='space-y-6'>
      <div>
        <h2 className='text-2xl font-bold'>Appearance</h2>
        <p className='text-muted-foreground'>
          Customize how the chat interface looks and feels
        </p>
      </div>

      <Separator />

      <div className='space-y-8'>
        <div className='space-y-4'>
          <h3 className='text-lg font-medium'>Theme</h3>
          <RadioGroup
            defaultValue={theme}
            onValueChange={handleThemeChange}
            className='flex flex-col sm:flex-row gap-4'
          >
            <Label
              htmlFor='light'
              className='flex items-center space-x-2 border rounded-lg p-4 flex-1 cursor-pointer'
            >
              <RadioGroupItem value='light' id='light' />
              <div className='flex items-center gap-2'>
                <SunIcon className='h-5 w-5' />
                <span>Light</span>
              </div>
            </Label>

            <Label
              htmlFor='dark'
              className='flex items-center space-x-2 border rounded-lg p-4 flex-1 cursor-pointer'
            >
              <RadioGroupItem value='dark' id='dark' />
              <div className='flex items-center gap-2'>
                <MoonIcon className='h-5 w-5' />
                <span>Dark</span>
              </div>
            </Label>

            <Label
              htmlFor='system'
              className='flex items-center space-x-2 border rounded-lg p-4 flex-1 cursor-pointer'
            >
              <RadioGroupItem value='system' id='system' />
              <div className='flex items-center gap-2'>
                <MonitorIcon className='h-5 w-5' />
                <span>System</span>
              </div>
            </Label>
          </RadioGroup>
        </div>
        {/* Settings for sidebar. Users can choose to collapse the sidebar to hide it or minimize it */}

        <div className='space-y-4'>
          <h3 className='text-lg font-medium'>Sidebar</h3>
          <p className='text-sm text-muted-foreground'>
            Customize what happens when you collapse the sidebar
          </p>
          <Select
            value={collapseSidebarState}
            onValueChange={(value: SidebarState) =>
              saveCollapseSidebarState(value)
            }
          >
            <SelectTrigger>
              <SelectValue placeholder='Select a side' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='hidden'>Hide</SelectItem>
              <SelectItem value='minimized'>Minimize</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className='space-y-4'>
          <h3 className='text-lg font-medium'>Code Theme</h3>
          <p className='text-sm text-muted-foreground'>
            Customize the code theme
          </p>
          <Select value={codeTheme} onValueChange={handleCodeThemeChange}>
            <SelectTrigger>
              <SelectValue placeholder='Select a side' />
            </SelectTrigger>
            <SelectContent>
              {codeThemes.themes.map((theme) => (
                <SelectItem key={theme.id} value={theme.id}>
                  {theme.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        {/*
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Font Size</h3>
            <span className="text-sm text-muted-foreground">{fontSize}px</span>
          </div>
          <Slider value={[fontSize]} min={12} max={24} step={1} onValueChange={(value) => setFontSize(value[0])} />
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>Small</span>
            <span>Medium</span>
            <span>Large</span>
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Color Scheme</h3>
          <RadioGroup
            value={colorScheme}
            onValueChange={setColorScheme}
            className="grid grid-cols-1 sm:grid-cols-3 gap-4"
          >
            <Label
              htmlFor="default-color"
              className="flex items-center space-x-2 border rounded-lg p-4 cursor-pointer"
            >
              <RadioGroupItem value="default" id="default-color" />
              <span>Default</span>
            </Label>

            <Label
              htmlFor="blue-color"
              className="flex items-center space-x-2 border rounded-lg p-4 cursor-pointer"
            >
              <RadioGroupItem value="blue" id="blue-color" />
              <span>Blue</span>
            </Label>

            <Label
              htmlFor="green-color"
              className="flex items-center space-x-2 border rounded-lg p-4 cursor-pointer"
            >
              <RadioGroupItem value="green" id="green-color" />
              <span>Green</span>
            </Label>
          </RadioGroup>
        </div> */}

        {/* <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <h3 className="text-lg font-medium">Reduced Motion</h3>
            <p className="text-sm text-muted-foreground">Minimize animations and transitions</p>
          </div>
          <Switch checked={reducedMotion} onCheckedChange={setReducedMotion} />
        </div> */}

        <Button>Save Preferences</Button>
      </div>
    </div>
  );
}
