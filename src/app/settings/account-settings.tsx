'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  LogOutIcon,
  TrashIcon,
  ArchiveIcon,
  AlertTriangleIcon,
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { SearchIcon, RefreshCwIcon } from 'lucide-react';
import { useContext, useEffect, useState } from 'react';
import { toast } from 'sonner';
import { Conversation } from '@/lib/supabase/db';
import { UserContext } from '@/providers/AuthProvider';

export default function AccountSettings() {
  const { signOut: handleLogout } = useContext(UserContext);


  const handleDeleteAllChats = async () => {
    const response = await fetch('/api/groupConversations', {
      method: 'DELETE',
    });
    if (response.ok) {
      toast.success('All chats deleted');
    } else {
      toast.error('Failed to delete chats');
    }
  };

  const [archivedChats, setArchivedChats] = useState<Conversation[]>([]);

  const [searchQuery, setSearchQuery] = useState('');

  const filteredArchivedChats = archivedChats.filter((chat) =>
    chat?.title?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleRestoreChat = async (id: string) => {
    const response = await fetch(`/api/groupConversations/archive/${id}`, {
      method: 'PUT',
    });
    if (response.ok) {
      toast.success('Chat restored');
      fetchArchivedChats();
    } else {
      toast.error('Failed to restore chat');
    }
  };

  const handleDeleteArchivedChat = async (id: string) => {
    const response = await fetch(`/api/groupConversations/${id}`, {
      method: 'DELETE',
    });
    if (response.ok) {
      toast.success('Chat deleted');
    } else {
      toast.error('Failed to delete chat');
    }
  };

  const fetchArchivedChats = async () => {
    const response = await fetch('/api/groupConversations/archive');
    const data = await response.json();
    setArchivedChats(data.data);
  };

  useEffect(() => {
    fetchArchivedChats();
  }, []);

  return (
    <div className='space-y-6'>
      <div>
        <h2 className='text-2xl font-bold'>Account Settings</h2>
        <p className='text-muted-foreground'>
          Manage your account preferences and data
        </p>
      </div>

      <Separator />

      <div className='space-y-6'>
        {/* Session Management */}
        <div className='space-y-4'>
          <h3 className='text-lg font-medium'>Session</h3>

          <div className='flex items-center justify-between border rounded-lg p-4'>
            <div className='space-y-0.5'>
              <div className='font-medium flex items-center gap-2'>
                <LogOutIcon className='h-4 w-4' />
                Logout
              </div>
              <p className='text-sm text-muted-foreground'>
                Sign out from your current session
              </p>
            </div>
            <Button onClick={handleLogout} variant='outline'>
              Logout
            </Button>
          </div>

          {/* <div className="flex items-center justify-between border rounded-lg p-4">
            <div className="space-y-0.5">
              <div className="font-medium flex items-center gap-2">
                <ToggleLeftIcon className="h-4 w-4" />
                Auto Logout
              </div>
              <p className="text-sm text-muted-foreground">Automatically log out after 30 minutes of inactivity</p>
            </div>
            <Switch />
          </div> */}
        </div>

        {/* Chat Management */}
        <div className='space-y-4'>
          <h3 className='text-lg font-medium'>Chat Management</h3>

          <div className='flex items-center justify-between border rounded-lg p-4'>
            <div className='space-y-0.5'>
              <div className='font-medium flex items-center gap-2'>
                <TrashIcon className='h-4 w-4' />
                Delete All Chats
              </div>
              <p className='text-sm text-muted-foreground'>
                Permanently delete all your chat history
              </p>
            </div>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  variant='outline'
                  className='text-destructive border-destructive hover:bg-destructive/10'
                >
                  Delete All
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. This will permanently delete
                    all your chat history and remove your data from our servers.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleDeleteAllChats}
                    className='bg-destructive text-destructive-foreground'
                  >
                    Delete All
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>

          <div className='flex items-center justify-between border rounded-lg p-4'>
            <div className='space-y-0.5'>
              <div className='font-medium flex items-center gap-2'>
                <ArchiveIcon className='h-4 w-4' />
                Archived Chats
              </div>
              <p className='text-sm text-muted-foreground'>
                View and manage your archived conversations
              </p>
            </div>
            <Dialog>
              <DialogTrigger asChild>
                <Button variant='outline'>Manage</Button>
              </DialogTrigger>
              <DialogContent className='sm:max-w-[525px] max-h-[80vh]'>
                <DialogHeader>
                  <DialogTitle>Archived Chats</DialogTitle>
                  <DialogDescription>
                    View and manage your archived conversations. You can restore
                    chats to your active list or delete them permanently.
                  </DialogDescription>
                </DialogHeader>

                <div className='relative mt-4 mb-4'>
                  <SearchIcon className='absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground' />
                  <Input
                    placeholder='Search archived chats...'
                    className='pl-9'
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>

                <div className='overflow-y-auto max-h-[50vh] -mx-6 px-6'>
                  {filteredArchivedChats.length > 0 ? (
                    <div className='space-y-3'>
                      {filteredArchivedChats.map((chat) => (
                        <div
                          key={chat.id}
                          className='border rounded-lg p-3 hover:bg-accent/50 transition-colors'
                        >
                          <div className='flex justify-between items-start mb-1'>
                            <h4 className='font-medium'>{chat.title}</h4>
                            <span className='text-xs text-muted-foreground'>
                              {chat.created_at}
                            </span>
                          </div>
                          <div className='flex justify-end gap-2'>
                            <Button
                              variant='outline'
                              size='sm'
                              className='h-8 gap-1'
                              onClick={() => handleRestoreChat(chat.id)}
                            >
                              <RefreshCwIcon className='h-3.5 w-3.5' />
                              Restore
                            </Button>
                            <Button
                              variant='outline'
                              size='sm'
                              className='h-8 text-destructive hover:bg-destructive/10 hover:text-destructive'
                              onClick={() => handleDeleteArchivedChat(chat.id)}
                            >
                              <TrashIcon className='h-3.5 w-3.5 mr-1' />
                              Delete
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className='text-center py-8 text-muted-foreground'>
                      {searchQuery
                        ? 'No archived chats match your search'
                        : 'No archived chats found'}
                    </div>
                  )}
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Danger Zone */}
        <div className='space-y-4'>
          <h3 className='text-lg font-medium text-destructive flex items-center gap-2'>
            <AlertTriangleIcon className='h-4 w-4' />
            Danger Zone
          </h3>

          <div className='border border-destructive/30 rounded-lg p-4 bg-destructive/5'>
            <div className='space-y-3'>
              <div className='space-y-0.5'>
                <div className='font-medium'>Delete Account</div>
                <p className='text-sm text-muted-foreground'>
                  Permanently delete your account and all associated data
                </p>
              </div>

              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant='destructive'>Delete Account</Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>
                      Are you absolutely sure?
                    </AlertDialogTitle>
                    <AlertDialogDescription>
                      This action cannot be undone. This will permanently delete
                      your account and remove all your data from our servers.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction className='bg-destructive text-destructive-foreground'>
                      Delete Account
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
