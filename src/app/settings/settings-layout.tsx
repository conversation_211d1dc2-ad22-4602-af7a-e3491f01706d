'use client';

import React from 'react';
import { useSearchPara<PERSON>, useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import {
  UserIcon,
  PaintbrushIcon as PaintBrushIcon,
  ArrowLeftIcon,
  CreditCardIcon,
  CodeIcon,
} from 'lucide-react';

interface SettingsLayoutProps {
  children: React.ReactNode;
}

export default function SettingsLayout({ children }: SettingsLayoutProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get the active tab from URL search param, default to 'appearance'
  const activeTab = searchParams.get('tab') || 'appearance';

  const tabs = [
    {
      id: 'account',
      label: 'Account',
      icon: <UserIcon className='h-5 w-5 mr-2' />,
    },
    {
      id: 'appearance',
      label: 'Appearance',
      icon: <PaintBrushIcon className='h-5 w-5 mr-2' />,
    },
    {
      id: 'billing',
      label: 'Billing',
      icon: <CreditCardIcon className='h-5 w-5 mr-2' />,
    },
    {
      id: 'models',
      label: 'Models',
      icon: <CodeIcon className='h-5 w-5 mr-2' />,
    },
  ];

  // Function to update the URL when a tab is clicked
  const setActiveTab = (tabId: string) => {
    const url = new URL(window.location.href);
    url.searchParams.set('tab', tabId);
    router.push(url.pathname + url.search);
  };

  return (
    <div className='container mx-auto py-8 px-4 md:px-6'>
      <div className='flex items-center mb-8'>
        <Button
          variant='outline'
          size='icon'
          onClick={() => router.push('/')}
          className='mr-4'
        >
          <ArrowLeftIcon className='h-5 w-5' />
        </Button>
        <h1 className='text-3xl font-bold'>Settings</h1>
      </div>

      <div className='flex flex-col md:flex-row gap-6'>
        {/* Side Navigation */}
        <div className='w-full md:w-64 shrink-0'>
          <Card className='p-2'>
            <nav className='flex flex-col space-y-1'>
              {tabs.map((tab) => (
                <Button
                  key={tab.id}
                  variant={activeTab === tab.id ? 'default' : 'ghost'}
                  className={cn(
                    'justify-start h-10',
                    activeTab === tab.id
                      ? 'bg-primary text-primary-foreground'
                      : ''
                  )}
                  onClick={() => setActiveTab(tab.id)}
                >
                  {tab.icon}
                  {tab.label}
                </Button>
              ))}
            </nav>
          </Card>
        </div>

        {/* Content Area */}
        <div className='flex-1'>
          <Card className='p-6'>
            {React.Children.toArray(children).map((child, index) => {
              const tabId = tabs[index]?.id;
              return (
                <div
                  key={tabId}
                  className={cn(
                    'transition-opacity duration-200',
                    activeTab === tabId ? 'block' : 'hidden'
                  )}
                >
                  {child}
                </div>
              );
            })}
          </Card>
        </div>
      </div>
    </div>
  );
}
