import { Skeleton } from '@/components/ui/skeleton';
import { Card } from '@/components/ui/card';

export default function SettingsLoading() {
  return (
    <div className='container mx-auto py-8 px-4 md:px-6 animate-pulse'>
      <div className='flex items-center mb-8'>
        <Skeleton className='h-10 w-10 mr-4 rounded-md' />
        <Skeleton className='h-8 w-40 rounded-md' />
      </div>

      <div className='flex flex-col md:flex-row gap-6'>
        {/* Side Navigation Skeleton */}
        <div className='w-full md:w-64 shrink-0'>
          <Card className='p-2'>
            <div className='flex flex-col space-y-1'>
              {[...Array(4)].map((_, i) => (
                <Skeleton key={i} className='h-10 w-full rounded-md' />
              ))}
            </div>
          </Card>
        </div>

        {/* Content Area Skeleton */}
        <div className='flex-1'>
          <Card className='p-6'>
            <Skeleton className='h-6 w-1/4 mb-4 rounded-md' />
            <Skeleton className='h-4 w-full mb-2 rounded-md' />
            <Skeleton className='h-4 w-3/4 mb-6 rounded-md' />
            <Skeleton className='h-10 w-1/3 rounded-md' />
          </Card>
        </div>
      </div>
    </div>
  );
}
