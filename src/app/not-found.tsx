import { But<PERSON> } from '@/components/ui/button';
import Link from 'next/link';
import { MoveLeft } from 'lucide-react';

export default function NotFound() {
  return (
    <div className='flex min-h-screen flex-col items-center justify-center bg-background px-4 text-center'>
      <div className='space-y-6 max-w-md'>
        <div className='space-y-2'>
          <h1 className='animate-fade-in text-9xl font-bold tracking-tighter text-primary'>
            404
          </h1>
          <div className='h-1 w-20 bg-primary mx-auto rounded-full animate-pulse'></div>
          <h2 className='text-3xl font-bold tracking-tight mt-4'>
            Page not found
          </h2>
          <p className='text-muted-foreground'>
            Sorry, we couldn&apos;t find the page you&apos;re looking for. It
            might have been moved, deleted, or never existed.
          </p>
        </div>

        <div className='flex flex-col sm:flex-row gap-4 justify-center'>
          <Button asChild className='gap-2'>
            <Link href='/'>
              <MoveLeft className='h-4 w-4' />
              Back to home
            </Link>
          </Button>
        </div>

        <div className='relative mt-8'>
          <div className='absolute -inset-0.5 rounded-lg bg-gradient-to-r from-primary to-secondary opacity-75 blur'></div>
          <div className='relative rounded-lg bg-background p-6'>
            <p className='text-sm'>
              Lost? Try checking the URL for typos or return to our homepage to
              start fresh.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
