'use client';

import * as Sentry from '@sentry/nextjs';
import NextError from 'next/error';
import { useEffect } from 'react';

// Conditional Bugsnag import for client-side
let Bugsnag: { notify: (error: Error, callback?: (event: unknown) => void) => void } | null = null;
if (process.env.NODE_ENV === 'production' && typeof window !== 'undefined') {
  try {
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    Bugsnag = require('@bugsnag/js');
  } catch (error) {
    console.warn('Bugsnag not available on client:', error);
  }
}

export default function GlobalError({ error }: { error: Error & { digest?: string } }) {
  useEffect(() => {
    // Only report to external services in production
    if (process.env.NODE_ENV === 'production') {
      // Report to Sentry
      try {
        Sentry.captureException(error, {
          tags: {
            errorBoundary: 'global',
            digest: error.digest,
          },
          extra: {
            digest: error.digest,
            timestamp: new Date().toISOString(),
          },
        });
      } catch (sentryError) {
        console.error('Failed to report to Sentry:', sentryError);
      }

      // Report to Bugsnag if available
      if (Bugsnag) {
        try {
          Bugsnag.notify(error, (event: unknown) => {
            const bugsnagEvent = event as {
              addMetadata: (section: string, key: string, value: unknown) => void;
              context?: string;
            };
            bugsnagEvent.addMetadata('errorBoundary', 'type', 'global');
            bugsnagEvent.addMetadata('errorBoundary', 'digest', error.digest);
            bugsnagEvent.addMetadata('errorBoundary', 'timestamp', new Date().toISOString());
            bugsnagEvent.context = 'Global Error Boundary';
          });
        } catch (bugsnagError) {
          console.error('Failed to report to Bugsnag:', bugsnagError);
        }
      }
    } else {
      // In development, just log to console
      console.error('Global error caught:', error);
    }
  }, [error]);

  return (
    <html>
      <body>
        {/* `NextError` is the default Next.js error page component. Its type
        definition requires a `statusCode` prop. However, since the App Router
        does not expose status codes for errors, we simply pass 0 to render a
        generic error message. */}
        <NextError statusCode={0} />
      </body>
    </html>
  );
}