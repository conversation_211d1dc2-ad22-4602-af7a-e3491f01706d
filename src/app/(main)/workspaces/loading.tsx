import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent, CardFooter } from '@/components/ui/card';

export default function WorkspacesLoading() {
  return (
    <div className="container mx-auto py-8 px-4 md:px-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-3">
          <Skeleton className="h-8 w-8 rounded-md" />
          <Skeleton className="h-8 w-48 rounded-md" />
        </div>
        <Skeleton className="h-10 w-32 rounded-md" />
      </div>

      {/* Search Bar */}
      <div className="mb-6">
        <Skeleton className="h-10 w-full max-w-md rounded-md" />
      </div>

      {/* Workspace Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <Card
            key={i}
            className="overflow-hidden border border-border/40 shadow-sm hover:shadow-md transition-all"
          >
            <CardContent className="p-0">
              {/* Workspace Icon Area */}
              <div className="h-28 bg-muted/30 flex items-center justify-center">
                <Skeleton className="h-12 w-12 rounded-full" />
              </div>
              
              {/* Workspace Info */}
              <div className="p-5">
                <Skeleton className="h-5 w-3/4 mb-2 rounded-md" />
                <Skeleton className="h-4 w-full mb-1 rounded-md" />
                <Skeleton className="h-4 w-2/3 rounded-md" />
              </div>
            </CardContent>
            
            {/* Footer */}
            <CardFooter className="border-t p-3 bg-muted/10 flex justify-between">
              <Skeleton className="h-8 w-20 rounded-md" />
              <Skeleton className="h-8 w-8 rounded-md" />
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  );
}
