'use client';

import { ReactNode } from 'react';
import { Briefcase } from 'lucide-react';
import { cn } from '@/lib/utils';

interface WorkspacesLayoutProps {
  children: ReactNode;
}

export default function WorkspacesLayout({ children }: WorkspacesLayoutProps) {
  return (
    <div className='flex h-full w-full flex-col overflow-hidden'>
      <header
        className={cn(
          'px-4 py-3 gap-2 border-b border-border',
          'backdrop-blur-sm sticky top-0 z-10'
        )}
      >
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            <Briefcase className='h-5 w-5 text-primary/80' />
            <h1 className='text-lg font-semibold'>Workspaces</h1>
          </div>
        </div>
      </header>

      <div className='flex flex-1 overflow-hidden'>
        <main className='flex-1 overflow-auto px-6 py-6'>{children}</main>
      </div>
    </div>
  );
}
