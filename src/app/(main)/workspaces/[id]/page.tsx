'use client';

import {
  useState,
  useEffect,
  useCallback,
  useRef,
  use,
  useContext,
} from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { Loader2, MessageSquare } from 'lucide-react';
import {
  Workspace,
  WorkspaceFile,
  WorkspaceNote,
  GroupConversation,
} from '@/lib/supabase/types';
import ChatArea from '@/components/chat/ChatArea';
import { ChatContext } from '@/providers/ChatProvider';
import { AppContext } from '@/providers/AppProvider';
import WorkspaceItemHeader from '@/components/workspaces/WorkspaceItemHeader';
import { createClient } from '@/utils/supabase/client';
import { RealtimePostgresChangesPayload } from '@supabase/supabase-js';
import { Card, CardContent } from '@/components/ui/card';

type Params = {
  params: Promise<{ id: string }>;
};

export default function WorkspacePage(props: Params) {
  const params = use(props.params);
  const router = useRouter();
  const [workspace, setWorkspace] = useState<Workspace | null>(null);
  const [files, setFiles] = useState<WorkspaceFile[]>([]);
  const [notes, setNotes] = useState<WorkspaceNote[]>([]);
  const [recentConversations, setRecentConversations] = useState<
    GroupConversation[]
  >([]);
  const { initializeNewChat, setSelectedConversation } =
    useContext(ChatContext);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [contextSheetOpen, setContextSheetOpen] = useState(false);
  const [showConversations, setShowConversations] = useState(true);
  const { viewportHeight, isMobile } = useContext(AppContext);
  const initialized = useRef(false);
  const supabase = createClient();

  const fetchData = useCallback(async () => {
    try {
      // Fetch data in parallel for better performance
      const [workspaceResponse, recentConversationsResponse] =
        await Promise.all([
          fetch(`/api/workspaces/${params.id}/all`),
          fetch(`/api/workspaces/${params.id}/conversations?limit=5`),
        ]);

      if (!workspaceResponse.ok)
        throw new Error('Failed to fetch workspace data');

      const [workspaceData, conversationsData] = await Promise.all([
        workspaceResponse.json(),
        recentConversationsResponse.ok
          ? recentConversationsResponse.json()
          : { data: [] },
      ]);

      setWorkspace(workspaceData.workspace);
      setFiles(workspaceData.files || []);
      setNotes(workspaceData.notes || []);
      setRecentConversations(conversationsData.data || []);
    } catch (error) {
      console.error('Error fetching workspace data:', error);
      toast.error('Failed to load workspace data');
    }
  }, [params.id]);

  const fetchRecentConversations = useCallback(async () => {
    if (recentConversations.length >= 5) return;
    try {
      const response = await fetch(
        `/api/workspaces/${params.id}/conversations`
      );
      if (!response.ok) throw new Error('Failed to fetch conversations');
      const data = await response.json();
      setRecentConversations(data.data || []);
    } catch (error) {
      console.error('Error fetching recent conversations:', error);
      // Don't show error toast for conversations as it's not critical
    }
  }, [params.id]);

  useEffect(() => {
    setIsLoading(true);
    Promise.all([fetchData(), fetchRecentConversations()]).finally(() =>
      setIsLoading(false)
    );

    // Set up real-time subscription for this specific workspace's status changes
    const channel = supabase
      .channel(`workspace-status-changes-${params.id}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'workspaces',
          filter: `id=eq.${params.id}`,
        },
        (payload: RealtimePostgresChangesPayload<Workspace>) => {
          if (!workspace) return; // Skip if no workspace is loaded yet

          const updatedWorkspace = payload.new as Workspace;

          // Update the workspace in state
          setWorkspace((current) => {
            if (!current) return updatedWorkspace;
            return { ...current, ...updatedWorkspace };
          });

          // Show toast notifications for status changes
          if ((payload.old as Workspace)?.status !== updatedWorkspace.status) {
            if (updatedWorkspace.status === 'completed') {
              toast.success('Workspace processing completed');
              // Refresh files since processing might have added new embeddings
              fetchData();
            } else if (updatedWorkspace.status === 'error') {
              toast.error('Error processing workspace');
            }
          }
        }
      )
      .subscribe();

    // Clean up the subscription when component unmounts
    return () => {
      supabase.removeChannel(channel);
    };
  }, [fetchData, fetchRecentConversations, params.id]);

  useEffect(() => {
    if (initialized.current) return;
    initialized.current = true;
    initializeNewChat(params.id);
  }, [initializeNewChat, params.id]);

  const handleDeleteWorkspace = async () => {
    if (!workspace) return;
    if (
      window.confirm(
        `Are you sure you want to delete workspace "${workspace.name}"? This action cannot be undone.`
      )
    ) {
      setIsSubmitting(true);
      try {
        const response = await fetch(`/api/workspaces/${workspace.id}`, {
          method: 'DELETE',
        });
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || 'Failed to delete workspace');
        }
        toast.success('Workspace deleted successfully');
        router.push('/workspaces');
        router.refresh();
      } catch (error) {
        console.error('Error deleting workspace:', error);
        toast.error(
          error instanceof Error ? error.message : 'Failed to delete workspace'
        );
        setIsSubmitting(false);
      }
    }
  };

  const handleContextUpdate = () => {
    toast.info('Context updated, refreshing data...');
    fetchData();
    setContextSheetOpen(true);
  };

  const handleNewChat = () => {
    setSelectedConversation(null);
    initializeNewChat(params.id);
    setShowConversations(false);
  };

  const handleSelectConversation = (conversation: GroupConversation) => {
    // setSelectedConversation(conversation);
    // setShowConversations(false);
    router.push(`/c/${conversation.id}`);
  };

  const handleToggleConversations = () => {
    setShowConversations(!showConversations);
  };

  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`;
    return date.toLocaleDateString();
  };

  if (isLoading) {
    return (
      <div className='flex items-center justify-center h-full'>
        <Loader2 className='h-8 w-8 animate-spin' />
      </div>
    );
  }

  if (!workspace) {
    return (
      <div className='flex flex-col items-center justify-center py-12'>
        <h2 className='text-2xl font-semibold mb-4'>Workspace not found</h2>
        <p className='text-muted-foreground mb-6'>
          The workspace you&apos;re looking for doesn&apos;t exist or you
          don&apos;t have access to it.
        </p>
        <Button onClick={() => router.push('/workspaces')}>
          Go Back to Workspaces
        </Button>
      </div>
    );
  }

  return (
    <div className='space-y-6 h-full justify-between flex flex-col'>
      <div className='space-y-4'>
        <WorkspaceItemHeader
          workspace={workspace}
          files={files}
          notes={notes}
          recentConversations={recentConversations}
          showConversations={showConversations}
          fetchData={fetchData}
          handleDeleteWorkspace={handleDeleteWorkspace}
          handleContextUpdate={handleContextUpdate}
          handleNewChat={handleNewChat}
          handleToggleConversations={handleToggleConversations}
          isSubmitting={isSubmitting}
          contextSheetOpen={contextSheetOpen}
          setContextSheetOpen={setContextSheetOpen}
        />

        {/* Recent Conversations */}
        {showConversations && recentConversations.length > 0 && (
          <div className='px-6'>
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3'>
              {recentConversations.map((conversation) => (
                <Card
                  key={conversation.id}
                  className='cursor-pointer hover:shadow-md transition-all duration-200 border-border/40'
                  onClick={() => handleSelectConversation(conversation)}
                >
                  <CardContent className='p-4'>
                    <div className='flex items-start gap-3'>
                      <div className='flex-shrink-0 mt-1'>
                        <MessageSquare className='h-4 w-4 text-muted-foreground' />
                      </div>
                      <div className='flex-1 min-w-0'>
                        <h4 className='text-sm font-medium truncate mb-1'>
                          {conversation.title || 'Untitled Conversation'}
                        </h4>
                        <p className='text-xs text-muted-foreground'>
                          {formatRelativeTime(conversation.created_at)}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}
      </div>

      <div
        className='flex-1 flex flex-col bg-chat-bg text-foreground overflow-hidden relative'
        style={{ height: isMobile ? `${viewportHeight}px` : '100%' }}
      >
        <div className='flex-1 overflow-hidden'>
          <ChatArea
            isTemporary={false}
            setIsTemporary={() => {}}
            chatState='workspace'
            workspaceId={workspace.id}
            onMessageSent={() => {
              fetchRecentConversations();
            }}
          />
        </div>
      </div>
    </div>
  );
}
