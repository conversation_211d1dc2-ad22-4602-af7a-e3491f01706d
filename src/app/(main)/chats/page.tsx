'use client';

import { useContext } from 'react';
import { ChatsList } from '@/components/chats/ChatsList';
import { ChatContext } from '@/providers/ChatProvider';

export default function ChatsPage() {
  const {
    loadedConversations,
    isConversationListLoading,
    hasInitiallyLoaded,
    hasMoreConversations,
    isFetchingMoreConversations,
    fetchMoreConversations,
    sseConnections,
  } = useContext(ChatContext);

  return (
    <div className='flex flex-col h-screen w-full'>
      <div className='container mx-auto flex-1 flex flex-col max-w-7xl min-h-0'>
        <ChatsList
          conversations={loadedConversations}
          isLoading={isConversationListLoading && !hasInitiallyLoaded}
          hasMore={hasMoreConversations}
          onLoadMore={fetchMoreConversations}
          isLoadingMore={isFetchingMoreConversations}
          sseConnections={sseConnections}
        />
      </div>
    </div>
  );
}
