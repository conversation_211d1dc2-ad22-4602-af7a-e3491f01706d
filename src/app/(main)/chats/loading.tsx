import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent } from '@/components/ui/card';

export default function ChatsLoading() {
  return (
    <div className="flex flex-col h-screen w-full">
      <div className="container mx-auto flex-1 flex flex-col max-w-7xl min-h-0 p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <Skeleton className="h-8 w-32 rounded-md" />
          <div className="flex items-center gap-2">
            <Skeleton className="h-10 w-32 rounded-md" />
            <Skeleton className="h-10 w-10 rounded-md" />
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex items-center gap-4 mb-6">
          <Skeleton className="h-10 flex-1 max-w-md rounded-md" />
          <Skeleton className="h-10 w-24 rounded-md" />
        </div>

        {/* Chat List */}
        <div className="flex-1 space-y-4 overflow-auto">
          {[...Array(8)].map((_, i) => (
            <Card key={i} className="p-4 hover:shadow-md transition-shadow">
              <CardContent className="p-0">
                <div className="flex items-start gap-4">
                  {/* Avatar */}
                  <Skeleton className="h-10 w-10 rounded-full flex-shrink-0" />
                  
                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-2">
                      <Skeleton className="h-5 w-48 rounded-md" />
                      <Skeleton className="h-4 w-16 rounded-md" />
                    </div>
                    
                    <div className="space-y-1">
                      <Skeleton className="h-4 w-full rounded-md" />
                      <Skeleton className="h-4 w-3/4 rounded-md" />
                    </div>
                    
                    {/* Tags/Labels */}
                    <div className="flex items-center gap-2 mt-3">
                      <Skeleton className="h-6 w-16 rounded-full" />
                      <Skeleton className="h-6 w-20 rounded-full" />
                    </div>
                  </div>
                  
                  {/* Actions */}
                  <div className="flex items-center gap-1">
                    <Skeleton className="h-8 w-8 rounded-md" />
                    <Skeleton className="h-8 w-8 rounded-md" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Load More */}
        <div className="flex items-center justify-center py-4">
          <Skeleton className="h-10 w-32 rounded-md" />
        </div>
      </div>
    </div>
  );
}
