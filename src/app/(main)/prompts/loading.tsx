import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent } from '@/components/ui/card';

export default function PromptsLoading() {
  return (
    <div className="flex h-full w-full">
      <div className="flex flex-1 gap-6 p-6 overflow-hidden">
        {/* Left panel - Search and filters */}
        <div className="w-full max-w-2xl mx-auto flex flex-col space-y-4">
          {/* Header */}
          <div className="flex items-center justify-between">
            <Skeleton className="h-8 w-48 rounded-md" />
            <Skeleton className="h-10 w-32 rounded-md" />
          </div>

          {/* Search and Filters */}
          <div className="space-y-4">
            <Skeleton className="h-10 w-full rounded-md" />

            {/* Filter tabs */}
            <div className="flex gap-2">
              {[...Array(4)].map((_, i) => (
                <Skeleton key={i} className="h-8 w-20 rounded-md" />
              ))}
            </div>
          </div>

          {/* Prompt Cards Grid */}
          <div className="flex-1 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {[...Array(8)].map((_, i) => (
                <Card key={i} className="p-4 hover:shadow-md transition-shadow">
                  <CardContent className="p-0 space-y-3">
                    {/* Title and visibility */}
                    <div className="flex items-start justify-between">
                      <Skeleton className="h-5 w-3/4 rounded-md" />
                      <Skeleton className="h-4 w-4 rounded-md" />
                    </div>

                    {/* Description */}
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-full rounded-md" />
                      <Skeleton className="h-4 w-2/3 rounded-md" />
                    </div>

                    {/* Tags */}
                    <div className="flex gap-2 flex-wrap">
                      {[...Array(3)].map((_, j) => (
                        <Skeleton key={j} className="h-6 w-16 rounded-full" />
                      ))}
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2 pt-2">
                      <Skeleton className="h-8 w-16 rounded-md" />
                      <Skeleton className="h-8 w-16 rounded-md" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
