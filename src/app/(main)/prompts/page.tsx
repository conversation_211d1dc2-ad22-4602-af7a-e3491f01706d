'use client';

import { useState, useEffect, useCallback, useContext } from 'react';
import { useRouter } from 'next/navigation';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Search, Plus, User, Globe, Building2 } from 'lucide-react';
import { Tables } from '@/types/database.types';
import { useDebounce } from '@/hooks/useDebounce';
import { PromptCard } from '@/components/prompts/PromptCard';
import { PromptPreview } from '@/components/prompts/PromptPreview';
import { VariableDialog } from '@/components/prompts/VariableDialog';
import { PromptEditorDrawer } from '@/components/prompts/PromptEditorDrawer';
import {
  parsePromptVariables,
  resolvePromptVariables,
  VariableResolutionContext,
} from '@/lib/promptVariables';
import { ChatContext } from '@/providers/ChatProvider';

type Prompt = Tables<'prompts'>;

interface PromptSearchFilters {
  query: string;
  tags: string[];
  visibility: 'all' | 'private' | 'workspace' | 'public';
}

export default function PromptsPage() {
  const router = useRouter();
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedPrompt, setSelectedPrompt] = useState<Prompt | null>(null);
  const [showVariableDialog, setShowVariableDialog] = useState(false);
  const [showEditor, setShowEditor] = useState(false);
  const [editingPrompt, setEditingPrompt] = useState<Prompt | null>(null);
  const { initializeNewChat } = useContext(ChatContext);

  const [filters, setFilters] = useState<PromptSearchFilters>({
    query: '',
    tags: [],
    visibility: 'all',
  });

  const debouncedQuery = useDebounce(filters.query, 300);
  const context: VariableResolutionContext = {};

  // Fetch prompts based on current filters
  const fetchPrompts = useCallback(async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams();

      if (debouncedQuery) {
        params.append('q', debouncedQuery);
      }

      if (filters.visibility !== 'all') {
        params.append('visibility', filters.visibility);
      }

      filters.tags.forEach((tag) => {
        params.append('tags', tag);
      });

      const response = await fetch(`/api/prompts?${params}`);
      const data = await response.json();

      if (data.error) {
        console.error('Failed to fetch prompts:', data.error);
        return;
      }

      setPrompts(data.data || []);
    } catch (error) {
      console.error('Error fetching prompts:', error);
    } finally {
      setLoading(false);
    }
  }, [debouncedQuery, filters.visibility, JSON.stringify(filters.tags)]);

  useEffect(() => {
    fetchPrompts();
  }, [fetchPrompts]);

  const handleUsePrompt = useCallback(
    async (prompt: Prompt) => {
      const variables = parsePromptVariables(prompt.body_md);

      if (variables.length > 0) {
        setSelectedPrompt(prompt);
        setShowVariableDialog(true);
      } else {
        // No variables, navigate to chat with prompt
        try {
          await fetch(`/api/prompts/${prompt.id}/use`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ sent: false }),
          });
        } catch (error) {
          console.error('Failed to track prompt usage:', error);
        }

        // Navigate to new chat with prompt populated
        const encodedPrompt = encodeURIComponent(prompt.body_md);
        const defaultModel = prompt.default_model;
        initializeNewChat(undefined, defaultModel || undefined);
        const url = `/chat?prompt=${encodedPrompt}${
          defaultModel
            ? `&defaultModel=${encodeURIComponent(defaultModel)}`
            : ''
        }`;
        router.push(url);
      }
    },
    [router, initializeNewChat]
  );

  const handleVariableSubmit = async (userValues: Record<string, string>) => {
    if (!selectedPrompt) return;

    const { resolvedBody } = resolvePromptVariables(
      selectedPrompt.body_md,
      userValues,
      context
    );

    // Track usage
    try {
      await fetch(`/api/prompts/${selectedPrompt.id}/use`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sent: false }),
      });
    } catch (error) {
      console.error('Failed to track prompt usage:', error);
    }

    // Navigate to new chat with resolved prompt
    const encodedPrompt = encodeURIComponent(resolvedBody);
    const defaultModel = selectedPrompt.default_model;
    const url = `/chat?prompt=${encodedPrompt}${
      defaultModel ? `&defaultModel=${encodeURIComponent(defaultModel)}` : ''
    }`;
    router.push(url);

    setShowVariableDialog(false);
    setSelectedPrompt(null);
  };

  const handleCreateNew = useCallback(() => {
    // Prevent focus issues by setting a small delay
    setTimeout(() => {
      setEditingPrompt(null);
      setShowEditor(true);
    }, 0);
  }, []);

  const handleEditPrompt = useCallback((prompt: Prompt) => {
    // Prevent focus issues by setting a small delay
    setTimeout(() => {
      setEditingPrompt(prompt);
      setShowEditor(true);
    }, 0);
  }, []);

  const handlePromptSaved = useCallback(() => {
    setShowEditor(false);
    setEditingPrompt(null);
    fetchPrompts(); // Refresh the list
  }, [fetchPrompts]);

  const handleSelectPrompt = useCallback((prompt: Prompt) => {
    setSelectedPrompt(prompt);
  }, []);

  const availableTags = Array.from(
    new Set(
      prompts
        .flatMap((p) => p.tags)
        .filter((tag): tag is string => tag !== null)
    )
  ).sort();

  const handleTagToggle = (tag: string) => {
    setFilters((prev) => ({
      ...prev,
      tags: prev.tags.includes(tag)
        ? prev.tags.filter((t) => t !== tag)
        : [...prev.tags, tag],
    }));
  };

  const clearFilters = () => {
    setFilters({
      query: '',
      tags: [],
      visibility: 'all',
    });
  };

  return (
    <div className='flex h-full w-full'>
      <div className='flex flex-1 gap-6 p-6 overflow-hidden'>
        {/* Left panel - Search and filters */}
        <div
          className={`${
            selectedPrompt ? 'w-1/3' : 'w-full max-w-2xl mx-auto'
          } flex flex-col space-y-4 transition-all duration-200`}
        >
          {/* Header */}
          <div className='flex items-center justify-between'>
            <h1 className='text-2xl font-semibold'>Prompt Library</h1>
            <Button onClick={handleCreateNew} size='sm'>
              <Plus className='w-4 h-4 mr-2' />
              New Prompt
            </Button>
          </div>

          {/* Search bar */}
          <div className='relative'>
            <Search className='absolute left-3 top-3 h-4 w-4 text-muted-foreground' />
            <Input
              placeholder='Search prompts...'
              value={filters.query}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, query: e.target.value }))
              }
              className='pl-10'
            />
          </div>

          {/* Visibility tabs */}
          <Tabs
            value={filters.visibility}
            onValueChange={(value) =>
              setFilters((prev) => ({
                ...prev,
                visibility: value as PromptSearchFilters['visibility'],
              }))
            }
          >
            <TabsList className='grid w-full grid-cols-4'>
              <TabsTrigger value='all' className='text-xs'>
                All
              </TabsTrigger>
              <TabsTrigger value='private' className='text-xs'>
                <User className='w-3 h-3' />
              </TabsTrigger>
              <TabsTrigger value='workspace' className='text-xs'>
                <Building2 className='w-3 h-3' />
              </TabsTrigger>
              <TabsTrigger value='public' className='text-xs'>
                <Globe className='w-3 h-3' />
              </TabsTrigger>
            </TabsList>
          </Tabs>

          {/* Tag filters */}
          {availableTags.length > 0 && (
            <div className='space-y-2'>
              <div className='flex items-center justify-between'>
                <span className='text-sm font-medium'>Tags</span>
                {filters.tags.length > 0 && (
                  <Button
                    variant='ghost'
                    size='sm'
                    onClick={clearFilters}
                    className='h-6 px-2 text-xs'
                  >
                    Clear
                  </Button>
                )}
              </div>
              <div className='flex flex-wrap gap-1 max-h-24 overflow-y-auto'>
                {availableTags.map((tag) => (
                  <Badge
                    key={tag}
                    variant={filters.tags.includes(tag) ? 'default' : 'outline'}
                    className='cursor-pointer text-xs'
                    onClick={() => handleTagToggle(tag)}
                  >
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Results count */}
          <div className='text-sm text-muted-foreground'>
            {loading ? 'Loading...' : `${prompts.length} prompts`}
          </div>

          {/* Prompt list */}
          <ScrollArea className='flex-1'>
            <div
              className={`${
                selectedPrompt
                  ? 'space-y-2'
                  : 'grid grid-cols-1 md:grid-cols-2 gap-3'
              } transition-all duration-200`}
            >
              {prompts.map((prompt) => (
                <PromptCard
                  key={prompt.id}
                  prompt={prompt}
                  onSelect={handleSelectPrompt}
                  onUse={handleUsePrompt}
                  onEdit={handleEditPrompt}
                  isSelected={selectedPrompt?.id === prompt.id}
                  compact={!selectedPrompt}
                />
              ))}

              {!loading && prompts.length === 0 && (
                <div className='col-span-full text-center py-12'>
                  <div className='mx-auto w-24 h-24 mb-4 rounded-full bg-muted flex items-center justify-center'>
                    <Search className='w-8 h-8 text-muted-foreground' />
                  </div>
                  <h3 className='font-medium text-lg mb-2'>No prompts found</h3>
                  <p className='text-muted-foreground mb-4'>
                    {filters.query || filters.tags.length > 0
                      ? 'Try adjusting your search criteria'
                      : 'Get started by creating your first prompt'}
                  </p>
                  <Button onClick={handleCreateNew}>
                    <Plus className='w-4 h-4 mr-2' />
                    Create First Prompt
                  </Button>
                </div>
              )}
            </div>
          </ScrollArea>
        </div>

        {/* Right panel - Preview */}
        {selectedPrompt && (
          <div className='flex-1 border-l pl-6'>
            <div className='flex items-center gap-2 mb-4'>
              <Button
                variant='ghost'
                size='sm'
                onClick={() => setSelectedPrompt(null)}
                className='p-1'
              >
                ← Back to Library
              </Button>
            </div>
            <PromptPreview
              prompt={selectedPrompt}
              context={context}
              onUse={() => handleUsePrompt(selectedPrompt)}
              onEdit={() => handleEditPrompt(selectedPrompt)}
            />
          </div>
        )}
      </div>

      {/* Variable input dialog */}
      {showVariableDialog && selectedPrompt && (
        <VariableDialog
          isOpen={showVariableDialog}
          onClose={() => {
            setShowVariableDialog(false);
            setSelectedPrompt(null);
          }}
          prompt={selectedPrompt}
          context={context}
          onSubmit={handleVariableSubmit}
        />
      )}

      {/* Prompt editor drawer */}
      {showEditor && (
        <PromptEditorDrawer
          isOpen={showEditor}
          onClose={() => {
            setShowEditor(false);
            setEditingPrompt(null);
          }}
          prompt={editingPrompt}
          onSave={handlePromptSaved}
        />
      )}
    </div>
  );
}
