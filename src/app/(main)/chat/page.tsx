'use client';
import Chat from '@/components/chat/Main';
import { useSearchParams } from 'next/navigation';

export default function ChatPage() {
  const searchParams = useSearchParams();
  const initialPrompt = searchParams.get('prompt');
  const defaultModel = searchParams.get('defaultModel');

  return <Chat initialPrompt={initialPrompt || undefined} initialDefaultModel={defaultModel || undefined} />;
}
