import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent } from '@/components/ui/card';

export default function SearchLoading() {
  return (
    <div className="flex flex-col h-full w-full">
      {/* Search Header */}
      <div className="border-b border-border p-6">
        <div className="max-w-4xl mx-auto">
          <Skeleton className="h-8 w-48 mb-4 rounded-md" />
          <Skeleton className="h-12 w-full rounded-lg" />
        </div>
      </div>

      {/* Search Results */}
      <div className="flex-1 overflow-auto p-6">
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Filter tabs */}
          <div className="flex gap-2">
            {[...Array(4)].map((_, i) => (
              <Skeleton key={i} className="h-8 w-20 rounded-md" />
            ))}
          </div>

          {/* Results */}
          <div className="space-y-4">
            {[...Array(6)].map((_, i) => (
              <Card key={i} className="p-4 hover:shadow-md transition-shadow">
                <CardContent className="p-0">
                  <div className="flex items-start gap-4">
                    {/* Avatar */}
                    <Skeleton className="h-10 w-10 rounded-full flex-shrink-0" />
                    
                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-2">
                        <Skeleton className="h-5 w-48 rounded-md" />
                        <Skeleton className="h-4 w-16 rounded-md" />
                      </div>
                      
                      <div className="space-y-2 mb-3">
                        <Skeleton className="h-4 w-full rounded-md" />
                        <Skeleton className="h-4 w-3/4 rounded-md" />
                        <Skeleton className="h-4 w-1/2 rounded-md" />
                      </div>
                      
                      {/* Tags/Labels */}
                      <div className="flex items-center gap-2">
                        <Skeleton className="h-6 w-16 rounded-full" />
                        <Skeleton className="h-6 w-20 rounded-full" />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
