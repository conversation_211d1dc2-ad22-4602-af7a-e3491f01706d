import { Skeleton } from '@/components/ui/skeleton';

export default function ChatLoading() {
  return (
    <div className="flex flex-col h-full w-full">
      {/* Chat Header */}
      <div className="border-b border-border p-4">
        <div className="flex items-center justify-between">
          <Skeleton className="h-6 w-48 rounded-md" />
          <div className="flex items-center gap-2">
            <Skeleton className="h-8 w-8 rounded-md" />
            <Skeleton className="h-8 w-8 rounded-md" />
          </div>
        </div>
      </div>

      {/* Chat Messages Area */}
      <div className="flex-1 overflow-auto p-4 space-y-6">
        {/* Welcome message skeleton */}
        <div className="flex items-start gap-3">
          <Skeleton className="h-8 w-8 rounded-full" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-3/4 rounded-md" />
            <Skeleton className="h-4 w-1/2 rounded-md" />
          </div>
        </div>

        {/* Chat message skeletons */}
        {[...Array(3)].map((_, i) => (
          <div key={i} className={`flex gap-3 ${i % 2 === 0 ? 'justify-start' : 'justify-end'}`}>
            {i % 2 === 0 && <Skeleton className="h-8 w-8 rounded-full" />}
            <div className={`max-w-[70%] space-y-2 ${i % 2 === 1 ? 'order-first' : ''}`}>
              <div className={`p-3 rounded-lg ${i % 2 === 0 ? 'bg-muted' : 'bg-primary/10'}`}>
                <Skeleton className="h-4 w-full rounded-md mb-2" />
                <Skeleton className="h-4 w-3/4 rounded-md" />
              </div>
            </div>
            {i % 2 === 1 && <Skeleton className="h-8 w-8 rounded-full" />}
          </div>
        ))}
      </div>

      {/* Chat Input Area */}
      <div className="border-t border-border p-4">
        <div className="flex items-end gap-2">
          <div className="flex-1">
            <Skeleton className="h-20 w-full rounded-lg" />
          </div>
          <Skeleton className="h-10 w-10 rounded-md" />
        </div>
        <div className="flex items-center justify-between mt-2">
          <div className="flex items-center gap-2">
            <Skeleton className="h-6 w-6 rounded-md" />
            <Skeleton className="h-6 w-6 rounded-md" />
          </div>
          <Skeleton className="h-4 w-24 rounded-md" />
        </div>
      </div>
    </div>
  );
}
