import { AttachmentPayload } from '@/lib/supabase/types';

// Parameters for initializing a chat stream
export interface ChatInitParams {
  message: string;
  model: string;
  conversationId?: string;
  comparisonIndex?: number | null;
  isComparison: boolean;
  groupConversationId: string;
  isTestMode: boolean;
  parentMessageId?: string;
  attachments?: AttachmentPayload[];
  useWebSearch: boolean;
  useImageGeneration: boolean;
  isTemporary: boolean;
  workspaceId?: string;
}

// Response from SSE init endpoint
export interface InitSseResponse {
  conversationId: string;
  groupConversationId: string;
  assistantMessageId: string;
  userMessageId: string;
  isNewConversation: boolean;
  isTemporary: boolean;
  workspaceId?: string;
}

/**
 * Initialize a server-sent events (SSE) chat stream.
 */
export async function initSse(
  params: ChatInitParams
): Promise<InitSseResponse> {
  const response = await fetch('/api/chat/stream-sse/init', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(params),
  });
  if (!response.ok) {
    throw new Error(`Error initializing SSE chat: ${response.statusText}`);
  }
  return response.json();
}

// Parameters for retrying or editing a chat message
export interface ChatUpdateParams {
  messageId: string;
  model: string;
  isTestMode: boolean;
  newContent?: string;
  isEdit: boolean;
  useWebSearch: boolean;
  useImageGeneration: boolean;
}

// Response from SSE update endpoint
export interface UpdateSseResponse {
  conversationId?: string;
  assistantMessageId: string;
}

/**
 * Send an update (retry or edit) via SSE.
 */
export async function updateSse(
  params: ChatUpdateParams
): Promise<UpdateSseResponse> {
  const response = await fetch('/api/chat/stream-sse/init', {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(params),
  });
  if (!response.ok) {
    throw new Error(`Error updating SSE chat: ${response.statusText}`);
  }
  return response.json();
}

// Removed NDJSON support; only SSE endpoints remain.
