import { AnalyticsBrowser } from '@june-so/analytics-next';
import { AnalyticsProvider, UserTraits, EventProperties } from './provider';

// Ensure JUNE_WRITE_KEY is available, likely via environment variables
const JUNE_WRITE_KEY = process.env.NEXT_PUBLIC_JUNE_WRITE_KEY;

export class JuneAnalyticsProvider implements AnalyticsProvider {
  private analytics: AnalyticsBrowser | null = null;

  init() {
    if (!JUNE_WRITE_KEY) {
      console.warn('June Analytics Write Key not found. Analytics disabled.');
      return;
    }
    if (!this.analytics) {
      // Initialize June SDK
      this.analytics = AnalyticsBrowser.load({ writeKey: JUNE_WRITE_KEY });
    }
  }

  identify(userId: string, traits: UserTraits) {
    if (!this.analytics) return;
    this.analytics.identify(userId, traits);
  }

  track(eventName: string, properties?: EventProperties) {
    if (!this.analytics) return;
    this.analytics.track(eventName, properties);
  }

  reset() {
    if (!this.analytics) return;
    this.analytics.reset();
  }
}
