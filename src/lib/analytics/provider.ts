interface UserTraits {
  email?: string;
  name?: string;
  createdAt?: Date | string;
  // Add other relevant user properties from your user model
  [key: string]: unknown;
}

interface EventProperties {
  [key: string]: unknown; // Allow arbitrary properties
}

export interface AnalyticsProvider {
  /** Initializes the analytics provider */
  init: () => void;

  /** Identifies the current user */
  identify: (userId: string, traits: UserTraits) => void;

  /** Tracks a specific event */
  track: (eventName: string, properties?: EventProperties) => void;

  /** Resets user identification (e.g., on sign out) */
  reset: () => void;
}

export type { UserTraits, EventProperties };