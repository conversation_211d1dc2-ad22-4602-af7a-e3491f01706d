// Variable parsing and resolution for prompt templates
// Regex pattern: {{variable_name|default_value}}
const VARIABLE_REGEX = /{{\s*([a-zA-Z0-9_]+)(?:\|([^}]*))?\s*}}/g;

// Reserved keywords that cannot be used as variable names
const RESERVED_KEYWORDS = ['system', 'assistant', 'user'];

export interface PromptVariable {
  name: string;
  defaultValue?: string;
  position: number;
}

export interface VariableResolutionContext {
  conversationTitle?: string;
  lastMessage?: string;
  workspaceContext?: Record<string, unknown | undefined>;
}

export interface ResolveVariablesResult {
  resolvedBody: string;
  missingVariables: PromptVariable[];
}

/**
 * Parses a prompt template to extract all variables
 * @param template The prompt template with {{variable}} syntax
 * @returns Array of variables found in the template
 */
export function parsePromptVariables(template: string): PromptVariable[] {
  const variables: PromptVariable[] = [];
  const seen = new Set<string>();
  let match;

  // Reset regex state
  VARIABLE_REGEX.lastIndex = 0;

  while ((match = VARIABLE_REGEX.exec(template)) !== null) {
    const [, variableName, defaultValue] = match;

    // Validate variable name
    if (!isValidVariableName(variableName)) {
      continue;
    }

    // Avoid duplicates
    if (seen.has(variableName)) {
      continue;
    }

    seen.add(variableName);
    variables.push({
      name: variableName,
      defaultValue: defaultValue || undefined,
      position: match.index,
    });
  }

  return variables;
}

/**
 * Validates a variable name against naming rules
 * @param name The variable name to validate
 * @returns true if valid, false otherwise
 */
export function isValidVariableName(name: string): boolean {
  // Check length (1-32 characters)
  if (name.length < 1 || name.length > 32) {
    return false;
  }

  // Check format (alphanumeric and underscore only)
  if (!/^[a-zA-Z0-9_]+$/.test(name)) {
    return false;
  }

  // Check reserved keywords
  if (RESERVED_KEYWORDS.includes(name.toLowerCase())) {
    return false;
  }

  return true;
}

/**
 * Resolves variables in a prompt template
 * @param template The prompt template
 * @param userValues User-provided variable values
 * @param context Additional context for automatic resolution
 * @returns Object with resolved template and any missing variables
 */
export function resolvePromptVariables(
  template: string,
  userValues: Record<string, string> = {},
  context: VariableResolutionContext = {}
): ResolveVariablesResult {
  const variables = parsePromptVariables(template);
  const missingVariables: PromptVariable[] = [];

  let resolvedBody = template;

  for (const variable of variables) {
    const { name, defaultValue } = variable;
    let value: string | undefined;

    // Resolution order:
    // 1. User-supplied values
    // 2. Conversation context
    // 3. Prompt-defined default values

    if (userValues[name] !== undefined) {
      value = userValues[name];
    } else if (context && getContextValue(name, context) !== undefined) {
      value = getContextValue(name, context);
    } else if (defaultValue !== undefined) {
      value = defaultValue;
    }

    if (value !== undefined) {
      // Replace all instances of this variable
      const variablePattern = new RegExp(
        `{{\\s*${escapeRegex(name)}(?:\\|[^}]*)?\\s*}}`,
        'g'
      );
      resolvedBody = resolvedBody.replace(variablePattern, value);
    } else {
      // Add to missing variables
      missingVariables.push(variable);
    }
  }

  return {
    resolvedBody,
    missingVariables,
  };
}

/**
 * Gets a value from the resolution context
 * @param variableName The variable name to look up
 * @param context The resolution context
 * @returns The value if found, undefined otherwise
 */
function getContextValue(
  variableName: string,
  context: VariableResolutionContext
): string | undefined {
  switch (variableName.toLowerCase()) {
    case 'conversation_title':
    case 'title':
      return context.conversationTitle;

    case 'last_message':
    case 'lastmessage':
      return context.lastMessage;

    case 'workspace':
      return context.workspaceContext?.name as string | undefined;

    default:
      // Check if it exists in workspace context
      return context.workspaceContext?.[variableName] as string | undefined;
  }
}

/**
 * Escapes special regex characters in a string
 * @param str The string to escape
 * @returns The escaped string
 */
function escapeRegex(str: string): string {
  return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

/**
 * Validates a complete prompt template
 * @param template The template to validate
 * @returns Validation result with any errors
 */
export function validatePromptTemplate(template: string): {
  isValid: boolean;
  errors: string[];
  variables: PromptVariable[];
} {
  const errors: string[] = [];

  if (!template || template.trim().length === 0) {
    errors.push('Template cannot be empty');
    return { isValid: false, errors, variables: [] };
  }

  const variables = parsePromptVariables(template);

  // Check for invalid variable names
  let match;
  VARIABLE_REGEX.lastIndex = 0;

  while ((match = VARIABLE_REGEX.exec(template)) !== null) {
    const [, variableName] = match;

    if (!isValidVariableName(variableName)) {
      if (variableName.length > 32) {
        errors.push(
          `Variable name "${variableName}" exceeds maximum length of 32 characters`
        );
      } else if (!/^[a-zA-Z0-9_]+$/.test(variableName)) {
        errors.push(
          `Variable name "${variableName}" contains invalid characters. Only letters, numbers, and underscores are allowed`
        );
      } else if (RESERVED_KEYWORDS.includes(variableName.toLowerCase())) {
        errors.push(`Variable name "${variableName}" is a reserved keyword`);
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    variables,
  };
}

/**
 * Generates a preview of how the template will look with current values
 * @param template The prompt template
 * @param userValues Current user-provided values
 * @param context Resolution context
 * @returns Preview text with placeholder indicators for missing variables
 */
export function generateTemplatePreview(
  template: string,
  userValues: Record<string, string> = {},
  context: VariableResolutionContext = {}
): string {
  const { resolvedBody, missingVariables } = resolvePromptVariables(
    template,
    userValues,
    context
  );

  // Replace any remaining unresolved variables with placeholder text
  let preview = resolvedBody;
  for (const variable of missingVariables) {
    const variablePattern = new RegExp(
      `{{\\s*${escapeRegex(variable.name)}(?:\\|[^}]*)?\\s*}}`,
      'g'
    );
    preview = preview.replace(variablePattern, `[${variable.name}]`);
  }

  return preview;
}
