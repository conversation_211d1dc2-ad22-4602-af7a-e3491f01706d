import { createClient } from '@/utils/supabase/client';
import { Session, AuthChangeEvent } from '@supabase/supabase-js';

export class AuthService {
  private static instance: AuthService;
  private client;

  private constructor() {
    this.client = createClient();
  }

  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  async signInWithGoogle(redirectTo?: string) {
    const searchParams = new URLSearchParams(window.location.search);
    const nextPath = searchParams.get('next');
    const baseUrl = redirectTo || `${window.location.origin}/api/auth/callback`;
    const redirectUrl = nextPath
      ? `${baseUrl}?next=${encodeURIComponent(nextPath)}`
      : baseUrl;

    return this.client.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: redirectUrl,
        // Add these options to handle the fragment
        queryParams: {
          display: 'popup',
        },
      },
    });
  }

  async signInWithOtp({
    email,
    options,
  }: {
    email: string;
    options?: { emailRedirectTo?: string };
  }) {
    return this.client.auth.signInWithOtp({
      email,
      options: {
        emailRedirectTo:
          options?.emailRedirectTo ||
          `${window.location.origin}/api/auth/callback`,
        shouldCreateUser: false,
      },
    });
  }

  async verifyOtp({
    email,
    token,
  }: {
    email: string;
    token: string;
    type?: 'signup' | 'recovery' | 'invite' | 'magiclink' | 'email_change';
  }) {
    return this.client.auth.verifyOtp({
      email,
      token,
      type: 'email',
    });
  }

  async setSession(session: Session) {
    return this.client.auth.setSession(session);
  }

  async signOut() {
    return this.client.auth.signOut();
  }

  async getUser() {
    return this.client.auth.getUser();
  }

  async getSession() {
    return this.client.auth.getSession();
  }

  onAuthStateChange(
    callback: (event: AuthChangeEvent, session: Session | null) => void
  ) {
    return this.client.auth.onAuthStateChange(callback);
  }

  async updateUserMetadata({
    firstName,
    lastName,
  }: {
    firstName: string;
    lastName: string;
  }) {
    return this.client.auth.updateUser({
      data: {
        first_name: firstName,
        last_name: lastName,
      },
    });
  }
}

// Export a singleton instance
export const authService = AuthService.getInstance();
