import { Database, Tables } from '@/types/database.types';
import { SupabaseClient } from '@supabase/supabase-js';

export type DbClient = SupabaseClient<Database>;

export type AttachmentPayload = {
  name: string;
  type: string;
  url: string;
  image_url?: {
    url: string;
    detail: 'auto' | 'low' | 'high';
  };
};

type BaseMessageRow = Tables<'messages'>;

export interface AssistantMessageCustomMetadata
  extends Omit<StreamMetadataClientData, 'usage'> {
  usage?: StreamReceivedUsage;
}

export const ASSISTANT_MESSAGE_METADATA_VERSION = 1;

export interface VersionedAssistantMessageMetadata {
  version: typeof ASSISTANT_MESSAGE_METADATA_VERSION;
  data: AssistantMessageCustomMetadata;
}

export type Message = Omit<
  BaseMessageRow,
  'attachments' | 'annotations' | 'metadata' | 'file_annotations'
> & {
  attachments: AttachmentPayload[] | null;
  annotations: UrlCitationAnnotation[] | null;
  file_annotations?: unknown[] | null;
  metadata?: VersionedAssistantMessageMetadata | Record<string, unknown> | null;
};

export type MessageWithModel = Message & {
  modelData: LLMModel | null;
};

export type MessageNode = MessageWithModel & {
  children: MessageNode[];
  metadata?: VersionedAssistantMessageMetadata | Record<string, unknown> | null;
  annotations?: UrlCitationAnnotation[];
};

export type MessageWithConversation = Message & {
  conversation: Conversation | null;
};

// export Conversation from '@/types/database.types', dont redefine, just export the Database public type
export type Conversation = Tables<'conversations'>;

export type ConversationWithParentMessageNode = Tables<'conversations'> & {
  parentMessageNode: MessageNode | null;
};

export type ConversationWithMessages = Conversation & {
  messages: MessageWithModel[];
};

export type GroupConversation = Tables<'group_conversations'>;

export type UsageLog = Tables<'usage_logs'>;

export type Provider = Tables<'llm_providers'> & {
  models: Model[];
};

export type BaseModel = Tables<'llm_models'>;

export type Model = Omit<
  BaseModel,
  | 'provider_specific_data'
  | 'capabilities'
  | 'supported_parameters'
  | 'architecture'
  | 'pricing'
> & {
  provider?: Tables<'llm_providers'> | null;
  capabilities?: ModelCapabilities | null;
  supported_parameters?: string[] | null;
  provider_specific_data?: ProviderSpecificData | null;
  architecture?: ModelArchitecture | null;
  pricing?: ModelPricing | null;
};

export type ConversationState =
  Database['public']['Enums']['conversation_state'];

export type SubscriptionPlan = Database['public']['Enums']['subscription_plan'];

export type SubscriptionStatus =
  Database['public']['Enums']['subscription_status'];
export type SubscriptionData = Tables<'subscriptions'>;

export type ModelTier = 'free' | 'starter' | 'premium';

export type LLMModel = Tables<'llm_models'>;

export type DailyUsage = Tables<'user_daily_usage'>;
export const PLAN_MODEL_ACCESS: Record<SubscriptionPlan, ModelTier[]> = {
  free: ['free'],
  starter: ['free', 'starter'],
  premium: ['free', 'starter', 'premium'],
};

export interface SubscriptionDetails {
  name: string;
  price: string;
  maxDailyMessages: number;
  tokenQuota: number | null;
  imageCreditsMonthly: number;
  features: string[];
  modelTiers: ModelTier[];
  allowsComparison: boolean;
  allowsWebSearch: boolean;
  allowsPromptLibrary: boolean;
  maxWorkspaces: number;
  priorityThroughput: number;
}

type SubscriptionPlanFeatures = {
  [key in SubscriptionPlan]: SubscriptionDetails;
};

export const SUBSCRIPTION_PLANS: SubscriptionPlanFeatures = {
  free: {
    name: 'Free Plan',
    price: 'Free',
    maxDailyMessages: 20,
    tokenQuota: null,
    imageCreditsMonthly: 0,
    features: [
      '20 messages/day',
      'GPT-3.5, Claude Haiku (rate-limited)',
      'Shareable conversations',
      '1 workspace',
    ],
    modelTiers: ['free'],
    allowsComparison: false,
    allowsWebSearch: false,
    allowsPromptLibrary: false,
    maxWorkspaces: 1,
    priorityThroughput: 1,
  },
  starter: {
    name: 'Starter Plan',
    price: '$12',
    maxDailyMessages: Infinity,
    tokenQuota: 150000,
    imageCreditsMonthly: 200,
    features: [
      '150K tokens/month',
      'GPT-4o-mini, Gemini Flash, DeepSeek',
      'Unlimited workspaces',
      'Prompt library',
      'Web search',
    ],
    modelTiers: ['free', 'starter'],
    allowsComparison: false,
    allowsWebSearch: true,
    allowsPromptLibrary: true,
    maxWorkspaces: -1, // -1 means unlimited
    priorityThroughput: 1,
  },
  premium: {
    name: 'Premium Plan',
    price: '$24',
    maxDailyMessages: Infinity,
    tokenQuota: 400000,
    imageCreditsMonthly: 1000,
    features: [
      '400K tokens/month',
      'All models',
      'Unlimited workspaces',
      'Prompt library',
      'Side-by-side compare (50/day)',
      'Web search',
      '2x priority throughput',
    ],
    modelTiers: ['free', 'starter', 'premium'],
    allowsComparison: true,
    allowsWebSearch: true,
    allowsPromptLibrary: true,
    maxWorkspaces: -1, // -1 means unlimited
    priorityThroughput: 2,
  },
};

export type UserPreferences = Tables<'user_preferences'>;

export type Me = {
  preferences: UserPreferences;
};

export interface AttachmentMetadata {
  name: string;
  type: string;
  url: string;
}

// Define annotation type (based on OpenAI Responses API)
export interface UrlCitationAnnotation {
  type: 'url_citation';
  url: string;
  title: string;
  start_index: number;
  end_index: number;
  // Add other fields if necessary based on actual backend structure
}

export type UploadedFile = Tables<'uploaded_files'>;

export interface StreamReceivedUsage {
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
  prompt_tokens_details?: { cached_tokens: number };
  completion_tokens_details?: { reasoning_tokens: number };
}

export interface StreamMetadataClientData {
  provider?: string;
  model?: string;
  id?: string;
  object_type?: string;
  created_timestamp?: number;
  usage?: StreamReceivedUsage;
}

// Workspace related types
export type Workspace = Tables<'workspaces'>;
export type WorkspaceFile = Tables<'workspace_files'>;
export type WorkspaceNote = Tables<'workspace_notes'>;

export type JobStatus = Database['public']['Enums']['job_status'];

// Enhanced model types
export interface ModelCapabilities {
  file_upload: boolean;
  web_search: boolean;
  visible_by_default: boolean;
  image_generation?: boolean;
  code_generation?: boolean;
  function_calling?: boolean;
  reasoning?: boolean;
  structured_output?: boolean;
}

export interface ModelArchitecture {
  modality?: string;
  input_modalities: string[];
  output_modalities: string[];
  tokenizer: string;
  instruct_type?: string | null;
}

export interface ModelPricing {
  prompt: string;
  completion: string;
  image?: string;
  request?: string;
  web_search?: string;
  internal_reasoning?: string;
  input_cache_read?: string;
  input_cache_write?: string;
}

export interface ProviderSpecificData {
  context_length?: number;
  max_completion_tokens?: number;
  is_moderated?: boolean;
  per_request_limits?: Record<string, unknown> | null;
  [key: string]: unknown;
}
