import { <PERSON>, Json } from '@/types/database.types';
import { SupabaseClient } from '@supabase/supabase-js';
import {
  AttachmentMetadata,
  AttachmentPayload,
  Conversation,
  ConversationState,
  ConversationWithMessages,
  GroupConversation,
  JobStatus,
  LLMModel,
  Message,
  MessageNode,
  MessageWithConversation,
  MessageWithModel,
  Model,
  UploadedFile,
  UrlCitationAnnotation,
  UsageLog,
  UserPreferences,
  Workspace,
  WorkspaceFile,
  WorkspaceNote,
} from './types';
import { logger } from '@/lib/logger';
import { AppError, AuthError, ErrorCode } from '../error';
import { DUMMY_ROOT_MESSAGE_ID } from '@/constants/chat';
const log = logger.child({
  module: 'DatabaseService',
});

// Re-export types from './types' for other modules to use
export type {
  Conversation,
  ConversationState,
  GroupConversation,
  Message,
  MessageNode,
  Model,
  Provider,
  UsageLog,
} from './types';
// lib/supabase/types.ts

export type DbClient = SupabaseClient<Database>;

function isPostgresErrorLike(error: unknown): error is {
  message: string;
  code?: string;
  details?: string;
  hint?: string;
} {
  return (
    typeof error === 'object' &&
    error !== null &&
    'message' in error &&
    typeof (error as { message: string }).message === 'string'
  );
}

const handleSupabaseError = (error: unknown, context: string): never => {
  let errorMessage = 'An unknown database error occurred';
  let originalError: Error | undefined;

  if (isPostgresErrorLike(error)) {
    // Prioritize the message from PostgrestError-like objects
    errorMessage = `Database error in ${context}: ${error.message}`;
    // Attempt to wrap it in an Error if it's not already one, preserving details
    originalError = new Error(
      `${error.message} (Code: ${error.code}, Details: ${error.details}, Hint: ${error.hint})`
    );
    // You could potentially attach the original object properties to this new Error if needed downstream
    // e.g., Object.assign(originalError, error);
  } else if (error instanceof Error) {
    // Handle generic Error objects
    errorMessage = `Database error in ${context}: ${error.message}`;
    originalError = error; // Keep the original Error object
  } else {
    // Handle non-Error types
    errorMessage = `Database error in ${context}: ${String(error)}`;
    originalError = new Error(String(error)); // Wrap in a basic Error
  }

  // Throw AppError, which should handle the logging downstream
  throw new AppError(
    errorMessage, // Pass the extracted or generic message
    ErrorCode.DATABASE_ERROR,
    500,
    originalError // Pass the original/wrapped error as the cause
  );
};

export class DatabaseService {
  private static instance: DatabaseService;
  private client: DbClient;

  private constructor(client: SupabaseClient<Database>) {
    this.client = client;
  }

  public static getInstance(client: SupabaseClient<Database>): DatabaseService {
    // Always create a new instance with the provided client
    // This ensures we're using the latest session
    DatabaseService.instance = new DatabaseService(client);
    return DatabaseService.instance;
  }

  // User methods
  async getCurrentUser() {
    try {
      const { data, error } = await this.client.auth.getUser();
      if (error) {
        throw new AuthError(
          'Unauthorized',
          ErrorCode.AUTH_TOKEN_EXPIRED,
          error
        );
      }
      return data.user;
    } catch (error) {
      throw new AuthError(
        'Unauthorized',
        ErrorCode.AUTH_TOKEN_EXPIRED,
        error as Error
      );
    }
  }

  // Conversations
  async createConversation(
    userId: string,
    options: Partial<Conversation>
  ): Promise<Conversation> {
    const { data, error } = await this.client
      .from('conversations')
      .insert({
        user_id: userId,
        title: options.title || 'New Conversation',
        // system_prompt: options.system_prompt || '',
        last_model_used: options.last_model_used || undefined,
        group_conversation_id: options.group_conversation_id || '',
      })
      .select()
      .single();

    if (error)
      throw new AppError(
        'Failed to create conversation',
        ErrorCode.DATABASE_ERROR,
        500,
        error
      );
    return data;
  }

  async upsertGroupConversation(
    userId: string,
    options: Partial<GroupConversation>
  ): Promise<GroupConversation> {
    const { data, error } = await this.client
      .from('group_conversations')
      .upsert(
        {
          id: options.id || crypto.randomUUID(),
          user_id: userId,
          title: options.title || 'New Conversation',
          is_active: true,
          is_comparison: options.is_comparison || false,
          is_temporary: options.is_temporary || false,
          workspace_id: options.workspace_id ?? undefined,
        },
        {
          onConflict: 'id',
        }
      )
      .select()
      .single();

    if (error)
      throw new AppError(
        'Failed to upsert group conversation',
        ErrorCode.DATABASE_ERROR,
        500,
        error
      );
    return data;
  }

  async createAnonymousConversation(
    anonymousSessionId: string,
    options: Partial<Conversation>
  ): Promise<Conversation> {
    const { data, error } = await this.client
      .from('conversations')
      .insert({
        anonymous_session_id: anonymousSessionId,
        title: options.title || 'New Conversation',
        // system_prompt: options.system_prompt || '',
        last_model_used: options.last_model_used || '',
        group_conversation_id: options.group_conversation_id || '',
        comparison_index: options.comparison_index || 0,
        is_active: true,
      })
      .select()
      .single();
    if (error)
      throw new AppError(
        'Failed to create conversation',
        ErrorCode.DATABASE_ERROR,
        500,
        error
      );
    return data;
  }

  async getConversation(conversationId: string): Promise<Conversation | null> {
    const { data, error } = await this.client
      .from('conversations')
      .select('*')
      .eq('id', conversationId)
      .single();

    if (error) {
      log.error(error);
      return null;
    }
    return data as Conversation;
  }

  async getGroupConversation(
    groupConversationId: string
  ): Promise<GroupConversation | null> {
    const { data, error } = await this.client
      .from('group_conversations')
      .select('*')
      .eq('id', groupConversationId)
      .single();

    if (error) {
      // If the error is a not found error, return null
      if (error.code === 'PGRST116') {
        return null;
      }
      // Otherwise, handle the error
      handleSupabaseError(error, 'getGroupConversation');
    }

    return data;
  }

  async getUserGroupConversations(
    limit: number = 30,
    cursor?: string,
    workspaceId?: string
  ): Promise<{ data: GroupConversation[]; nextCursor: string | null }> {
    let query = this.client
      .from('group_conversations')
      .select('*')
      .eq('is_active', true)
      .eq('is_temporary', false) // Only get non-temporary conversations
      .order('is_favorite', { ascending: false })
      .order('created_at', { ascending: false })
      .limit(limit);

    if (workspaceId) {
      query = query.eq('workspace_id', workspaceId);
    }

    // Apply cursor-based filtering if cursor is provided
    if (cursor) {
      try {
        // Parse the cursor format: timestamp_id
        const [timestampStr, id] = cursor.split('_');

        if (timestampStr && id) {
          // First get conversations with created_at less than the cursor timestamp
          const lessQuery = query.filter('created_at', 'lt', timestampStr);

          // Then get conversations with created_at equal to cursor timestamp but with id less than cursor id
          const equalQuery = this.client
            .from('group_conversations')
            .select('*')
            .eq('is_active', true)
            .eq('created_at', timestampStr)
            .lt('id', id)
            .order('is_favorite', { ascending: false })
            .order('created_at', { ascending: false })
            .limit(limit);

          // Execute both queries separately and combine results
          const [lessResult, equalResult] = await Promise.all([
            lessQuery,
            equalQuery,
          ]);

          // Handle errors
          if (lessResult.error) throw lessResult.error;
          if (equalResult.error) throw equalResult.error;

          // Combine and sort the results
          const combinedData = [
            ...(lessResult.data || []),
            ...(equalResult.data || []),
          ]
            .sort((a, b) => {
              // Sort by created_at (desc)
              const dateComparison =
                new Date(b.created_at || 0).getTime() -
                new Date(a.created_at || 0).getTime();

              // If same date, sort by id (desc)
              if (dateComparison === 0 && a.id && b.id) {
                return b.id.localeCompare(a.id);
              }

              return dateComparison;
            })
            .slice(0, limit); // Ensure we only return 'limit' items

          // Generate the next cursor
          let nextCursor: string | null = null;
          if (combinedData.length === limit) {
            const lastItem = combinedData[combinedData.length - 1];
            if (lastItem.created_at && lastItem.id) {
              nextCursor = this.formatCursorValue(
                lastItem.created_at,
                lastItem.id
              );
            }
          }

          return { data: combinedData, nextCursor };
        } else if (timestampStr) {
          // Simple case: just filter by timestamp
          query = query.lt('created_at', timestampStr);
        }
      } catch (err) {
        log.error(
          { err, cursor },
          'Error parsing cursor in getUserGroupConversations'
        );
        // Continue without cursor if parsing fails
      }
    }

    // If we reach here, either there's no cursor or we're using a simple timestamp filter
    const { data, error } = await query;

    if (error)
      throw new AppError(
        'Failed to get user group conversations',
        ErrorCode.DATABASE_ERROR,
        500,
        error
      );

    let nextCursor: string | null = null;

    // Generate the next cursor from the last item
    if (data && data.length === limit) {
      const lastItem = data[data.length - 1];
      if (lastItem.created_at && lastItem.id) {
        nextCursor = this.formatCursorValue(lastItem.created_at, lastItem.id);
      }
    }

    return { data: data || [], nextCursor };
  }

  // Helper method to correctly format cursor values
  private formatCursorValue(timestamp: string, id: string): string {
    try {
      // Ensure the timestamp is in the correct ISO format (remove any spaces after the timestamp)
      const date = new Date(timestamp);
      const formattedTimestamp = date.toISOString();
      return `${formattedTimestamp}_${id}`;
    } catch (err) {
      log.error({ err, timestamp }, 'Error formatting timestamp for cursor');
      // Fallback to original value if there's an error
      return `${timestamp}_${id}`;
    }
  }

  async updateConversation(
    id: string,
    updates: Partial<Conversation>
  ): Promise<Conversation> {
    const { data, error } = await this.client
      .from('conversations')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();

    if (error) handleSupabaseError(error, 'updateConversation');
    return data as Conversation;
  }

  async deleteConversation(id: string): Promise<void> {
    const { error } = await this.client
      .from('conversations')
      .update({ is_active: false })
      .eq('id', id);

    if (error) handleSupabaseError(error, 'deleteConversation');
  }

  async deleteGroupConversation(id: string): Promise<void> {
    const { error } = await this.client
      .from('group_conversations')
      .update({ is_active: false })
      .eq('id', id);

    if (error) handleSupabaseError(error, 'deleteGroupConversation');
  }

  async deleteGroupConversations(
    conversationIds: string[]
  ): Promise<GroupConversation[] | null> {
    const result = await this.client
      .from('group_conversations')
      .update({ is_active: false })
      .in('id', conversationIds)
      .select();

    if (result.error)
      handleSupabaseError(result.error, 'deleteGroupConversations');
    return result.data;
  }

  async deleteAllConversations(): Promise<void> {
    const { error } = await this.client
      .from('group_conversations')
      .update({ is_active: false });

    if (error) handleSupabaseError(error, 'deleteAllConversations');
  }

  async archiveConversation(conversationId: string): Promise<void> {
    const { error } = await this.client
      .from('conversations')
      .update({ is_active: false })
      .eq('id', conversationId);

    if (error) handleSupabaseError(error, 'archiveConversation');
  }

  async archiveGroupConversation(conversationId: string): Promise<void> {
    const { error } = await this.client
      .from('group_conversations')
      .update({ is_active: false })
      .eq('id', conversationId);

    if (error) handleSupabaseError(error, 'archiveGroupConversation');
  }

  async archiveGroupConversations(
    conversationIds: string[]
  ): Promise<GroupConversation[] | null> {
    const result = await this.client
      .from('group_conversations')
      .update({ is_active: false })
      .in('id', conversationIds)
      .select();

    if (result.error)
      handleSupabaseError(result.error, 'archiveGroupConversations');

    return result.data;
  }

  async restoreGroupConversation(conversationId: string): Promise<void> {
    const { error } = await this.client
      .from('group_conversations')
      .update({ is_active: true })
      .eq('id', conversationId);

    if (error) handleSupabaseError(error, 'restoreGroupConversation');
  }

  async getArchivedGroupConversations(): Promise<GroupConversation[]> {
    const { data, error } = await this.client
      .from('group_conversations')
      .select('*')
      .order('updated_at', { ascending: false })
      .eq('is_active', false);

    if (error) handleSupabaseError(error, 'getArchivedGroupConversations');
    return data || [];
  }

  async restoreConversation(conversationId: string): Promise<void> {
    const { error } = await this.client
      .from('group_conversations')
      .update({ is_active: true })
      .eq('id', conversationId);

    if (error) handleSupabaseError(error, 'restoreConversation');
  }

  async updateGroupConversation(
    groupConversationId: string,
    updates: Partial<GroupConversation>
  ): Promise<void> {
    const { error } = await this.client
      .from('group_conversations')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', groupConversationId);

    if (error) handleSupabaseError(error, 'updateGroupConversation');
  }

  async toggleGroupConversationFavorite(
    groupConversationId: string
  ): Promise<GroupConversation> {
    // First get the current favorite status
    const { data: conversation, error: getError } = await this.client
      .from('group_conversations')
      .select('is_favorite')
      .eq('id', groupConversationId)
      .single();

    if (getError)
      handleSupabaseError(getError, 'toggleGroupConversationFavorite');

    // Toggle the favorite status
    const newFavoriteStatus = !(conversation?.is_favorite || false);
    const { data, error } = await this.client
      .from('group_conversations')
      .update({
        is_favorite: newFavoriteStatus,
        updated_at: new Date().toISOString(),
      })
      .eq('id', groupConversationId)
      .select()
      .single();

    if (error) handleSupabaseError(error, 'toggleGroupConversationFavorite');
    return data as GroupConversation;
  }

  async updateGroupConversationState(
    groupConversationId: string,
    state: ConversationState
  ): Promise<Conversation> {
    const { data, error } = await this.client
      .from('conversations')
      .update({ state })
      .eq('group_conversation_id', groupConversationId)
      .select()
      .single();

    if (error) {
      handleSupabaseError(error, 'updateGroupConversationState');
    }
    return data as Conversation;
  }

  // Messages
  async createMessage(
    {
      id,
      conversationId,
      content,
      role,
      parentMessageId,
      modelId,
      providerId,
      attachments,
      annotations,
      file_annotations,
    }: {
      id?: string;
      conversationId: string;
      content: string;
      role: string;
      parentMessageId?: string;
      modelId?: string;
      providerId?: string;
      attachments?: AttachmentMetadata[] | null;
      annotations?: UrlCitationAnnotation[] | null;
      file_annotations?: unknown[] | null;
    },
    metadata: Record<string, string | number | boolean | undefined> = {}
  ): Promise<Message> {
    const messageId = id || crypto.randomUUID();
    const { data, error } = await this.client
      .from('messages')
      .insert({
        id: messageId,
        conversation_id: conversationId,
        content,
        role,
        parent_message_id: parentMessageId,
        model_id: modelId,
        provider_id: providerId,
        attachments: attachments as Json,
        annotations: annotations as Json,
        file_annotations: file_annotations as Json,
        metadata: metadata as Json,
      })
      .select()
      .single();

    if (error)
      throw new AppError(
        `Failed to create message: ${error.message}`,
        ErrorCode.DATABASE_ERROR,
        500,
        error
      );
    return data as Message;
  }

  async getConversationMessages(conversationId: string): Promise<Message[]> {
    const { data, error } = await this.client
      .from('messages')
      .select('*')
      .eq('conversation_id', conversationId)
      .order('created_at', { ascending: true });

    if (error) handleSupabaseError(error, 'getConversationMessages');
    return data as Message[];
  }

  public buildMessageTree(messages: MessageWithModel[]): MessageNode | null {
    if (!messages || messages.length === 0) {
      return null;
    }

    const messageMap: Record<string, MessageNode> = {};
    const actualRoots: MessageNode[] = [];

    // Populate the map
    messages.forEach((msg) => {
      // Use forceful cast due to complex inherited type conflicts despite type fixes
      messageMap[msg.id] = { ...msg, children: [] } as unknown as MessageNode;
    });

    // Link children to parents and find actual roots
    messages.forEach((msg) => {
      if (msg.parent_message_id) {
        const parent = messageMap[msg.parent_message_id];
        if (parent) {
          // Ensure children are added in chronological order if needed,
          // although sorting roots later handles the primary case.
          parent.children.push(messageMap[msg.id]);
          // Optional: Sort children here if strict order within branches is needed
          // parent.children.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
        }
      } else {
        actualRoots.push(messageMap[msg.id]);
      }
    });

    // Sort actual roots by creation time, handling potential nulls
    actualRoots.sort((a, b) => {
      const timeA = a.created_at ? new Date(a.created_at).getTime() : 0;
      const timeB = b.created_at ? new Date(b.created_at).getTime() : 0;
      return timeA - timeB;
    });

    if (actualRoots.length === 0) {
      // Should not happen if messages array is not empty and messages have IDs,
      // but handle defensively. Could indicate a cycle or orphaned messages.
      // Returning the first message as a fallback, though ideally this case is logged/investigated.
      return messages.length > 0 ? messageMap[messages[0].id] : null;
    } else if (actualRoots.length === 1) {
      // Standard case: only one root
      return actualRoots[0];
    } else {
      // Multiple roots found: create and return a dummy root
      const firstRoot = actualRoots[0];
      const dummyRoot = {
        id: DUMMY_ROOT_MESSAGE_ID,
        conversation_id: firstRoot.conversation_id,
        parent_message_id: null,
        role: 'system',
        content: '',

        // Properties from MessageNode
        children: actualRoots,
      } as MessageNode;
      return dummyRoot;
    }
  }

  async getMessage(messageId: string): Promise<MessageWithConversation | null> {
    const { data, error } = await this.client
      .from('messages')
      .select('*, conversations(*)')
      .eq('id', messageId)
      .single();

    if (error) handleSupabaseError(error, 'getMessage');

    // Cast data to the expected type after validation
    return {
      ...data,
      conversation: data?.conversations as Conversation,
    } as MessageWithConversation;
  }

  async getGroupConversationMessages(
    groupConversationId: string
  ): Promise<ConversationWithMessages[]> {
    // get conversations and all their messages given a group conversation id
    const { data, error } = await this.client
      .from('conversations')
      .select(
        `
        *,
        messages (
          *,
          model:llm_models(id, name, display_name)
        )
      `
      )
      .eq('group_conversation_id', groupConversationId)
      .eq('is_active', true);

    if (error) handleSupabaseError(error, 'getGroupConversationMessages');

    // sort messages by created_at and map model display name
    const conversationsWithMessages: ConversationWithMessages[] =
      data?.map((conversation) => {
        const messages = (conversation.messages || [])
          .sort((a, b) => {
            if (!a.created_at || !b.created_at) return 0;
            return (
              new Date(a.created_at).getTime() -
              new Date(b.created_at).getTime()
            );
          })
          // Map the model display name to modelName
          .map((msg): MessageWithModel => {
            // Destructure model and metadata to handle metadata type mismatch
            const { model, metadata, ...rest } = msg;

            // Validate metadata from DB (Json type) to fit Message type
            const validatedMetadata =
              typeof metadata === 'object' &&
              metadata !== null &&
              !Array.isArray(metadata)
                ? (metadata as Record<string, unknown>)
                : undefined;

            return {
              ...rest,
              modelData: model as LLMModel,
              attachments: (rest.attachments as AttachmentPayload[]) || null,
              annotations: rest.annotations as UrlCitationAnnotation[] | null,
              file_annotations: rest.file_annotations as unknown[] | null,
              metadata: validatedMetadata, // Use validated metadata
            };
          });

        return {
          ...conversation,
          messages,
        };
      }) ?? [];

    return conversationsWithMessages;
  }

  async updateMessageTokensUsed(
    messageId: string,
    tokensUsed: number
  ): Promise<void> {
    const { error } = await this.client
      .from('messages')
      .update({ tokens_used: tokensUsed })
      .eq('id', messageId);

    if (error) handleSupabaseError(error, 'updateMessageTokensUsed');
  }

  async updateMessage(
    messageId: string,
    updates: Partial<Message>
  ): Promise<void> {
    // Create a type-safe update object
    const updateObj: Record<string, unknown> = {
      ...updates,
      updated_at: new Date().toISOString(),
    };

    // Cast specific fields if they exist in updates
    if ('annotations' in updates) {
      updateObj.annotations = updates.annotations as Json;
    }
    if ('attachments' in updates) {
      updateObj.attachments = updates.attachments as Json;
    }
    if ('file_annotations' in updates) {
      updateObj.file_annotations = updates.file_annotations as Json;
    }
    if ('metadata' in updates) {
      updateObj.metadata = updates.metadata as Json;
    }

    const { error } = await this.client
      .from('messages')
      .update(updateObj)
      .eq('id', messageId);

    if (error) handleSupabaseError(error, 'updateMessage');
  }

  // Usage logs
  async createUsageLog(log: UsageLog): Promise<UsageLog> {
    const { data, error } = await this.client
      .from('usage_logs')
      .insert(log)
      .select()
      .single();

    if (error) handleSupabaseError(error, 'createUsageLog');

    return data as UsageLog;
  }

  async getUserUsage(
    userId: string,
    startDate?: string,
    endDate?: string
  ): Promise<UsageLog[]> {
    let query = this.client
      .from('usage_logs')
      .select('*')
      .eq('user_id', userId);

    if (startDate) {
      query = query.gte('created_at', startDate);
    }

    if (endDate) {
      query = query.lte('created_at', endDate);
    }

    const { data, error } = await query.order('created_at', {
      ascending: false,
    });

    if (error) handleSupabaseError(error, 'getUserUsage');

    return data || [];
  }

  // LLM Providers and Models
  async getActiveProviders() {
    const { data, error } = await this.client
      .from('llm_providers')
      .select('*')
      .eq('is_active', true);

    if (error) handleSupabaseError(error, 'getActiveProviders');

    return data || [];
  }

  async getActiveModels(providerId?: string) {
    let query = this.client
      .from('llm_models')
      .select('*')
      .eq('is_active', true);

    if (providerId) {
      query = query.eq('provider_id', providerId);
    }

    const { data, error } = await query;

    if (error) handleSupabaseError(error, 'getActiveModels');

    return data || [];
  }

  async getProvidersAndModels() {
    const { data, error } = await this.client
      .from('llm_providers')
      .select(
        'id, name, display_name, models:llm_models(*, provider:llm_providers(*))'
      )
      .order('display_name', { ascending: false })
      .eq('models.is_active', true);

    if (error) handleSupabaseError(error, 'getProvidersAndModels');

    // Transform the data to ensure capabilities are properly formatted
    if (data) {
      data.forEach((provider) => {
        if (provider.models) {
          provider.models = provider.models.map((model) => {
            // Handle legacy models without capabilities
            if (!model.capabilities) {
              model.capabilities = {
                file_upload: model.allows_file_upload || false,
                web_search: model.allows_search || false,
                visible_by_default: model.is_visible_by_default || true,
                // Set defaults for new capabilities
                image_generation: false,
                code_generation: true, // Most models can generate code
                function_calling: false, // Will be set based on provider data if available
                reasoning: false,
                structured_output: false,
              };
            }

            // Ensure supported_parameters is an array
            if (
              !model.supported_parameters ||
              !Array.isArray(model.supported_parameters)
            ) {
              model.supported_parameters = [
                'temperature',
                'max_tokens',
                'stop',
              ];
            }

            return model;
          });
        }
      });
    }

    return data || [];
  }

  async getModel(modelId: string): Promise<Model | null> {
    const { data, error } = await this.client
      .from('llm_models')
      .select(
        `
        *,
        provider:llm_providers(*)
      `
      )
      .eq('id', modelId)
      .single();

    if (error) handleSupabaseError(error, 'getModel');

    // Handle legacy models without capabilities
    if (data && !data.capabilities) {
      data.capabilities = {
        file_upload: data.allows_file_upload || false,
        web_search: data.allows_search || false,
        visible_by_default: data.is_visible_by_default || true,
        // Set defaults for new capabilities
        image_generation: false,
        code_generation: true,
        function_calling: false,
        reasoning: false,
        structured_output: false,
      };
    }

    // Ensure supported_parameters is an array
    if (
      data &&
      (!data.supported_parameters || !Array.isArray(data.supported_parameters))
    ) {
      data.supported_parameters = ['temperature', 'max_tokens', 'stop'];
    }

    return data as Model;
  }

  async getUserPreferences(userId: string): Promise<UserPreferences | null> {
    const { data, error } = await this.client
      .from('user_preferences')
      .select('*')
      .eq('user_id', userId)
      .maybeSingle();

    if (error) handleSupabaseError(error, 'getUserPreferences');

    return data;
  }

  async updateUserPreferences(
    userId: string,
    preferences: Partial<UserPreferences>
  ): Promise<UserPreferences | null> {
    const { data, error } = await this.client
      .from('user_preferences')
      .upsert(preferences, {
        onConflict: 'user_id',
      })
      .select()
      .single();

    if (error) handleSupabaseError(error, 'updateUserPreferences');

    return data;
  }

  // Update this to filter out temporary chats
  async searchGroupConversations(
    query: string,
    limit: number = 30,
    offset: number = 0
  ): Promise<GroupConversation[]> {
    const { data, error } = await this.client
      .from('group_conversations')
      .select('*')
      .eq('is_active', true)
      .eq('is_temporary', false) // Only search in non-temporary conversations
      .ilike('title', `%${query}%`)
      .order('updated_at', { ascending: false })
      .limit(limit)
      .range(offset, offset + limit - 1);

    if (error) handleSupabaseError(error, 'searchGroupConversations');

    return data || [];
  }

  // Uploaded Files
  async getUploadedFile(fileHash: string): Promise<UploadedFile | null> {
    const { data, error } = await this.client
      .from('uploaded_files')
      .select('*')
      .eq('file_hash', fileHash)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // File not found is an expected condition, not an error
        return null;
      }
      handleSupabaseError(error, 'getUploadedFile');
    }

    return data;
  }

  // Workspace methods
  async createWorkspace(
    userId: string,
    data: {
      name: string;
      description?: string;
      icon?: string;
      default_model_id?: string;
      status?: JobStatus;
    }
  ): Promise<Workspace> {
    const { data: workspace, error } = await this.client
      .from('workspaces')
      .insert({
        owner_id: userId,
        name: data.name,
        description: data.description || null,
        icon: data.icon || null,
        default_model_id: data.default_model_id || null,
        structured: {},
        status: data.status || 'completed',
      })
      .select()
      .single();

    if (error) {
      handleSupabaseError(error, 'createWorkspace');
    }

    // Add null check here
    if (!workspace) {
      throw new AppError(
        'Workspace creation resulted in null data unexpectedly.',
        ErrorCode.DATABASE_ERROR,
        500
      );
    }

    return workspace;
  }

  async getWorkspace(workspaceId: string): Promise<Workspace | null> {
    const { data, error } = await this.client
      .from('workspaces')
      .select('*')
      .eq('id', workspaceId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null;
      }
      handleSupabaseError(error, 'getWorkspace');
    }

    return data;
  }

  async getUserWorkspaces(userId: string): Promise<Workspace[]> {
    const { data, error } = await this.client
      .from('workspaces')
      .select('*')
      .eq('owner_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      handleSupabaseError(error, 'getUserWorkspaces');
    }

    return data || [];
  }

  async updateWorkspace(
    workspaceId: string,
    updates: Partial<Workspace>
  ): Promise<Workspace> {
    const { data, error } = await this.client
      .from('workspaces')
      .update(updates)
      .eq('id', workspaceId)
      .select()
      .single();

    if (error) {
      handleSupabaseError(error, 'updateWorkspace');
    }

    return data as Workspace;
  }

  async deleteWorkspace(workspaceId: string): Promise<void> {
    const { error } = await this.client
      .from('workspaces')
      .delete()
      .eq('id', workspaceId);

    if (error) {
      handleSupabaseError(error, 'deleteWorkspace');
    }
  }

  // Workspace files
  async createWorkspaceFile(
    workspaceId: string,
    data: {
      filename: string;
      mime_type: string;
      byte_size: number;
      text_content?: string;
      file_path?: string;
    }
  ): Promise<WorkspaceFile> {
    const { data: file, error } = await this.client
      .from('workspace_files')
      .insert({
        workspace_id: workspaceId,
        filename: data.filename,
        mime_type: data.mime_type,
        byte_size: data.byte_size,
        text_content: data.text_content || null,
        status: 'pending',
        file_path: data.file_path || null,
      })
      .select()
      .single();

    if (error) {
      handleSupabaseError(error, 'createWorkspaceFile');
    }

    return file as WorkspaceFile;
  }

  async updateWorkspaceFileStatus(
    fileId: string,
    status: string,
    textContent?: string
  ): Promise<WorkspaceFile> {
    const updates: Partial<WorkspaceFile> = { status };
    if (textContent !== undefined) {
      updates.text_content = textContent;
    }

    const { data, error } = await this.client
      .from('workspace_files')
      .update(updates)
      .eq('id', fileId)
      .select()
      .single();

    if (error) {
      handleSupabaseError(error, 'updateWorkspaceFileStatus');
    }

    return data as WorkspaceFile;
  }

  async getWorkspaceFiles(workspaceId: string): Promise<WorkspaceFile[]> {
    const { data, error } = await this.client
      .from('workspace_files')
      .select('*')
      .eq('workspace_id', workspaceId)
      .order('created_at', { ascending: false });

    if (error) {
      handleSupabaseError(error, 'getWorkspaceFiles');
    }

    return data || [];
  }

  async deleteWorkspaceFile(fileId: string): Promise<void> {
    const { error } = await this.client
      .from('workspace_files')
      .delete()
      .eq('id', fileId);

    if (error) {
      handleSupabaseError(error, 'deleteWorkspaceFile');
    }
  }

  // Workspace notes
  async createWorkspaceNote(
    workspaceId: string,
    data: {
      title: string;
      body: string;
    }
  ): Promise<WorkspaceNote> {
    const { data: note, error } = await this.client
      .from('workspace_notes')
      .insert({
        workspace_id: workspaceId,
        title: data.title,
        body: data.body,
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      handleSupabaseError(error, 'createWorkspaceNote');
    }

    return note as WorkspaceNote;
  }

  async updateWorkspaceNote(
    noteId: string,
    updates: { title?: string; body?: string }
  ): Promise<WorkspaceNote> {
    const updateData = {
      ...updates,
      updated_at: new Date().toISOString(),
    };

    const { data, error } = await this.client
      .from('workspace_notes')
      .update(updateData)
      .eq('id', noteId)
      .select()
      .single();

    if (error) {
      handleSupabaseError(error, 'updateWorkspaceNote');
    }

    return data as WorkspaceNote;
  }

  async getWorkspaceNotes(workspaceId: string): Promise<WorkspaceNote[]> {
    const { data, error } = await this.client
      .from('workspace_notes')
      .select('*')
      .eq('workspace_id', workspaceId)
      .order('updated_at', { ascending: false });

    if (error) {
      handleSupabaseError(error, 'getWorkspaceNotes');
    }

    return data || [];
  }

  async deleteWorkspaceNote(noteId: string): Promise<void> {
    const { error } = await this.client
      .from('workspace_notes')
      .delete()
      .eq('id', noteId);

    if (error) {
      handleSupabaseError(error, 'deleteWorkspaceNote');
    }
  }
}
