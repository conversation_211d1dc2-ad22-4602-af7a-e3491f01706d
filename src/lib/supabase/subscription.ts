import { v4 as uuidv4 } from 'uuid';
import { createClient } from '@/utils/supabase/server';
import {
  SubscriptionData,
  SubscriptionPlan,
  SUBSCRIPTION_PLANS,
  SubscriptionStatus,
  PLAN_MODEL_ACCESS,
  LLMModel,
} from './types';
import { logger } from '@/lib/logger';
import { AppError, ErrorCode } from '@/lib/error';

// Helper function to get today's date in YYYY-MM-DD format
const getTodayDate = (): string => new Date().toISOString().split('T')[0];

const log = logger.child({ module: 'subscription' });

// Helper function to handle Supabase errors consistently
const handleSupabaseError = (error: unknown, context: string) => {
  log.error(`Supabase error in ${context}:`, error);
  throw new AppError(
    `Database error in ${context}`,
    ErrorCode.DATABASE_ERROR,
    500,
    error instanceof Error ? error : undefined
  );
};

// Fetch user's subscription
export async function getUserSubscription(
  userId: string
): Promise<SubscriptionData | null> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from('subscriptions')
    .select('*')
    .eq('user_id', userId)
    .single();

  if (error) {
    if (error.code !== 'PGRST116')
      handleSupabaseError(error, 'getUserSubscription');
    return null;
  }

  return data as SubscriptionData;
}

// Create a new subscription for a user
export async function createSubscription(
  userId: string,
  plan: SubscriptionPlan = 'free',
  stripeCustomerId?: string,
  stripeSubscriptionId?: string,
  status: SubscriptionStatus = 'active'
): Promise<SubscriptionData> {
  const supabase = await createClient();
  const now = new Date().toISOString();

  const subscriptionData: SubscriptionData = {
    id: uuidv4(),
    user_id: userId,
    plan,
    status,
    created_at: now,
    updated_at: now,
    current_period_end: new Date(
      new Date().setFullYear(new Date().getFullYear() + 1)
    ).toISOString(),
    cancel_at_period_end: false,
    stripe_customer_id: stripeCustomerId || null,
    stripe_subscription_id: stripeSubscriptionId || null,
  };

  const { data, error } = await supabase
    .from('subscriptions')
    .insert(subscriptionData)
    .select()
    .single();

  if (error) handleSupabaseError(error, 'createSubscription');

  return data as SubscriptionData;
}

// Update an existing subscription
export async function updateSubscription(
  subscriptionId: string,
  updates: Partial<SubscriptionData>
): Promise<SubscriptionData | null> {
  const supabase = await createClient();

  const updateData = {
    ...updates,
    updated_at: new Date().toISOString(),
  };

  const { data, error } = await supabase
    .from('subscriptions')
    .update(updateData)
    .eq('id', subscriptionId)
    .select()
    .single();

  if (error) {
    handleSupabaseError(error, 'updateSubscription');
    return null;
  }

  return data as SubscriptionData;
}

// Get user's daily message count
export async function getUserDailyMessageCount(
  userId: string
): Promise<number> {
  const supabase = await createClient();
  const today = getTodayDate();

  const { data, error } = await supabase
    .from('user_daily_usage')
    .select('message_count')
    .eq('user_id', userId)
    .eq('date', today)
    .single();

  if (error) {
    if (error.code !== 'PGRST116')
      handleSupabaseError(error, 'getUserDailyMessageCount');
    return 0;
  }

  return data?.message_count || 0;
}

// Increment user's daily message count
export async function incrementUserDailyMessageCount(
  userId: string
): Promise<boolean> {
  const supabase = await createClient();
  const today = getTodayDate();
  const now = new Date().toISOString();

  const { data: existingRecord } = await supabase
    .from('user_daily_usage')
    .select('id, message_count')
    .eq('user_id', userId)
    .eq('date', today)
    .single();

  if (existingRecord) {
    const { error: updateError } = await supabase
      .from('user_daily_usage')
      .update({
        message_count: existingRecord.message_count + 1,
        updated_at: now,
      })
      .eq('id', existingRecord.id);

    if (updateError)
      handleSupabaseError(
        updateError,
        'incrementUserDailyMessageCount (update)'
      );
  } else {
    const { error: insertError } = await supabase
      .from('user_daily_usage')
      .insert({
        id: uuidv4(),
        user_id: userId,
        date: today,
        message_count: 1,
        created_at: now,
        updated_at: now,
      });

    if (insertError)
      handleSupabaseError(
        insertError,
        'incrementUserDailyMessageCount (insert)'
      );
  }

  return true;
}

// Check if user can send a message
export async function canUserSendMessage(
  userId: string
): Promise<{ allowed: boolean; reason?: string }> {
  let subscription = await getUserSubscription(userId);

  if (!subscription) {
    subscription = await createSubscription(userId);
  }

  // if (!['active', 'trialing'].includes(subscription.status)) {
  //   return { allowed: false, reason: 'Subscription is not active.' };
  // }

  if (subscription.plan !== 'free') {
    return { allowed: true };
  }

  const dailyCount = await getUserDailyMessageCount(userId);
  const maxDailyMessages = SUBSCRIPTION_PLANS.free.maxDailyMessages;

  if (dailyCount >= maxDailyMessages) {
    return {
      allowed: false,
      reason: `Daily limit of ${maxDailyMessages} messages reached. Upgrade your plan to continue.`,
    };
  }

  return { allowed: true };
}

export async function canUserAccessModel(
  userId: string,
  modelId: string
): Promise<boolean> {
  const supabase = await createClient();

  // Get the model details including tier
  const { data: model, error } = await supabase
    .from('llm_models')
    .select('tier')
    .eq('id', modelId)
    .single();

  if (error || !model) {
    handleSupabaseError(error, 'canUserAccessModel');
    return false;
  }

  // Get user's subscription
  const subscription = await getUserSubscription(userId);
  // if (!subscription || !['active', 'trialing'].includes(subscription.status)) {
  //   // Only allow basic models for non-subscribers or inactive subscriptions
  //   return model.tier === 'free';
  // }

  // Check if the user's plan allows access to this model tier
  const allowedTiers = PLAN_MODEL_ACCESS[subscription?.plan || 'free'];
  return allowedTiers.includes(model.tier);
}

export async function getAvailableModelsForUser(
  userId: string
): Promise<Partial<LLMModel>[]> {
  const supabase = await createClient();
  const subscription = await getUserSubscription(userId);

  // Determine which tiers the user has access to
  const accessibleTiers =
    subscription && ['active', 'trialing'].includes(subscription.status)
      ? PLAN_MODEL_ACCESS[subscription.plan]
      : ['free'];

  // Fetch models that match the accessible tiers
  const { data: models, error } = await supabase
    .from('llm_models')
    .select('id, name, display_name')
    .in('tier', accessibleTiers);

  if (error) {
    handleSupabaseError(error, 'getAvailableModelsForUser');
    return [];
  }

  return models;
}

// function to check if user has access to feature. We'll pass in feature name
export async function hasUserAccessToFeature(
  userId: string,
  feature: string
): Promise<boolean> {
  const subscription = await getUserSubscription(userId);

  if (!subscription) {
    return false;
  }

  // switch on feature
  switch (feature) {
    case 'web_search':
      return SUBSCRIPTION_PLANS[subscription.plan].allowsWebSearch;
    default:
      return false;
  }
}
