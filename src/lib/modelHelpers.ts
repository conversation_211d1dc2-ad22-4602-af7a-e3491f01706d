import { Model as DbModel, ModelTier } from '@/lib/supabase/types';

/**
 * Groups models by their tier (free, starter, premium)
 */
export const groupModelsByTier = (models: DbModel[]) => {
  const grouped: Record<ModelTier, DbModel[]> = {
    free: [],
    starter: [],
    premium: [],
  };

  models.forEach((model) => {
    const tier = model.tier as ModelTier | undefined;
    if (tier && (tier === 'free' || tier === 'starter' || tier === 'premium')) {
      grouped[tier].push(model);
    } else {
      grouped.free.push(model);
    }
  });

  return grouped;
};

/**
 * Returns a display name for a model tier
 */
export const getTierDisplayName = (tier: ModelTier) => {
  switch (tier) {
    case 'free':
      return 'Free Models';
    case 'starter':
      return 'Starter Models';
    case 'premium':
      return 'Premium Models';
    default:
      return 'Models';
  }
};

/**
 * Returns the plan requirement text for a model tier
 */
export const getTierPlanRequirement = (tier: ModelTier) => {
  switch (tier) {
    case 'free':
      return null;
    case 'starter':
      return 'Requires starter';
    case 'premium':
      return 'Requires premium';
    default:
      return null;
  }
};
