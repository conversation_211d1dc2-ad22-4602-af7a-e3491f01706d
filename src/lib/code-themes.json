{"themes": [{"id": "a11y-dark", "name": "A11y Dark", "import": "react-syntax-highlighter/dist/esm/styles/prism/a11y-dark"}, {"id": "dracula", "name": "Dracula", "import": "react-syntax-highlighter/dist/esm/styles/prism/dracula"}, {"id": "material-dark", "name": "Material Dark", "import": "react-syntax-highlighter/dist/esm/styles/prism/material-dark"}, {"id": "material-oceanic", "name": "Material Oceanic", "import": "react-syntax-highlighter/dist/esm/styles/prism/material-oceanic"}, {"id": "night-owl", "name": "Night Owl", "import": "react-syntax-highlighter/dist/esm/styles/prism/night-owl"}, {"id": "one-dark", "name": "One Dark", "import": "react-syntax-highlighter/dist/esm/styles/prism/one-dark"}, {"id": "one-light", "name": "One Light", "import": "react-syntax-highlighter/dist/esm/styles/prism/one-light"}, {"id": "synthwave84", "name": "Synthwave '84", "import": "react-syntax-highlighter/dist/esm/styles/prism/synthwave84"}, {"id": "nord", "name": "Nord", "import": "react-syntax-highlighter/dist/esm/styles/prism/nord"}, {"id": "vsc-dark-plus", "name": "VSCode Dark+", "import": "react-syntax-highlighter/dist/esm/styles/prism/vsc-dark-plus"}, {"id": "shades-of-purple", "name": "Shades of Purple", "import": "react-syntax-highlighter/dist/esm/styles/prism/shades-of-purple"}, {"id": "atom-dark", "name": "Atom Dark", "import": "react-syntax-highlighter/dist/esm/styles/prism/atom-dark"}, {"id": "okaidia", "name": "Okaidia", "import": "react-syntax-highlighter/dist/esm/styles/prism/okaidia"}, {"id": "solarized-dark-atom", "name": "Solarized Dark Atom", "import": "react-syntax-highlighter/dist/esm/styles/prism/solarized-dark-atom"}, {"id": "solarizedlight", "name": "Solarized Light", "import": "react-syntax-highlighter/dist/esm/styles/prism/solarizedlight"}, {"id": "material-light", "name": "Material Light", "import": "react-syntax-highlighter/dist/esm/styles/prism/material-light"}, {"id": "duotone-dark", "name": "Duotone Dark", "import": "react-syntax-highlighter/dist/esm/styles/prism/duotone-dark"}, {"id": "duotone-sea", "name": "Duotone Sea", "import": "react-syntax-highlighter/dist/esm/styles/prism/duotone-sea"}, {"id": "gruvbox-dark", "name": "Gruvbox Dark", "import": "react-syntax-highlighter/dist/esm/styles/prism/gruvbox-dark"}, {"id": "coldark-dark", "name": "Coldark Dark", "import": "react-syntax-highlighter/dist/esm/styles/prism/coldark-dark"}]}