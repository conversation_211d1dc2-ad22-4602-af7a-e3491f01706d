import pino, { Logger } from 'pino';

const getLogLevel = (): string => {
  const levels = process.env.LOGGING_LEVELS?.split(',').map((l) => l.trim().toLowerCase()) || [];

  if (levels.includes('none')) return 'silent';
  if (levels.includes('all')) return 'trace';
  if (levels.includes('error')) return 'error';
  if (levels.includes('warn')) return 'warn';
  if (levels.includes('info')) return 'info';
  if (levels.includes('debug')) return 'debug';

  return 'warn'; // Default log level
};

export const logger: Logger =
  process.env.NODE_ENV === 'production'
    ? pino({ level: getLogLevel() })
    : pino({
        transport: {
          target: 'pino-pretty',
          options: { colorize: true },
        },
        level: getLogLevel(),
      });
