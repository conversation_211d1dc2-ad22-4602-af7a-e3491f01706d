import { LLMModel, UserPreferences } from './supabase/types';

/**
 * Determine if a model should be visible to a user based on default visibility and user preferences
 *
 * @param modelId The ID of the model to check
 * @param isVisibleByDefault Whether the model is visible by default
 * @param userPreferences User preferences containing explicitly hidden and shown model IDs
 * @returns boolean indicating if the model should be visible
 */
export function isModelVisible(
  modelId: string,
  isVisibleByDefault: boolean | null | undefined,
  userPreferences:
    | Pick<
        UserPreferences,
        'explicitly_hidden_model_ids' | 'explicitly_shown_model_ids'
      >
    | null
    | undefined
): boolean {
  // Default to true if isVisibleByDefault is null or undefined
  const isDefaultVisible = isVisibleByDefault ?? true;

  if (!userPreferences) return isDefaultVisible;

  const explicitlyHiddenModelIds =
    (userPreferences.explicitly_hidden_model_ids as string[]) || [];
  const explicitlyShownModelIds =
    (userPreferences.explicitly_shown_model_ids as string[]) || [];

  if (isDefaultVisible) {
    // If visible by default, it's visible unless explicitly hidden
    return !explicitlyHiddenModelIds.includes(modelId);
  } else {
    // If hidden by default, it's only visible if explicitly shown
    return explicitlyShownModelIds.includes(modelId);
  }
}

/**
 * Filter an array of models based on visibility settings
 *
 * @param models Array of models to filter
 * @param userPreferences User preferences containing explicitly hidden and shown model IDs
 * @returns Filtered array of models that should be visible
 */
export function filterVisibleModels(
  models: LLMModel[],
  userPreferences:
    | Pick<
        UserPreferences,
        'explicitly_hidden_model_ids' | 'explicitly_shown_model_ids'
      >
    | null
    | undefined
): LLMModel[] {
  if (!userPreferences)
    return models.filter((model) => model.is_visible_by_default ?? true);

  return models.filter((model) =>
    isModelVisible(model.id, model.is_visible_by_default, userPreferences)
  );
}
