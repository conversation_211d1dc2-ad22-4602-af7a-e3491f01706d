name: Test Suite

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '21.1.0'
          cache: 'yarn'
      - name: Install dependencies
        run: yarn install --frozen-lockfile
      - name: Run Jest Tests
        run: yarn test
      - name: Upload test coverage
        uses: actions/upload-artifact@v4
        with:
          name: coverage
          path: coverage/

  # e2e-tests:
  #   name: E2E Tests
  #   runs-on: ubuntu-latest
  #   steps:
  #     - uses: actions/checkout@v4
  #     - name: Setup Node.js
  #       uses: actions/setup-node@v4
  #       with:
  #         node-version: '21.1.0'
  #         cache: 'yarn'
  #     - name: Install dependencies
  #       run: yarn install --frozen-lockfile
  #     - name: Install Playwright Browsers
  #       run: npx playwright install --with-deps
  #     - name: Install Supabase CLI
  #       run: npm install -g supabase
  #     - name: Start Supabase Local
  #       run: npx supabase start
  #     - name: Build Next.js App
  #       run: yarn build
  #     - name: Run Playwright tests
  #       run: yarn test:e2e
  #     - name: Upload Playwright report
  #       if: always()
  #       uses: actions/upload-artifact@v4
  #       with:
  #         name: playwright-report
  #         path: playwright/reports/
  #     - name: Stop Supabase Local
  #       if: always()
  #       run: npx supabase stop

  lint-type-check:
    name: Lint & Type Check
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '21.1.0'
          cache: 'yarn'
      - name: Install dependencies
        run: yarn install --frozen-lockfile
      - name: Run ESLint
        run: yarn lint
      - name: Run TypeScript Type Check
        run: yarn validate