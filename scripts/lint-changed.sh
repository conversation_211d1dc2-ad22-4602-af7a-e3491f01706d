#!/bin/bash

# Wrapper script to ensure correct Node version is used for linting
# This script automatically switches to Node v21 before running the lint script

set -e

# Function to check if nvm is available
check_nvm() {
    if [ -f ~/.nvm/nvm.sh ]; then
        source ~/.nvm/nvm.sh
        return 0
    elif command -v nvm >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Function to check Node version
check_node_version() {
    local current_version=$(node --version)
    local required_version="v21"
    
    if [[ $current_version == v21* ]]; then
        return 0
    else
        return 1
    fi
}

# Main execution
main() {
    echo "🔧 Checking Node.js version..."
    
    if check_node_version; then
        echo "✅ Node.js v21 is already active"
    else
        echo "🔄 Switching to Node.js v21..."
        
        if check_nvm; then
            nvm use v21
            echo "✅ Switched to Node.js v21"
        else
            echo "❌ Error: nvm not found. Please ensure Node.js v21.1.0+ is installed and active."
            echo "Current version: $(node --version)"
            echo "Required version: v21.1.0+"
            exit 1
        fi
    fi
    
    echo ""
    
    # Run the lint script with all passed arguments
    node scripts/lint-changed.js "$@"
}

# Run main function with all arguments
main "$@"
