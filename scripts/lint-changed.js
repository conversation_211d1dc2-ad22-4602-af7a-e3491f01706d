#!/usr/bin/env node

const { execSync, spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

/**
 * Script to lint only changed files
 * Usage:
 *   yarn lint:changed          # Lint unstaged changes
 *   yarn lint:staged           # Lint staged changes
 *   node scripts/lint-changed.js --help
 */

const SUPPORTED_EXTENSIONS = ['.js', '.jsx', '.ts', '.tsx', '.mjs', '.cjs'];

function showHelp() {
  console.log(`
Usage: node scripts/lint-changed.js [options]

Options:
  --staged, -s     Lint only staged files (git add)
  --all, -a        Lint all changed files (staged + unstaged)
  --help, -h       Show this help message
  --fix            Auto-fix ESLint issues where possible
  --quiet, -q      Only show errors (suppress warnings)

Examples:
  yarn lint:changed           # Lint unstaged changes
  yarn lint:staged            # Lint staged changes
  node scripts/lint-changed.js --all --fix
  node scripts/lint-changed.js --staged --quiet
`);
}

function getChangedFiles(staged = false, includeUnstaged = true) {
  try {
    let gitCommand;

    if (staged && includeUnstaged) {
      // Get both staged and unstaged files
      gitCommand = 'git diff --name-only HEAD';
    } else if (staged) {
      // Get only staged files
      gitCommand = 'git diff --cached --name-only';
    } else {
      // Get only unstaged files
      gitCommand = 'git diff --name-only';
    }

    const output = execSync(gitCommand, { encoding: 'utf8' });
    const files = output.trim().split('\n').filter(Boolean);

    // Filter for supported file types and existing files
    return files.filter(file => {
      const ext = path.extname(file);
      const exists = fs.existsSync(file);
      return exists && SUPPORTED_EXTENSIONS.includes(ext);
    });
  } catch (error) {
    console.error('Error getting changed files:', error.message);
    return [];
  }
}

function runESLint(files, options = {}) {
  if (files.length === 0) {
    console.log('✅ No changed files to lint');
    return true;
  }

  console.log(`🔍 Linting ${files.length} changed file(s):`);
  files.forEach(file => console.log(`  - ${file}`));
  console.log('');

  const eslintArgs = ['eslint'];

  // Add options
  if (options.fix) {
    eslintArgs.push('--fix');
  }

  if (options.quiet) {
    eslintArgs.push('--quiet');
  }

  // Add files
  eslintArgs.push(...files);

  try {
    // Use spawn for better output handling
    const eslint = spawn('npx', eslintArgs, {
      stdio: 'inherit',
      cwd: process.cwd()
    });

    return new Promise((resolve) => {
      eslint.on('close', (code) => {
        if (code === 0) {
          console.log('\n✅ All files passed linting!');
          resolve(true);
        } else {
          console.log('\n❌ Linting failed. Please fix the issues above.');
          resolve(false);
        }
      });
    });
  } catch (error) {
    console.error('Error running ESLint:', error.message);
    return false;
  }
}

function parseArgs() {
  const args = process.argv.slice(2);
  const options = {
    staged: false,
    all: false,
    fix: false,
    quiet: false,
    help: false
  };

  for (const arg of args) {
    switch (arg) {
      case '--staged':
      case '-s':
        options.staged = true;
        break;
      case '--all':
      case '-a':
        options.all = true;
        break;
      case '--fix':
        options.fix = true;
        break;
      case '--quiet':
      case '-q':
        options.quiet = true;
        break;
      case '--help':
      case '-h':
        options.help = true;
        break;
      default:
        console.warn(`Unknown option: ${arg}`);
    }
  }

  return options;
}

async function main() {
  const options = parseArgs();

  if (options.help) {
    showHelp();
    return;
  }

  console.log('🚀 Running ESLint on changed files...\n');

  let files;
  if (options.all) {
    files = getChangedFiles(true, true); // Both staged and unstaged
    console.log('📋 Checking all changed files (staged + unstaged)');
  } else if (options.staged) {
    files = getChangedFiles(true, false); // Only staged
    console.log('📋 Checking staged files only');
  } else {
    files = getChangedFiles(false, true); // Only unstaged
    console.log('📋 Checking unstaged files only');
  }

  const success = await runESLint(files, {
    fix: options.fix,
    quiet: options.quiet
  });

  process.exit(success ? 0 : 1);
}

// Run the script
main().catch(error => {
  console.error('Script failed:', error);
  process.exit(1);
});
