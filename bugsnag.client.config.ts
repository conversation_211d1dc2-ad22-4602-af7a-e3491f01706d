// This file configures the initialization of Bugsnag on the client.
// The config you add here will be used whenever a user loads a page in their browser.
// https://docs.bugsnag.com/platforms/javascript/

import Bugsnag from '@bugsnag/js';

// Only initialize Bugsnag in production environment
if (process.env.NODE_ENV === 'production' && process.env.NEXT_PUBLIC_BUGSNAG_API_KEY) {
  Bugsnag.start({
    apiKey: process.env.NEXT_PUBLIC_BUGSNAG_API_KEY,
    
    // Configure which release stages to report errors for
    enabledReleaseStages: ['production'],
    releaseStage: process.env.NODE_ENV,
    
    // App version for tracking releases
    appVersion: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
    
    // Configure user context
    collectUserIp: false, // Privacy-friendly setting
    
    // Configure which errors to capture
    autoDetectErrors: true,
    autoTrackSessions: true,
    
    // Configure breadcrumbs
    enabledBreadcrumbTypes: [
      'navigation',
      'request',
      'process',
      'log',
      'user',
      'state',
      'error',
      'manual'
    ],
    
    // Configure metadata
    metadata: {
      app: {
        name: 'Sabi Chat',
        type: 'web-client'
      }
    },
    
    // Configure error filtering
    onError: (event) => {
      // Filter out development-related errors
      if (event.context?.includes('localhost') || event.context?.includes('127.0.0.1')) {
        return false;
      }
      
      // Add user context if available
      const userId = localStorage.getItem('userId');
      if (userId) {
        event.setUser(userId);
      }
      
      // Add custom metadata
      event.addMetadata('client', {
        userAgent: navigator.userAgent,
        url: window.location.href,
        timestamp: new Date().toISOString()
      });
      
      return true;
    }
  });
}

export default Bugsnag;
